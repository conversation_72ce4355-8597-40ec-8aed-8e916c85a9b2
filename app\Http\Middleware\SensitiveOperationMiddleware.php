<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware untuk memverifikasi 2FA pada operasi sensitif
 * Berbeda dengan TwoFactorAuthMiddleware yang untuk login,
 * ini khusus untuk operasi kritis seperti transfer uang, ubah password, dll
 */
class SensitiveOperationMiddleware
{
    /**
     * Handle an incoming request.
     * Membutuhkan re-verification 2FA untuk operasi sensitif
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        // Skip jika user tidak login
        if (!$user) {
            return $next($request);
        }
        
        // Skip jika user tidak punya 2FA aktif
        if (!$user->has2FAEnabled()) {
            return $next($request);
        }
        
        // Cek apakah sudah ada verifikasi 2FA untuk operasi sensitif dalam 15 menit terakhir
        $lastSensitiveVerification = session('sensitive_2fa_verified_at');
        $timeLimit = now()->subMinutes(15); // Expired setelah 15 menit
        
        if ($lastSensitiveVerification && $lastSensitiveVerification > $timeLimit) {
            return $next($request);
        }
        
        // Redirect ke halaman verifikasi 2FA untuk operasi sensitif
        session(['intended_sensitive_url' => $request->fullUrl()]);
        return redirect()->route('2fa.verify.sensitive')
            ->with('warning', 'Operasi sensitif membutuhkan verifikasi 2FA tambahan.');
    }
}
