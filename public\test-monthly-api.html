<!DOCTYPE html>
<html>
<head>
    <title>Test Monthly API</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Monthly API</h1>
    <button id="testBtn">Test API Call</button>
    <div id="result"></div>

    <script>
        $('#testBtn').click(function() {
            $.ajax({
                url: '/test-api/monthly?month=07&year=2025',
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    console.log('Response received:', response);
                    console.log('Summary:', response.summary);
                    console.log('Total Income:', response.summary.total_income);
                    
                    $('#result').html(
                        '<h3>Success!</h3>' +
                        '<p>Total Income: ' + response.summary.total_income + '</p>' +
                        '<p>Total Expense: ' + response.summary.total_expense + '</p>' +
                        '<p>Net Amount: ' + response.summary.net_amount + '</p>' +
                        '<pre>' + JSON.stringify(response, null, 2) + '</pre>'
                    );
                },
                error: function(xhr) {
                    console.error('Error:', xhr);
                    $('#result').html(
                        '<h3>Error!</h3>' +
                        '<p>Status: ' + xhr.status + '</p>' +
                        '<p>Response: ' + xhr.responseText + '</p>'
                    );
                }
            });
        });
    </script>
</body>
</html>
