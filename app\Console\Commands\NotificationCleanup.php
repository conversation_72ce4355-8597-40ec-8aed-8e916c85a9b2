<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\NotificationService;

class NotificationCleanup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:cleanup {--days=30 : Number of days to keep notifications}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old read notifications and send periodic summaries';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        
        $this->info('Starting notification cleanup...');
        
        // Clean up old notifications
        $deletedCount = NotificationService::cleanup($days);
        $this->info("Deleted {$deletedCount} old notifications (older than {$days} days)");
        
        // Send summaries
        $this->info('Sending periodic summaries...');
        NotificationService::sendSummaries();
        $this->info('Summary notifications sent');
        
        $this->info('Notification cleanup completed successfully!');
        
        return 0;
    }
}
