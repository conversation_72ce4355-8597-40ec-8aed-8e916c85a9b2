<?php

namespace App\Services;

use App\Models\User;
use App\Models\Notification;
use App\Models\NotificationSetting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Create a notification for a user
     */
    public static function create($userId, array $data): Notification
    {
        return Notification::createForUser($userId, $data);
    }

    /**
     * Create notification for current user
     */
    public static function createForCurrentUser(array $data): Notification
    {
        return self::create(Auth::id(), $data);
    }

    /**
     * Send notification via multiple channels
     */
    public static function send($userId, array $data, array $channels = ['web']): Notification
    {
        $notification = self::create($userId, $data);
        $user = User::find($userId);
        $settings = NotificationSetting::where('user_id', $userId)->first();

        if (!$user || !$settings) {
            return $notification;
        }

        foreach ($channels as $channel) {
            switch ($channel) {
                case 'email':
                    self::sendEmail($user, $notification, $settings);
                    break;
                case 'sms':
                    self::sendSMS($user, $notification, $settings);
                    break;
                case 'push':
                    self::sendPush($user, $notification, $settings);
                    break;
                case 'web':
                default:
                    // Web notification is already created
                    break;
            }
        }

        return $notification;
    }

    /**
     * Send email notification
     */
    private static function sendEmail(User $user, Notification $notification, NotificationSetting $settings)
    {
        if (!$settings->email_enabled) {
            return;
        }

        // Check specific email settings based on notification type
        $emailEnabled = match($notification->type) {
            'login' => $settings->email_login_alerts,
            'security' => $settings->email_security_alerts,
            'transaction' => $settings->email_transaction_alerts,
            'budget' => $settings->email_budget_alerts,
            default => true,
        };

        if (!$emailEnabled) {
            return;
        }

        // Check quiet hours
        if (self::isQuietHours($settings)) {
            return;
        }

        try {
            // Here you would send actual email
            // Mail::to($user->email)->send(new NotificationMail($notification));
            Log::info("Email notification sent to {$user->email}: {$notification->title}");
        } catch (\Exception $e) {
            Log::error("Failed to send email notification: " . $e->getMessage());
        }
    }

    /**
     * Send SMS notification
     */
    private static function sendSMS(User $user, Notification $notification, NotificationSetting $settings)
    {
        if (!$settings->sms_enabled || !$settings->sms_phone) {
            return;
        }

        // Only send critical SMS
        if (!$notification->is_important && $notification->type !== 'error') {
            return;
        }

        try {
            // Here you would integrate with SMS service (Twilio, etc.)
            Log::info("SMS notification sent to {$settings->sms_phone}: {$notification->title}");
        } catch (\Exception $e) {
            Log::error("Failed to send SMS notification: " . $e->getMessage());
        }
    }

    /**
     * Send push notification
     */
    private static function sendPush(User $user, Notification $notification, NotificationSetting $settings)
    {
        if (!$settings->push_enabled) {
            return;
        }

        try {
            // Here you would integrate with push notification service (FCM, etc.)
            Log::info("Push notification sent to user {$user->id}: {$notification->title}");
        } catch (\Exception $e) {
            Log::error("Failed to send push notification: " . $e->getMessage());
        }
    }

    /**
     * Check if current time is within quiet hours
     */
    private static function isQuietHours(NotificationSetting $settings): bool
    {
        $now = now();
        $quietStart = $now->copy()->setTimeFromTimeString($settings->quiet_hours_start);
        $quietEnd = $now->copy()->setTimeFromTimeString($settings->quiet_hours_end);

        // Handle overnight quiet hours (e.g., 22:00 to 08:00)
        if ($quietStart > $quietEnd) {
            return $now >= $quietStart || $now <= $quietEnd;
        }

        return $now >= $quietStart && $now <= $quietEnd;
    }

    /**
     * Create specific notification types
     */
    public static function createLoginAlert($userId, array $data = []): Notification
    {
        return self::send($userId, array_merge([
            'type' => 'info',
            'title' => 'Login Baru Terdeteksi',
            'message' => 'Akun Anda telah login dari perangkat baru.',
            'icon' => 'fas fa-sign-in-alt',
            'channel' => 'web',
        ], $data), ['web', 'email']);
    }

    public static function createSecurityAlert($userId, array $data = []): Notification
    {
        return self::send($userId, array_merge([
            'type' => 'warning',
            'title' => 'Peringatan Keamanan',
            'message' => 'Aktivitas mencurigakan terdeteksi pada akun Anda.',
            'is_important' => true,
            'icon' => 'fas fa-shield-alt',
            'channel' => 'web',
        ], $data), ['web', 'email', 'sms']);
    }

    public static function createTransactionAlert($userId, array $data = []): Notification
    {
        return self::send($userId, array_merge([
            'type' => 'success',
            'title' => 'Transaksi Berhasil',
            'message' => 'Transaksi Anda telah berhasil diproses.',
            'icon' => 'fas fa-credit-card',
            'channel' => 'web',
        ], $data), ['web']);
    }

    public static function createBudgetAlert($userId, array $data = []): Notification
    {
        return self::send($userId, array_merge([
            'type' => 'warning',
            'title' => 'Peringatan Budget',
            'message' => 'Budget Anda hampir mencapai batas.',
            'is_important' => true,
            'icon' => 'fas fa-exclamation-triangle',
            'channel' => 'web',
        ], $data), ['web', 'email']);
    }

    public static function create2FAAlert($userId, array $data = []): Notification
    {
        return self::send($userId, array_merge([
            'type' => 'success',
            'title' => '2FA Diaktifkan',
            'message' => 'Two-Factor Authentication berhasil diaktifkan.',
            'icon' => 'fas fa-shield-check',
            'action_url' => '/settings/security',
            'channel' => 'web',
        ], $data), ['web', 'email']);
    }

    public static function createBackupAlert($userId, array $data = []): Notification
    {
        return self::send($userId, array_merge([
            'type' => 'info',
            'title' => 'Backup Selesai',
            'message' => 'Backup data Anda telah selesai.',
            'icon' => 'fas fa-database',
            'action_url' => '/settings/backup',
            'channel' => 'web',
        ], $data), ['web']);
    }

    /**
     * Mark notification as read
     */
    public static function markAsRead($notificationId): bool
    {
        $notification = Notification::find($notificationId);
        if ($notification && $notification->user_id === Auth::id()) {
            $notification->markAsRead();
            return true;
        }
        return false;
    }

    /**
     * Delete notification
     */
    public static function delete($notificationId): bool
    {
        $notification = Notification::find($notificationId);
        if ($notification && $notification->user_id === Auth::id()) {
            $notification->delete();
            return true;
        }
        return false;
    }

    /**
     * Get user notification statistics
     */
    public static function getStats($userId): array
    {
        $user = User::find($userId);
        return $user ? $user->getNotificationCounts() : [];
    }

    /**
     * Clean up old notifications
     */
    public static function cleanup($days = 30): int
    {
        return Notification::where('created_at', '<', now()->subDays($days))
            ->whereNotNull('read_at')
            ->delete();
    }

    /**
     * Send daily/weekly/monthly summaries
     */
    public static function sendSummaries()
    {
        $users = User::whereHas('notificationSettings', function ($query) {
            $query->where('email_enabled', true)
                  ->where(function ($q) {
                      $q->where('email_weekly_summary', true)
                        ->orWhere('email_monthly_report', true);
                  });
        })->get();

        foreach ($users as $user) {
            $settings = $user->notificationSettings;
            
            // Send weekly summary if enabled and it's the right day
            if ($settings->email_weekly_summary && now()->isMonday()) {
                self::sendWeeklySummary($user);
            }
            
            // Send monthly report if enabled and it's the first day of month
            if ($settings->email_monthly_report && now()->day === 1) {
                self::sendMonthlySummary($user);
            }
        }
    }

    private static function sendWeeklySummary(User $user)
    {
        // Implementation for weekly summary
        self::create($user->id, [
            'type' => 'info',
            'title' => 'Ringkasan Mingguan',
            'message' => 'Ringkasan aktivitas keuangan minggu ini tersedia.',
            'action_url' => '/reports/weekly',
            'icon' => 'fas fa-chart-line',
        ]);
    }

    private static function sendMonthlySummary(User $user)
    {
        // Implementation for monthly summary
        self::create($user->id, [
            'type' => 'info',
            'title' => 'Laporan Bulanan',
            'message' => 'Laporan keuangan bulanan Anda telah siap.',
            'action_url' => '/reports/monthly',
            'icon' => 'fas fa-chart-bar',
        ]);
    }
}
