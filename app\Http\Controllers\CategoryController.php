<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $type = $request->get('type', 'income'); // Default to income
        
        return view('categories.shared.create', compact('type'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:income,expense',
            'icon' => 'nullable|string|max:50',
            'color' => 'required|string|max:7',
            'description' => 'nullable|string|max:500',
            'budget_limit' => 'nullable|numeric|min:0',
            'budget_period' => 'nullable|in:daily,weekly,monthly,yearly'
        ]);

        $category = Category::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'type' => $request->type,
            'icon' => $request->icon ?: 'fas fa-folder',
            'color' => $request->color,
            'description' => $request->description,
            'budget_limit' => $request->budget_limit,
            'budget_period' => $request->budget_period ?: 'monthly',
            'is_active' => true,
            'sort_order' => Category::forUser(Auth::id())->ofType($request->type)->max('sort_order') + 1
        ]);

        $redirectRoute = $request->type === 'income' ? 'categories.income.index' : 'categories.expense.index';
        
        return redirect()->route($redirectRoute)
                        ->with('success', 'Kategori berhasil ditambahkan!');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $category = Category::forUser(Auth::id())->findOrFail($id);
        
        // Tentukan view berdasarkan tipe kategori
        $view = $category->type === 'income' ? 'categories.income.edit' : 'categories.expense.edit';
        
        return view($view, compact('category'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $category = Category::forUser(Auth::id())->findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'icon' => 'nullable|string|max:50',
            'color' => 'required|string|max:7',
            'description' => 'nullable|string|max:500',
            'budget_limit' => 'nullable|numeric|min:0',
            'budget_period' => 'nullable|in:daily,weekly,monthly,yearly'
        ]);

        $category->update([
            'name' => $request->name,
            'icon' => $request->icon ?: 'fas fa-folder',
            'color' => $request->color,
            'description' => $request->description,
            'budget_limit' => $request->budget_limit,
            'budget_period' => $request->budget_period ?: 'monthly',
        ]);

        $redirectRoute = $category->type === 'income' ? 'categories.income.index' : 'categories.expense.index';
        
        return redirect()->route($redirectRoute)
                        ->with('success', 'Kategori berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $category = Category::forUser(Auth::id())->findOrFail($id);
        
        // Check if category has transactions
        $transactionCount = $category->transactions()->count();
        if ($transactionCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Kategori tidak dapat dihapus karena masih memiliki {$transactionCount} transaksi."
            ], 400);
        }
        
        $type = $category->type;
        $category->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Kategori berhasil dihapus!',
            'redirect' => $type === 'income' ? route('categories.income.index') : route('categories.expense.index')
        ]);
    }

    /**
     * Display income categories page.
     */
    public function income()
    {
        // Ambil semua kategori (aktif dan nonaktif) untuk ditampilkan
        $categories = Category::forUser(Auth::id())
                             ->ofType('income')
                             ->orderBy('is_active', 'desc') // Aktif di atas, nonaktif di bawah
                             ->orderBy('sort_order')
                             ->orderBy('name')
                             ->get();

        $stats = [
            'total_categories' => Category::forUser(Auth::id())->ofType('income')->count(),
            'active_categories' => Category::forUser(Auth::id())->ofType('income')->where('is_active', true)->count(),
            'inactive_categories' => Category::forUser(Auth::id())->ofType('income')->where('is_active', false)->count()
        ];

        return view('categories.income.index', compact('categories', 'stats'));
    }

    /**
     * Show the form for creating a new income category.
     */
    public function incomeCreate()
    {
        return view('categories.income.create');
    }

    /**
     * Display expense categories page.
     */
    public function expense(Request $request)
    {
        // Build query for expense categories
        $query = Category::forUser(Auth::id())
                        ->ofType('expense')
                        ->with(['transactions' => function($query) {
                            $query->select('id', 'category_id', 'amount');
                        }]);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sort = $request->get('sort', 'name_asc');
        switch ($sort) {
            case 'name_desc':
                $query->orderBy('name', 'desc');
                break;
            case 'transactions_desc':
                $query->withCount('transactions')->orderBy('transactions_count', 'desc');
                break;
            case 'amount_desc':
                $query->with('transactions')->get()->sortByDesc(function($category) {
                    return $category->transactions->sum('amount');
                });
                break;
            case 'created_desc':
                $query->orderBy('created_at', 'desc');
                break;
            case 'name_asc':
            default:
                $query->orderBy('name', 'asc');
                break;
        }

        // Get paginated results
        $categories = $query->orderBy('is_active', 'desc')
                           ->orderBy('sort_order')
                           ->paginate(12)
                           ->appends($request->query());

        // Calculate statistics
        $totalCategories = Category::forUser(Auth::id())->ofType('expense')->count();
        $activeCategories = Category::forUser(Auth::id())->ofType('expense')->where('is_active', true)->count();
        $inactiveCategories = $totalCategories - $activeCategories;

        $stats = [
            'total_categories' => $totalCategories,
            'active_categories' => $activeCategories,
            'inactive_categories' => $inactiveCategories
        ];

        return view('categories.expense.index', compact('categories', 'stats'));
    }

    /**
     * Show the form for creating a new expense category.
     */
    public function expenseCreate()
    {
        return view('categories.expense.create');
    }

    /**
     * Toggle category active status.
     */
    public function toggleStatus(Request $request, Category $category)
    {
        // Ensure user owns this category
        if ($category->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak memiliki akses untuk mengubah kategori ini.'
            ], 403);
        }

        $request->validate([
            'is_active' => 'required|boolean'
        ]);

        $category->update([
            'is_active' => $request->is_active
        ]);

        $statusText = $request->is_active ? 'diaktifkan' : 'dinonaktifkan';

        return response()->json([
            'success' => true,
            'message' => "Kategori '{$category->name}' berhasil {$statusText}."
        ]);
    }

    /**
     * API: Get list of categories
     */
    public function apiList()
    {
        try {
            $categories = Category::where('user_id', Auth::id())
                                ->where('is_active', true)
                                ->orderBy('type')
                                ->orderBy('name')
                                ->get(['id', 'name', 'type', 'icon', 'color']);

            return response()->json($categories);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load categories'
            ], 500);
        }
    }
}
