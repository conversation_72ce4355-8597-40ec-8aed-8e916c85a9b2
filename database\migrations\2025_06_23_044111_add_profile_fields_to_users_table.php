<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Personal Information
            $table->string('username')->unique()->after('name');
            $table->string('full_name')->nullable()->after('username');
            $table->string('phone')->nullable()->after('email');
            $table->enum('gender', ['L', 'P'])->nullable()->after('phone');
            $table->date('birth_date')->nullable()->after('gender');
            $table->text('address')->nullable()->after('birth_date');
            $table->string('city')->nullable()->after('address');
            $table->string('postal_code', 10)->nullable()->after('city');
            $table->string('country')->default('Indonesia')->after('postal_code');
            
            // Profile Photo
            $table->string('avatar')->nullable()->after('country');
            
            // Bio/Description
            $table->text('bio')->nullable()->after('avatar');
            
            // Security Fields
            $table->integer('login_attempts')->default(0)->after('remember_token');
            $table->timestamp('locked_until')->nullable()->after('login_attempts');
            $table->boolean('is_active')->default(true)->after('locked_until');
            $table->string('verification_token')->nullable()->after('is_active');
            $table->timestamp('email_verified_at')->nullable()->change();
            
            // Activity Tracking
            $table->timestamp('last_login_at')->nullable()->after('verification_token');
            $table->string('last_login_ip')->nullable()->after('last_login_at');
            
            // Settings
            $table->string('timezone')->default('Asia/Jakarta')->after('last_login_ip');
            $table->string('language')->default('id')->after('timezone');
            $table->enum('theme', ['light', 'solarized-dark', 'synth-wave'])->default('light')->after('language');
        });
    }

    /**
     * Reverse the migrations.
     */    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'username', 'full_name', 'phone', 'gender', 'birth_date', 
                'address', 'city', 'postal_code', 'country', 'avatar', 'bio',
                'login_attempts', 'locked_until', 'is_active', 'verification_token',
                'last_login_at', 'last_login_ip', 'timezone', 'language', 'theme',
                'currency', 'date_format', 'time_format', 'notification_enabled',
                'notification_settings', 'preferences', 'two_factor_enabled',
                'two_factor_secret', 'two_factor_recovery_codes', 'two_factor_confirmed_at',
                'backup_codes', 'security_settings', 'password_changed_at',
                'password_expires_at', 'last_backup_at'
            ]);
        });
    }
};
