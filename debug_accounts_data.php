<?php
require_once 'vendor/autoload.php';
require_once 'bootstrap/app.php';

use App\Models\Account;
use App\Models\User;

echo "=== CHECKING ACCOUNTS DATA ===\n";

// Get all users
$users = User::all();
echo "Total users: " . $users->count() . "\n\n";

foreach ($users as $user) {
    echo "User ID: {$user->id} - {$user->name} ({$user->email})\n";
    
    $accounts = Account::where('user_id', $user->id)->get();
    echo "  Accounts: " . $accounts->count() . "\n";
    
    foreach ($accounts as $account) {
        echo "    - {$account->name}: Rp " . number_format($account->balance, 0, ',', '.') . " (Active: " . ($account->is_active ? 'Yes' : 'No') . ")\n";
    }
    echo "\n";
}

echo "=== CHECKING SPECIFIC USER (ID=2) ===\n";
$user2 = User::find(2);
if ($user2) {
    echo "User: {$user2->name} ({$user2->email})\n";
    $accounts = Account::where('user_id', 2)->get();
    $totalBalance = $accounts->sum('balance');
    
    echo "Total Balance: Rp " . number_format($totalBalance, 0, ',', '.') . "\n";
    echo "Accounts:\n";
    
    foreach ($accounts as $account) {
        echo "  - {$account->name}: Rp " . number_format($account->balance, 0, ',', '.') . " (Active: " . ($account->is_active ? 'Yes' : 'No') . ")\n";
    }
} else {
    echo "User with ID 2 not found!\n";
}
?>
