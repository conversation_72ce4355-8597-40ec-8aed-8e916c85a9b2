@extends('layouts.dashboard')

@section('title', 'Monitor Saldo <PERSON>kun')

@section('page-title', '📊 Monitor Saldo Akun')

@push('styles')
<style>
    /* Dashboard container styling sesuai referensi */
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    /* Theme support untuk breadcrumb */
    body[data-theme="solarized-dark"] .breadcrumb-item a {
        color: #b58900 !important;
    }
    
    body[data-theme="solarized-dark"] .breadcrumb-item.active {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item a {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item.active {
        color: #ffffff !important;
    }
    
    /* Theme support untuk h2 */
    body[data-theme="solarized-dark"] h2 {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] h2 {
        color: #ffffff !important;
    }
    
    /* Theme support untuk small text */
    body[data-theme="solarized-dark"] .text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="synth-wave"] .text-muted {
        color: #cccccc !important;
    }

    /* Monitor cards styling */
    .monitor-card {
        background: #f8fffe;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 20px;
        position: relative;
    }
    
    .monitor-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
    }
    
    body[data-theme="solarized-dark"] .monitor-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
    }
    
    body[data-theme="synth-wave"] .monitor-card {
        background: rgba(0, 128, 255, 0.1);
        border: 1px solid #0080ff;
        color: #ffffff;
    }
    
    /* Summary cards dengan gradients */
    .summary-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 25px;
        color: white;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }
    
    .summary-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(30px, -30px);
    }
    
    .summary-card.total-balance {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .summary-card.positive-balance {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }
    
    .summary-card.negative-balance {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    }
    
    .summary-card.account-count {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    /* Chart containers */
    .chart-container {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        position: relative;
        height: 400px;
    }
    
    body[data-theme="solarized-dark"] .chart-container {
        background: rgba(253, 246, 227, 0.9);
    }
    
    body[data-theme="synth-wave"] .chart-container {
        background: rgba(0, 20, 40, 0.9);
        border: 1px solid #0080ff;
        color: #ffffff;
    }
    
    /* Account balance bars */
    .balance-bar {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
        overflow: hidden;
        margin-bottom: 10px;
    }
    
    .balance-progress {
        height: 100%;
        border-radius: 4px;
        transition: width 0.5s ease;
    }
    
    .balance-positive { background: linear-gradient(90deg, #28a745, #34ce57); }
    .balance-negative { background: linear-gradient(90deg, #dc3545, #e74c3c); }
    .balance-zero { background: #6c757d; }
    
    /* Account mini cards */
    .account-mini-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 15px;
        margin-bottom: 15px;
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .account-mini-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    body[data-theme="synth-wave"] .account-mini-card {
        background: rgba(0, 20, 40, 0.8);
        color: #ffffff;
    }
    
    /* Filter controls */
    .filter-controls {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    body[data-theme="solarized-dark"] .filter-controls {
        background: rgba(253, 246, 227, 0.9);
    }
    
    body[data-theme="synth-wave"] .filter-controls {
        background: rgba(0, 20, 40, 0.9);
        border: 1px solid #0080ff;
        color: #ffffff;
    }
    
    /* Status indicators */
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .status-active { background: #28a745; }
    .status-inactive { background: #6c757d; }
    .status-warning { background: #ffc107; }
    .status-danger { background: #dc3545; }
    
    /* Refresh button */
    .btn-refresh {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        border-radius: 15px;
        padding: 12px 30px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
    }
    
    .btn-refresh:hover {
        transform: translateY(-2px) rotate(180deg);
        box-shadow: 0 12px 30px rgba(0, 123, 255, 0.4);
        color: white;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .chart-container {
            height: 300px;
        }
        
        .summary-card {
            margin-bottom: 15px;
        }
    }
    
    /* Balance trend indicators */
    .trend-indicator {
        font-size: 1.2em;
        margin-left: 10px;
    }
    
    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    .trend-stable { color: #6c757d; }
    
    /* Loading states */
    .loading-placeholder {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading-wave 2s infinite;
        border-radius: 8px;
        height: 20px;
        margin-bottom: 10px;
    }
    
    @keyframes loading-wave {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    /* Account type colors */
    .type-bank { border-left-color: #007bff; }
    .type-cash { border-left-color: #28a745; }
    .type-e_wallet { border-left-color: #17a2b8; }
    .type-investment { border-left-color: #ffc107; }
    .type-credit_card { border-left-color: #dc3545; }
    .type-savings { border-left-color: #6f42c1; }
    .type-other { border-left-color: #6c757d; }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('dashboard') }}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="#" class="text-decoration-none">
                    <i class="fas fa-wallet me-1"></i>Akun & Dompet
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-chart-line me-1"></i>Monitor Saldo
            </li>
        </ol>
    </nav>

    <!-- Header dengan refresh button -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-chart-line text-primary me-2"></i>
            Monitor Saldo Akun
        </h2>
        <button onclick="refreshData()" class="btn btn-refresh" title="Refresh Data">
            <i class="fas fa-sync-alt me-2"></i>Refresh
        </button>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="summary-card total-balance">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-2 opacity-75">Total Saldo</h6>
                        <h3 class="mb-0 fw-bold">{{ $summary['total_balance'] ?? 'Rp 0' }}</h3>
                        <span class="trend-indicator trend-{{ $summary['balance_trend'] ?? 'stable' }}">
                            <i class="fas fa-arrow-{{ $summary['balance_trend'] === 'up' ? 'up' : ($summary['balance_trend'] === 'down' ? 'down' : 'right') }}"></i>
                        </span>
                    </div>
                    <div class="text-end opacity-75">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="summary-card positive-balance">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-2 opacity-75">Saldo Positif</h6>
                        <h3 class="mb-0 fw-bold">{{ $summary['positive_balance'] ?? 'Rp 0' }}</h3>
                        <small class="opacity-75">{{ $summary['positive_accounts'] ?? 0 }} akun</small>
                    </div>
                    <div class="text-end opacity-75">
                        <i class="fas fa-arrow-up fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="summary-card negative-balance">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-2 opacity-75">Saldo Negatif</h6>
                        <h3 class="mb-0 fw-bold">{{ $summary['negative_balance'] ?? 'Rp 0' }}</h3>
                        <small class="opacity-75">{{ $summary['negative_accounts'] ?? 0 }} akun</small>
                    </div>
                    <div class="text-end opacity-75">
                        <i class="fas fa-arrow-down fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="summary-card account-count">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-2 opacity-75">Total Akun</h6>
                        <h3 class="mb-0 fw-bold">{{ $summary['total_accounts'] ?? 0 }}</h3>
                        <small class="opacity-75">{{ $summary['active_accounts'] ?? 0 }} aktif</small>
                    </div>
                    <div class="text-end opacity-75">
                        <i class="fas fa-wallet fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="filter-controls">
        <form method="GET" action="{{ route('accounts.monitor.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="type" class="form-label small">Jenis Akun</label>
                <select name="type" id="type" class="form-select form-select-sm">
                    <option value="all" {{ request('type') === 'all' ? 'selected' : '' }}>Semua Jenis</option>
                    <option value="bank" {{ request('type') === 'bank' ? 'selected' : '' }}>Bank</option>
                    <option value="cash" {{ request('type') === 'cash' ? 'selected' : '' }}>Tunai</option>
                    <option value="e_wallet" {{ request('type') === 'e_wallet' ? 'selected' : '' }}>E-Wallet</option>
                    <option value="investment" {{ request('type') === 'investment' ? 'selected' : '' }}>Investasi</option>
                    <option value="credit_card" {{ request('type') === 'credit_card' ? 'selected' : '' }}>Kartu Kredit</option>
                    <option value="savings" {{ request('type') === 'savings' ? 'selected' : '' }}>Tabungan</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label small">Status</label>
                <select name="status" id="status" class="form-select form-select-sm">
                    <option value="all" {{ request('status') === 'all' ? 'selected' : '' }}>Semua Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Aktif</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Nonaktif</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="sort" class="form-label small">Urutkan</label>
                <select name="sort" id="sort" class="form-select form-select-sm">
                    <option value="balance_desc" {{ request('sort') === 'balance_desc' ? 'selected' : '' }}>Saldo Tertinggi</option>
                    <option value="balance_asc" {{ request('sort') === 'balance_asc' ? 'selected' : '' }}>Saldo Terendah</option>
                    <option value="name_asc" {{ request('sort') === 'name_asc' ? 'selected' : '' }}>Nama A-Z</option>
                    <option value="type_asc" {{ request('sort') === 'type_asc' ? 'selected' : '' }}>Jenis</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label small text-white">.</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-filter me-1"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div class="row">
        <!-- Charts Column -->
        <div class="col-lg-8">
            <!-- Balance Distribution Chart -->
            <div class="chart-container mb-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-chart-pie text-primary me-2"></i>
                    Distribusi Saldo per Jenis Akun
                </h5>
                <canvas id="balanceChart" width="400" height="200"></canvas>
            </div>
            
            <!-- Balance Trend Chart -->
            <div class="chart-container">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    Tren Saldo 30 Hari Terakhir
                </h5>
                <canvas id="trendChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Account List Column -->
        <div class="col-lg-4">
            <div class="monitor-card">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-list text-primary me-2"></i>
                        Daftar Akun
                    </h5>
                    
                    @forelse($allAccounts ?? [] as $account)
                        <div class="account-mini-card type-{{ $account->type }}" 
                             onclick="showAccountDetail({{ $account->id }})" 
                             data-account="{{ json_encode($account) }}">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="color: {{ $account->color ?? '#007bff' }};">
                                        <i class="{{ $account->icon ?? 'fas fa-wallet' }}"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">{{ $account->name }}</h6>
                                        <small class="text-muted">
                                            <span class="status-indicator status-{{ $account->is_active ? 'active' : 'inactive' }}"></span>
                                            @switch($account->type)
                                                @case('bank') Bank @break
                                                @case('cash') Tunai @break
                                                @case('e_wallet') E-Wallet @break
                                                @case('investment') Investasi @break
                                                @case('credit_card') Kartu Kredit @break
                                                @case('savings') Tabungan @break
                                                @default Lainnya
                                            @endswitch
                                        </small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold {{ 
                                        $account->current_balance > 0 ? 'text-success' : 
                                        ($account->current_balance < 0 ? 'text-danger' : 'text-muted') 
                                    }}">
                                        Rp {{ number_format($account->current_balance, 0, ',', '.') }}
                                    </div>
                                    @if($account->type === 'credit_card' && $account->credit_limit)
                                        <small class="text-muted">
                                            Limit: Rp {{ number_format($account->credit_limit, 0, ',', '.') }}
                                        </small>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- Balance Progress Bar -->
                            @if($account->type === 'credit_card' && $account->credit_limit)
                                <div class="balance-bar">
                                    <div class="balance-progress balance-{{ $account->current_balance > 0 ? 'positive' : ($account->current_balance < 0 ? 'negative' : 'zero') }}" 
                                         style="width: {{ min(abs($account->current_balance) / $account->credit_limit * 100, 100) }}%"></div>
                                </div>
                                <small class="text-muted">
                                    {{ number_format(abs($account->current_balance) / $account->credit_limit * 100, 1) }}% dari limit
                                </small>
                            @else
                                @php
                                    $maxBalance = ($allAccounts->max('current_balance') ?: 1);
                                    $percentage = $maxBalance > 0 ? (abs($account->current_balance) / $maxBalance * 100) : 0;
                                @endphp
                                <div class="balance-bar">
                                    <div class="balance-progress balance-{{ $account->current_balance > 0 ? 'positive' : ($account->current_balance < 0 ? 'negative' : 'zero') }}" 
                                         style="width: {{ $percentage }}%"></div>
                                </div>
                            @endif
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fas fa-wallet text-muted" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-2">Belum ada akun</h6>
                            <p class="text-muted small">Tambahkan akun untuk memulai monitoring</p>
                            <a href="{{ route('accounts.create.index') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-1"></i>Tambah Akun
                            </a>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="monitor-card mt-4">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-bolt text-primary me-2"></i>
                        Aksi Cepat
                    </h5>
                    
                    <div class="d-grid gap-2">
                        <a href="{{ route('accounts.create.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>Tambah Akun Baru
                        </a>
                        <a href="{{ route('accounts.list.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i>Kelola Akun
                        </a>
                        <a href="{{ route('transactions.create') }}" class="btn btn-outline-success">
                            <i class="fas fa-exchange-alt me-2"></i>Tambah Transaksi
                        </a>
                        <button onclick="exportData()" class="btn btn-outline-info">
                            <i class="fas fa-download me-2"></i>Export Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Detail Modal -->
<div class="modal fade" id="accountDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-wallet me-2"></i>
                    Detail Akun
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="accountDetailContent">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a href="#" id="editAccountBtn" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Edit Akun
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let balanceChart = null;
let trendChart = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    
    // Auto refresh every 5 minutes
    setInterval(refreshData, 300000);
});

function initializeCharts() {
    initializeBalanceChart();
    initializeTrendChart();
}

function initializeBalanceChart() {
    const ctx = document.getElementById('balanceChart').getContext('2d');
    
    const data = {!! json_encode($chartData['balance'] ?? []) !!};
    
    balanceChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.labels || ['No Data'],
            datasets: [{
                data: data.data || [0],
                backgroundColor: data.backgroundColor || ['#007bff', '#28a745', '#17a2b8', '#ffc107', '#dc3545', '#6f42c1'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                            return context.label + ': Rp ' + value.toLocaleString('id-ID') + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
}

function initializeTrendChart() {
    const ctx = document.getElementById('trendChart').getContext('2d');
    
    const data = {!! json_encode($chartData['trend'] ?? []) !!};
    
    trendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels || [],
            datasets: [{
                label: 'Total Saldo',
                data: data.data || [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#007bff',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Saldo: Rp ' + context.parsed.y.toLocaleString('id-ID');
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + value.toLocaleString('id-ID');
                        }
                    }
                }
            }
        }
    });
}

function refreshData() {
    const refreshBtn = document.querySelector('.btn-refresh i');
    refreshBtn.style.animation = 'spin 1s linear infinite';
    
    // Show loading state
    Swal.fire({
        title: 'Memperbarui Data...',
        text: 'Sedang mengambil data terbaru',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    // Reload the page to get fresh data
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function showAccountDetail(accountId) {
    const accountCards = document.querySelectorAll('.account-mini-card');
    let accountData = null;
    
    accountCards.forEach(card => {
        const data = JSON.parse(card.dataset.account);
        if (data.id === accountId) {
            accountData = data;
        }
    });
    
    if (!accountData) return;
    
    const modalContent = document.getElementById('accountDetailContent');
    const editBtn = document.getElementById('editAccountBtn');
    
    editBtn.href = `/accounts/${accountId}/edit`;
    
    modalContent.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="fw-bold mb-3">Informasi Akun</h6>
                <table class="table table-borderless">
                    <tr>
                        <td class="text-muted">Nama:</td>
                        <td class="fw-bold">${accountData.name}</td>
                    </tr>
                    <tr>
                        <td class="text-muted">Jenis:</td>
                        <td>
                            <span class="badge bg-primary">${getAccountTypeLabel(accountData.type)}</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-muted">Status:</td>
                        <td>
                            <span class="badge bg-${accountData.is_active ? 'success' : 'secondary'}">
                                ${accountData.is_active ? 'Aktif' : 'Nonaktif'}
                            </span>
                        </td>
                    </tr>
                    ${accountData.bank_name ? `
                    <tr>
                        <td class="text-muted">Bank:</td>
                        <td>${accountData.bank_name}</td>
                    </tr>
                    ` : ''}
                    ${accountData.account_number ? `
                    <tr>
                        <td class="text-muted">Nomor:</td>
                        <td>${accountData.account_number}</td>
                    </tr>
                    ` : ''}
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="fw-bold mb-3">Saldo & Transaksi</h6>
                <table class="table table-borderless">
                    <tr>
                        <td class="text-muted">Saldo Awal:</td>
                        <td class="fw-bold">Rp ${parseFloat(accountData.initial_balance || 0).toLocaleString('id-ID')}</td>
                    </tr>
                    <tr>
                        <td class="text-muted">Saldo Saat Ini:</td>
                        <td class="fw-bold ${accountData.current_balance > 0 ? 'text-success' : (accountData.current_balance < 0 ? 'text-danger' : 'text-muted')}">
                            Rp ${parseFloat(accountData.current_balance || 0).toLocaleString('id-ID')}
                        </td>
                    </tr>
                    ${accountData.credit_limit ? `
                    <tr>
                        <td class="text-muted">Limit Kredit:</td>
                        <td class="fw-bold">Rp ${parseFloat(accountData.credit_limit).toLocaleString('id-ID')}</td>
                    </tr>
                    ` : ''}
                    <tr>
                        <td class="text-muted">Transaksi:</td>
                        <td>${accountData.transactions_count || 0} transaksi</td>
                    </tr>
                </table>
            </div>
        </div>
        ${accountData.description ? `
        <div class="mt-3">
            <h6 class="fw-bold">Deskripsi</h6>
            <p class="text-muted">${accountData.description}</p>
        </div>
        ` : ''}
    `;
    
    new bootstrap.Modal(document.getElementById('accountDetailModal')).show();
}

function getAccountTypeLabel(type) {
    const labels = {
        'bank': 'Bank',
        'cash': 'Tunai', 
        'e_wallet': 'E-Wallet',
        'investment': 'Investasi',
        'credit_card': 'Kartu Kredit',
        'savings': 'Tabungan',
        'other': 'Lainnya'
    };
    return labels[type] || 'Lainnya';
}

function exportData() {
    Swal.fire({
        title: 'Export Data Monitoring',
        text: 'Pilih format export yang diinginkan',
        showCancelButton: true,
        showDenyButton: true,
        confirmButtonText: 'PDF',
        denyButtonText: 'Excel',
        cancelButtonText: 'Batal',
        icon: 'question'
    }).then((result) => {
        if (result.isConfirmed) {
            window.open('/accounts/monitor/export/pdf', '_blank');
        } else if (result.isDenied) {
            window.open('/accounts/monitor/export/excel', '_blank');
        }
    });
}

// Add CSS for spin animation
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
@endpush
