<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use App\Models\SecurityLog;
use App\Models\User;

class AdvancedSecurityMiddleware
{
    /**
     * Handle an incoming request with comprehensive security checks
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip advanced security checks for API routes and safe operations
        if ($request->is('*/api/*') || $request->is('api/*') || 
            $request->is('reports/api/*') || $request->is('emergency/*') ||
            $request->is('test-reports-*') ||
            $request->is('categories/*') || $request->is('categories')) {
            return $next($request);
        }
        
        // Get client IP (considering proxies)
        $clientIp = $this->getClientIp($request);
        
        // 1. Rate Limiting (DDoS Protection)
        $this->handleRateLimit($request, $clientIp);
        
        // 2. IP-based Security Checks
        $this->checkSuspiciousIp($clientIp);
        
        // 3. User Agent Validation
        $this->validateUserAgent($request);
        
        // 4. SQL Injection Protection
        $this->checkSqlInjection($request);
        
        // 5. XSS Protection
        $this->checkXssAttacks($request);
        
        // 6. Path Traversal Protection
        $this->checkPathTraversal($request);
        
        // 7. Command Injection Protection
        $this->checkCommandInjection($request);
        
        // 8. File Upload Security
        $this->checkFileUploadSecurity($request);
        
        // 9. Session Security
        $this->enhanceSessionSecurity($request);
        
        // 10. Double Login Prevention
        $this->preventDoubleLogin($request);
        
        // Process request
        $response = $next($request);
        
        // 11. Add security headers
        $this->addSecurityHeaders($response);
        
        return $response;
    }
    
    /**
     * Get real client IP address
     */
    private function getClientIp(Request $request): string
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Direct connection
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $request->ip();
    }
    
    /**
     * Handle rate limiting and DDoS protection
     */
    private function handleRateLimit(Request $request, string $clientIp): void
    {
        $key = 'rate_limit:' . $clientIp;
        
        // Global rate limit: 100 requests per minute
        if (RateLimiter::tooManyAttempts($key, 100)) {
            SecurityLog::logEvent(
                null,
                'rate_limit_exceeded',
                "Rate limit exceeded for IP: {$clientIp}",
                'high',
                ['ip' => $clientIp, 'user_agent' => $request->userAgent()]
            );
            
            abort(429, 'Too Many Requests');
        }
        
        RateLimiter::hit($key, 60);
        
        // Stricter limits for auth routes
        if ($request->is('login*') || $request->is('register*')) {
            $authKey = 'auth_rate_limit:' . $clientIp;
            
            if (RateLimiter::tooManyAttempts($authKey, 10)) {
                SecurityLog::logEvent(
                    null,
                    'auth_rate_limit_exceeded',
                    "Auth rate limit exceeded for IP: {$clientIp}",
                    'critical',
                    ['ip' => $clientIp, 'path' => $request->path()]
                );
                
                abort(429, 'Too Many Authentication Attempts');
            }
            
            RateLimiter::hit($authKey, 60);
        }
    }
    
    /**
     * Check for suspicious IP addresses
     */
    private function checkSuspiciousIp(string $clientIp): void
    {
        // Check if IP is in blacklist
        $blacklistedIps = Cache::get('blacklisted_ips', []);
        
        if (in_array($clientIp, $blacklistedIps)) {
            SecurityLog::logEvent(
                null,
                'blacklisted_ip_access',
                "Access attempt from blacklisted IP: {$clientIp}",
                'critical',
                ['ip' => $clientIp, 'blacklist_reason' => 'Suspicious activity']
            );
            
            abort(403, 'Access Denied');
        }
        
        // Check for suspicious patterns
        $suspiciousCount = Cache::get("suspicious_activity:{$clientIp}", 0);
        
        if ($suspiciousCount > 50) {
            // Auto-blacklist IP
            $blacklistedIps[] = $clientIp;
            Cache::put('blacklisted_ips', $blacklistedIps, 3600); // 1 hour
            
            SecurityLog::logEvent(
                null,
                'auto_blacklisted_ip',
                "IP auto-blacklisted due to suspicious activity: {$clientIp}",
                'critical',
                ['ip' => $clientIp, 'suspicious_count' => $suspiciousCount]
            );
            
            abort(403, 'Access Denied - Suspicious Activity Detected');
        }
    }
    
    /**
     * Validate User Agent
     */
    private function validateUserAgent(Request $request): void
    {
        $userAgent = $request->userAgent();
        
        // Check for empty or suspicious user agents
        if (empty($userAgent) || strlen($userAgent) < 10) {
            $this->logSuspiciousActivity($request, 'suspicious_user_agent', 'Empty or too short user agent');
        }
        
        // Check for common attack tools
        $attackTools = [
            'sqlmap', 'nikto', 'wfuzz', 'dirb', 'gobuster', 'hydra', 'nmap',
            'burp', 'zaproxy', 'python-requests', 'wget', 'curl',
            'masscan', 'zmap', 'nuclei', 'subfinder'
        ];
        
        foreach ($attackTools as $tool) {
            if (stripos($userAgent, $tool) !== false) {
                SecurityLog::logEvent(
                    null,
                    'attack_tool_detected',
                    "Attack tool detected in user agent: {$tool}",
                    'critical',
                    ['ip' => $this->getClientIp($request), 'user_agent' => $userAgent]
                );
                
                abort(403, 'Access Denied - Unauthorized Tool Detected');
            }
        }
    }    /**
     * Check for SQL Injection attempts
     */
    private function checkSqlInjection(Request $request): void
    {
        // Skip SQL injection check for email verification routes and profile routes
        if ($request->is('email/verify/*') || $request->is('email/resend') || 
            $request->is('email/verification-notification') ||
            $request->is('profile*') || $request->is('settings*') || $request->is('user*')) {
            return;
        }
        
        $sqlPatterns = [
            // Union-based injections
            '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)(\s|$)/i',
            // Boolean-based injections
            '/(\s|^)(and|or)(\s|$).*(\s|^)(true|false|\d+\s*=\s*\d+)/i',
            // Time-based injections
            '/(sleep|waitfor|benchmark|pg_sleep)/i',
            // Error-based injections (more specific pattern to avoid false positives)
            '/(extractvalue|updatexml|floor\(|rand\()/i',
            // MSSQL specific
            '/(xp_cmdshell|sp_password|sp_make_web_task)/i',
            // MySQL specific
            '/(load_file|into\s+outfile|into\s+dumpfile)/i',
            // Generic patterns (more specific)
            '/(\s|^)(char\(|ascii\(|substring\(|mid\(|concat\()/i',
            // Hex encoded (longer patterns only)
            '/0x[0-9a-f]{12,}/i',
        ];
        
        $allInput = array_merge($request->all(), [$request->getRequestUri()]);
        
        foreach ($allInput as $key => $value) {
            if (is_string($value)) {
                // Skip check for certain safe fields and parameters
                $safeFields = ['_token', '_method', 'password', 'password_confirmation', 'remember', 'expires', 'signature'];
                
                // Only check captcha field if we're on login route
                if ($key === 'captcha' && !$request->is('login')) {
                    continue;
                }
                
                if (in_array($key, $safeFields)) {
                    continue;
                }
                
                // Skip CSRF token-like patterns (Laravel tokens)
                if (preg_match('/^[a-zA-Z0-9+\/]{40,}[=]{0,2}$/', $value)) {
                    continue;
                }
                
                // Skip URL verification patterns (Laravel email verification URLs)
                if (preg_match('/^\/email\/verify\/\d+\/[a-f0-9]{40}\?expires=\d+&signature=[a-f0-9]{64}$/', $value)) {
                    continue;
                }
                
                // Skip timestamp-like patterns (expires parameter)
                if (is_numeric($value) && strlen($value) === 10) {
                    continue;
                }
                
                foreach ($sqlPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        // Additional context check for "exp" pattern to avoid false positives
                        if (strpos($pattern, 'exp') !== false) {
                            // Check if it's part of "expires" parameter or timestamp
                            if (strpos($value, 'expires=') !== false || 
                                ($key === 'expires' || $key === 0) ||
                                (is_numeric($value) && strlen($value) >= 10)) {
                                continue;
                            }
                        }
                        
                        // Log detailed information for debugging
                        Log::error('SQL Injection Detection', [
                            'parameter' => $key,
                            'value' => $value,
                            'pattern' => $pattern,
                            'ip' => $this->getClientIp($request),
                            'url' => $request->fullUrl(),
                            'method' => $request->method()
                        ]);
                        
                        SecurityLog::logEvent(
                            null,
                            'sql_injection_attempt',
                            "SQL injection attempt detected in parameter: {$key}",
                            'critical',
                            [
                                'ip' => $this->getClientIp($request),
                                'parameter' => $key,
                                'value' => substr($value, 0, 500),
                                'pattern' => $pattern,
                                'url' => $request->fullUrl()
                            ]
                        );
                        
                        $this->logSuspiciousActivity($request, 'sql_injection', "SQL injection in {$key}");
                        
                        abort(403, 'Access Denied - Malicious Input Detected (SQL)');
                    }
                }
            }
        }
    }
      /**
     * Check for XSS attacks
     */    private function checkXssAttacks(Request $request): void
    {
        // Skip XSS check for profile routes to avoid false positives
        if ($request->is('profile*') || $request->is('settings*') || $request->is('user*')) {
            return;
        }

        $xssPatterns = [
            // Script tags
            '/<script[^>]*>.*?<\/script>/is',
            // Event handlers
            '/on\w+\s*=\s*["\']?[^"\']*["\']?/i',
            // JavaScript protocol
            '/javascript\s*:/i',
            // Data protocol
            '/data\s*:\s*[^,]*,/i',
            // VBScript
            '/vbscript\s*:/i',
            // Expression
            '/expression\s*\(/i',
            // Import
            '/@import/i',
            // Iframe
            '/<iframe[^>]*>.*?<\/iframe>/is',
            // Object/embed
            '/<(object|embed|applet)[^>]*>.*?<\/\1>/is',
            // Meta refresh
            '/<meta[^>]*http-equiv[^>]*refresh[^>]*>/i',
            // Form
            '/<form[^>]*>.*?<\/form>/is',
            // Link
            '/<link[^>]*>/i',
            // Base
            '/<base[^>]*>/i',
            // SVG
            '/<svg[^>]*>.*?<\/svg>/is',
            // Math
            '/<math[^>]*>.*?<\/math>/is',
        ];

        $allInput = array_merge($request->all(), [$request->getRequestUri()]);

        foreach ($allInput as $key => $value) {if (is_string($value)) {
                // Skip check for certain safe fields - remove captcha from general check since it's only for login now
                $safeFields = ['_token'];
                
                // Only check captcha field if we're on login route
                if ($key === 'captcha' && !$request->is('login')) {
                    continue;
                }
                
                if (in_array($key, $safeFields)) {
                    continue;
                }
                
                $decodedValue = html_entity_decode($value, ENT_QUOTES, 'UTF-8');
                $decodedValue = urldecode($decodedValue);
                
                foreach ($xssPatterns as $pattern) {
                    if (preg_match($pattern, $decodedValue)) {
                        // Log detailed information for debugging
                        Log::error('XSS Attack Detection', [
                            'parameter' => $key,
                            'value' => $value,
                            'decoded_value' => $decodedValue,
                            'pattern' => $pattern,
                            'ip' => $this->getClientIp($request),
                            'url' => $request->fullUrl(),
                            'method' => $request->method()
                        ]);
                        
                        SecurityLog::logEvent(
                            null,
                            'xss_attempt',
                            "XSS attempt detected in parameter: {$key}",
                            'high',
                            [
                                'ip' => $this->getClientIp($request),
                                'parameter' => $key,
                                'value' => substr($value, 0, 500),
                                'decoded_value' => substr($decodedValue, 0, 500),
                                'url' => $request->fullUrl()
                            ]
                        );
                        
                        $this->logSuspiciousActivity($request, 'xss_attempt', "XSS in {$key}");
                        
                        abort(403, 'Access Denied - XSS Attempt Detected');
                    }
                }
            }
        }
    }
    
    /**
     * Check for Path Traversal attacks
     */
    private function checkPathTraversal(Request $request): void
    {
        $pathTraversalPatterns = [
            '/\.\.\//',
            '/\.\.\\\\/',
            '/%2e%2e%2f/',
            '/%2e%2e\\\\/',
            '/\.\.%2f/',
            '/\.\.%5c/',
            '/%252e%252e%252f/',
            '/%c0%ae%c0%ae%c0%af/',
            '/%c1%9c%c1%9c%c1%af/',
            '/\.\.\x2f/',
            '/\.\.\x5c/',
        ];
        
        $allInput = array_merge($request->all(), [$request->getRequestUri()]);
        
        foreach ($allInput as $key => $value) {
            if (is_string($value)) {
                foreach ($pathTraversalPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        SecurityLog::logEvent(
                            null,
                            'path_traversal_attempt',
                            "Path traversal attempt detected in parameter: {$key}",
                            'high',
                            [
                                'ip' => $this->getClientIp($request),
                                'parameter' => $key,
                                'value' => substr($value, 0, 500)
                            ]
                        );
                        
                        $this->logSuspiciousActivity($request, 'path_traversal', "Path traversal in {$key}");
                        
                        abort(403, 'Access Denied - Path Traversal Detected');
                    }
                }
            }
        }
    }
    
    /**
     * Check for Command Injection attacks
     */    private function checkCommandInjection(Request $request): void
    {
        // Skip command injection check for profile routes to avoid false positives
        if ($request->is('profile*') || $request->is('settings*') || $request->is('user*')) {
            return;
        }

        // Only check for very obvious command injection patterns
        $commandPatterns = [
            // Only very obvious Unix commands with clear separators
            '/(\s|^|;|\||\||&|`|\$\()(cat|ls|pwd|whoami|id|rm|mv|cp|chmod|chown|su|sudo|passwd|kill|killall)(\s|;|\||&|`|$)/i',
            // Only very obvious Windows commands with clear separators  
            '/(\s|^|;|\||&|`|\$\()(dir|type|copy|move|del|format|powershell|cmd|command|reg|regedit)(\s|;|\||&|`|$)/i',
            // Only obvious command separators at word boundaries
            '/(\s|^)(;|\||&&|\|\|)(\s|$)/i',
            // Only obvious redirections
            '/(\s|^)(>>|<<|2>>|&>>)(\s|$)/i',
            // Backticks and command substitution (keep these as they're rarely legitimate)
            '/`[^`]*`/',
            '/\$\([^)]*\)/',
        ];

        $allInput = array_merge($request->all(), [$request->getRequestUri()]);

        foreach ($allInput as $key => $value) {
            if (is_string($value)) {
                // Skip common form fields that might contain legitimate special characters
                if (in_array($key, ['name', 'address', 'city', 'phone', 'description', 'bio', 'notes', 'comment'])) {
                    continue;
                }

                foreach ($commandPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        SecurityLog::logEvent(
                            null,
                            'command_injection_attempt',
                            "Command injection attempt detected in parameter: {$key}",
                            'critical',
                            [
                                'ip' => $this->getClientIp($request),
                                'parameter' => $key,
                                'value' => substr($value, 0, 500)
                            ]
                        );

                        $this->logSuspiciousActivity($request, 'command_injection', "Command injection in {$key}");

                        abort(403, 'Access Denied - Command Injection Detected');
                    }
                }
            }
        }
    }
    
    /**
     * Check file upload security
     */
    private function checkFileUploadSecurity(Request $request): void
    {
        if ($request->hasFile('file') || !empty($_FILES)) {
            $dangerousExtensions = [
                'php', 'php3', 'php4', 'php5', 'phtml', 'asp', 'aspx', 'jsp', 'jspx',
                'cfm', 'cfc', 'pl', 'py', 'rb', 'sh', 'bat', 'cmd', 'com', 'pif',
                'scr', 'msi', 'vbs', 'js', 'jar', 'exe', 'dll', 'so', 'dylib'
            ];
            
            foreach ($request->allFiles() as $key => $files) {
                $fileArray = is_array($files) ? $files : [$files];
                
                foreach ($fileArray as $file) {
                    if ($file->isValid()) {
                        $extension = strtolower($file->getClientOriginalExtension());
                        $filename = $file->getClientOriginalName();
                        
                        // Check dangerous extensions
                        if (in_array($extension, $dangerousExtensions)) {
                            SecurityLog::logEvent(
                                Auth::id(),
                                'dangerous_file_upload',
                                "Dangerous file upload attempt: {$filename}",
                                'high',
                                [
                                    'ip' => $this->getClientIp($request),
                                    'filename' => $filename,
                                    'extension' => $extension,
                                    'mime_type' => $file->getMimeType()
                                ]
                            );
                            
                            abort(403, 'Access Denied - Dangerous File Type');
                        }
                        
                        // Check file size (max 10MB for security)
                        if ($file->getSize() > 10485760) {
                            SecurityLog::logEvent(
                                Auth::id(),
                                'oversized_file_upload',
                                "Oversized file upload attempt: {$filename}",
                                'medium',
                                [
                                    'ip' => $this->getClientIp($request),
                                    'filename' => $filename,
                                    'size' => $file->getSize()
                                ]
                            );
                            
                            abort(413, 'File Too Large');
                        }
                    }
                }
            }
        }
    }
      /**
     * Enhance session security
     */
    private function enhanceSessionSecurity(Request $request): void
    {
        // Regenerate session ID periodically
        if (Auth::check()) {
            $lastRegeneration = session('last_regeneration', 0);
            
            if (time() - $lastRegeneration > 900) { // 15 minutes
                $request->session()->regenerate();
                session(['last_regeneration' => time()]);
            }
            
            // Check for session hijacking (less strict)
            $storedFingerprint = session('security_fingerprint');
            $currentFingerprint = $this->generateSecurityFingerprint($request);
            
            if ($storedFingerprint && $storedFingerprint !== $currentFingerprint) {
                // Log but don't immediately logout - might be legitimate browser changes
                SecurityLog::logEvent(
                    Auth::id(),
                    'session_fingerprint_changed',
                    'Session fingerprint changed - monitoring',
                    'low', // Changed from critical to low
                    [
                        'ip' => $this->getClientIp($request),
                        'stored_fingerprint' => substr($storedFingerprint, 0, 16),
                        'current_fingerprint' => substr($currentFingerprint, 0, 16)
                    ]
                );
                
                // Update fingerprint instead of logging out
                session(['security_fingerprint' => $currentFingerprint]);
            }
            
            if (!$storedFingerprint) {
                session(['security_fingerprint' => $currentFingerprint]);
            }
        }
    }
    
    /**
     * Prevent double login
     */
    private function preventDoubleLogin(Request $request): void
    {
        if (Auth::check()) {
            $user = Auth::user();
            $currentSessionId = $request->session()->getId();
            $storedSessionId = $user->current_session_id;
            
            if ($storedSessionId && $storedSessionId !== $currentSessionId) {
                SecurityLog::logEvent(
                    $user->id,
                    'concurrent_session_detected',
                    'Concurrent session detected - logging out user',
                    'high',
                    [
                        'ip' => $this->getClientIp($request),
                        'current_session' => $currentSessionId,
                        'stored_session' => $storedSessionId
                    ]
                );
                
                // Update user's session ID
                $user->current_session_id = $currentSessionId;
                $user->save();
            }
        }
    }
      /**
     * Add security headers to response
     */
    private function addSecurityHeaders($response): void
    {
        $headers = [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://js.pusher.com https://sockjs-mt1.pusher.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.bunny.net https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://fonts.bunny.net https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' ws://localhost:* wss://ws-mt1.pusher.com https://sockjs-mt1.pusher.com",
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
            'Permissions-Policy' => 'geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self)',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ];
        
        foreach ($headers as $header => $value) {
            $response->header($header, $value);
        }
    }
    
    /**
     * Generate security fingerprint
     */
    private function generateSecurityFingerprint(Request $request): string
    {
        return hash('sha256', 
            $request->userAgent() . 
            $request->header('Accept-Language', '') . 
            $request->header('Accept-Encoding', '') . 
            $this->getClientIp($request)
        );
    }
    
    /**
     * Log suspicious activity
     */
    private function logSuspiciousActivity(Request $request, string $type, string $details): void
    {
        $clientIp = $this->getClientIp($request);
        $key = "suspicious_activity:{$clientIp}";
        
        $count = Cache::increment($key, 1);
        Cache::put($key, $count, 3600); // 1 hour expiry
        
        SecurityLog::logEvent(
            Auth::id(),
            $type,
            $details,
            'medium',
            [
                'ip' => $clientIp,
                'user_agent' => $request->userAgent(),
                'path' => $request->path(),
                'method' => $request->method(),
                'suspicious_count' => $count
            ]
        );
    }
}
