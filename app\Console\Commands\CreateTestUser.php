<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class CreateTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:create-user {--verified : Create user with verified email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test user for login testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $verified = $this->option('verified');
        
        // Delete existing test users first
        User::where('username', 'testlogin')->delete();
        User::where('email', '<EMAIL>')->delete();
        
        $user = User::create([
            'name' => 'Test Login User',
            'username' => 'testlogin',
            'full_name' => 'Test Login User',
            'email' => '<EMAIL>',
            'password' => Hash::make('Test123!'),
            'email_verified_at' => $verified ? now() : null,
            'is_active' => $verified ? true : false,
            'verification_token' => $verified ? null : Str::random(64),
            'timezone' => 'Asia/Jakarta',
            'language' => 'id',
            'theme' => 'light',
        ]);
        
        $status = $verified ? 'verified and active' : 'unverified and inactive';
        $this->info("Test user created successfully!");
        $this->info("Username: testlogin");
        $this->info("Password: Test123!");
        $this->info("Email: <EMAIL>");
        $this->info("Status: {$status}");
        
        if (!$verified) {
            $this->warn("This user needs email verification before login.");
            $this->info("You can manually verify by running: php artisan test:verify-user testlogin");
        }
    }
}
