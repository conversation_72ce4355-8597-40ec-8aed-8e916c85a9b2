<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Category;
use App\Models\User;

class CreateExpenseCategorySamples extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'categories:expense-samples {--user-id= : ID user untuk menambahkan kategori}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Buat sample data kategori pengeluaran';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user-id');
        
        if (!$userId) {
            // Ambil user pertama jika tidak ada user-id yang ditentukan
            $user = User::first();
            if (!$user) {
                $this->error('Tidak ada user ditemukan. Silakan buat user terlebih dahulu.');
                return;
            }
            $userId = $user->id;
            $this->info("Menggunakan user: {$user->name} (ID: {$userId})");
        } else {
            $user = User::find($userId);
            if (!$user) {
                $this->error("User dengan ID {$userId} tidak ditemukan.");
                return;
            }
            $this->info("Menggunakan user: {$user->name} (ID: {$userId})");
        }

        $categories = [
            [
                'name' => 'Makanan & Minuman',
                'description' => 'Belanja makanan, makan di restoran, jajan',
                'icon' => 'fas fa-utensils',
                'color' => '#fd7e14',
                'sort_order' => 1
            ],
            [
                'name' => 'Transportasi',
                'description' => 'Bensin, ojek online, bus, kereta, parkir',
                'icon' => 'fas fa-car',
                'color' => '#6f42c1',
                'sort_order' => 2
            ],
            [
                'name' => 'Belanja',
                'description' => 'Pakaian, elektronik, kebutuhan rumah tangga',
                'icon' => 'fas fa-shopping-cart',
                'color' => '#e83e8c',
                'sort_order' => 3
            ],
            [
                'name' => 'Hiburan',
                'description' => 'Nonton, game, rekreasi, hobi',
                'icon' => 'fas fa-gamepad',
                'color' => '#20c997',
                'sort_order' => 4
            ],
            [
                'name' => 'Kesehatan',
                'description' => 'Dokter, obat, rumah sakit, asuransi kesehatan',
                'icon' => 'fas fa-medkit',
                'color' => '#dc3545',
                'sort_order' => 5
            ],
            [
                'name' => 'Pendidikan',
                'description' => 'Kursus, buku, pelatihan, biaya sekolah',
                'icon' => 'fas fa-book',
                'color' => '#0d6efd',
                'sort_order' => 6
            ]
        ];

        $this->info('Membuat kategori pengeluaran sample...');

        foreach ($categories as $categoryData) {
            // Cek apakah kategori sudah ada
            $existing = Category::where('user_id', $userId)
                               ->where('name', $categoryData['name'])
                               ->where('type', 'expense')
                               ->first();

            if ($existing) {
                $this->warn("Kategori '{$categoryData['name']}' sudah ada, dilewati.");
                continue;
            }

            Category::create([
                'user_id' => $userId,
                'name' => $categoryData['name'],
                'description' => $categoryData['description'],
                'type' => 'expense',
                'icon' => $categoryData['icon'],
                'color' => $categoryData['color'],
                'sort_order' => $categoryData['sort_order'],
                'is_active' => true,
                'budget_limit' => null,
                'budget_period' => null
            ]);

            $this->info("✓ Kategori '{$categoryData['name']}' berhasil dibuat.");
        }

        $this->info('');
        $this->info('Sample data kategori pengeluaran berhasil dibuat!');
        $this->info('Kategori yang dibuat:');
        foreach ($categories as $cat) {
            $this->line("- {$cat['name']}: {$cat['description']}");
        }
    }
}
