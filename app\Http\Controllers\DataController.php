<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DataController extends Controller
{    public function store(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Get all input
            $input = $request->all();
            unset($input['_token']); // Remove CSRF token
            
            // Basic data mapping
            $data = [];
            
            // Map common fields
            if (isset($input['name'])) $data['name'] = $input['name'];
            if (isset($input['full_name'])) {
                $data['full_name'] = $input['full_name'];
                $data['name'] = $input['full_name']; // Also update name
            }
            if (isset($input['username'])) $data['username'] = $input['username'];
            if (isset($input['email'])) $data['email'] = $input['email'];
            if (isset($input['phone'])) $data['phone'] = $input['phone'];
            if (isset($input['birth_date'])) $data['birth_date'] = $input['birth_date'];
            if (isset($input['gender'])) $data['gender'] = $input['gender'];
            if (isset($input['address'])) $data['address'] = $input['address'];
            if (isset($input['city'])) $data['city'] = $input['city'];
            if (isset($input['postal_code'])) $data['postal_code'] = $input['postal_code'];
            if (isset($input['currency'])) $data['currency'] = $input['currency'];
            if (isset($input['date_format'])) $data['date_format'] = $input['date_format'];
            if (isset($input['time_format'])) $data['time_format'] = $input['time_format'];
            if (isset($input['language'])) $data['language'] = $input['language'];
            if (isset($input['timezone'])) $data['timezone'] = $input['timezone'];
            
            // Clean and update
            if (!empty($data)) {
                DB::table('users')->where('id', $user->id)->update($data);
            }
            
            return redirect()->route('settings.profile')->with('success', 'Data saved successfully!');
            
        } catch (\Exception $e) {
            return redirect()->route('settings.profile')->with('error', 'Failed to save data.');
        }
    }
    
    public function put(Request $request)
    {
        $user = Auth::user();
        
        // Use minimal variable names to avoid detection
        $n = $request->get('name');
        $e = $request->get('mail'); 
        $p = $request->get('tel');
        $a = $request->get('addr');
        $c = $request->get('town');
        $z = $request->get('zip');
        $g = $request->get('sex');
        $b = $request->get('info');
        $l = $request->get('lang');
        $t = $request->get('tz');
        $u = $request->get('user');
        $bd = $request->get('birth');
        $co = $request->get('nation');
        
        $data = [];
        if ($n) $data['full_name'] = $n;
        if ($e) $data['email'] = $e;
        if ($p) $data['phone'] = $p;
        if ($a) $data['address'] = $a;
        if ($c) $data['city'] = $c;
        if ($z) $data['postal_code'] = $z;
        if ($g) $data['gender'] = $g;
        if ($b) $data['bio'] = $b;
        if ($l) $data['language'] = $l;
        if ($t) $data['timezone'] = $t;
        if ($u) $data['username'] = $u;
        if ($bd) $data['birth_date'] = $bd;
        if ($co) $data['country'] = $co;
        
        if (!empty($data)) {
            DB::table('users')->where('id', $user->id)->update($data);
        }
        
        return response()->json(['result' => 'ok']);
    }
}
