<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'My Money') }} - Verifikasi Email</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Bootstrap & Custom CSS -->
    @vite(['resources/sass/app.scss'])
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Figtree', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .verify-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .verify-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }
        
        .verify-title {
            font-size: 2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .verify-subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        
        .verify-email {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 30px;
            font-weight: 500;
            color: #667eea;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s ease;
            margin: 10px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            border: none;
            border-radius: 10px;
            color: white;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s ease;
            margin: 10px;
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <div class="verify-icon">📧</div>
        <h1 class="verify-title">Verifikasi Email</h1>
        
        <p class="verify-subtitle">
            Sebelum melanjutkan, silakan periksa email Anda untuk link verifikasi.
            Jika Anda tidak menerima email tersebut, klik tombol di bawah untuk mengirim ulang.
        </p>
        
        @if(auth()->check())
        <div class="verify-email">
            {{ auth()->user()->email }}
        </div>
        @endif
        
        <form method="POST" action="{{ route('verification.resend') }}" style="display: inline;">
            @csrf
            <button type="submit" class="btn-primary">
                Kirim Ulang Email Verifikasi
            </button>
        </form>
        
        <a href="{{ route('login') }}" class="btn-secondary">
            Kembali ke Login
        </a>
        
        @if(auth()->check())
        <form method="POST" action="{{ route('logout') }}" style="display: inline;">
            @csrf
            <button type="submit" class="btn-secondary">
                Logout
            </button>
        </form>
        @endif
    </div>
    
    @vite(['resources/js/app.js'])
    
    <script>
        // Handle flash messages
        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                confirmButtonColor: '#667eea'
            });
        @endif
        
        @if(session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: '{{ session('error') }}',
                confirmButtonColor: '#667eea'
            });
        @endif
        
        @if(session('resent'))
            Swal.fire({
                icon: 'success',
                title: 'Email Terkirim!',
                text: 'Link verifikasi baru telah dikirim ke email Anda!',
                confirmButtonColor: '#667eea'
            });
        @endif
    </script>
</body>
</html>
