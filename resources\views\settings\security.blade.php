@extends('layouts.dashboard')

@section('content')
    
@section('content')
<div class="container-fluid">
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <!-- Two-Factor Authentication -->
        <div class="col-lg-6 mb-4">
            <div class="card security-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="security-icon me-3">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-1">Autentikasi Dua Faktor (2FA)</h5>
                            <p class="card-subtitle mb-0">Tingkatkan keamanan dengan verifikasi tambahan</p>
                        </div>
                    </div>

                    <div class="security-status mb-3">
                        @if(Auth::user()->has2FAEnabled())
                            <span class="badge bg-success me-2">
                                <i class="fas fa-check-circle me-1"></i>Aktif
                            </span>
                            <small class="text-muted">2FA telah diaktifkan untuk akun Anda</small>
                        @else
                            <span class="badge bg-warning me-2">
                                <i class="fas fa-exclamation-triangle me-1"></i>Tidak Aktif
                            </span>
                            <small class="text-muted">Akun Anda belum dilindungi 2FA</small>
                        @endif
                        
                        <!-- Debug info (remove in production) -->
                        @if(config('app.debug'))
                            <div class="mt-2 small text-muted">
                                <strong>Debug Info:</strong><br>
                                two_factor_enabled: {{ Auth::user()->two_factor_enabled ? 'true' : 'false' }}<br>
                                has_secret: {{ !empty(Auth::user()->two_factor_secret) ? 'true' : 'false' }}<br>
                                confirmed_at: {{ Auth::user()->two_factor_confirmed_at ? Auth::user()->two_factor_confirmed_at : 'null' }}<br>
                                has2FAEnabled(): {{ Auth::user()->has2FAEnabled() ? 'true' : 'false' }}
                            </div>
                        @endif
                    </div>

                    <div class="security-actions">
                        @if(Auth::user()->has2FAEnabled())
                            <a href="{{ route('settings.2fa.show') }}" class="btn btn-outline-primary btn-sm me-2">
                                <i class="fas fa-key me-1"></i>Lihat Backup Codes
                            </a>
                            <button type="button" class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#disable2faModal">
                                <i class="fas fa-times me-1"></i>Nonaktifkan 2FA
                            </button>
                        @else
                            <a href="{{ route('settings.2fa.setup') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-1"></i>Aktifkan 2FA
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Password Security -->
        <div class="col-lg-6 mb-4">
            <div class="card security-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="security-icon me-3">
                            <i class="fas fa-key"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-1">Password</h5>
                            <p class="card-subtitle mb-0">Ubah password akun Anda</p>
                        </div>
                    </div>

                    <div class="security-status mb-3">
                        @if($user->password_changed_at)
                            <small class="text-muted">
                                Terakhir diubah: {{ $user->password_changed_at->format('d M Y H:i') }}
                            </small>
                        @else
                            <small class="text-muted">Belum pernah diubah</small>
                        @endif
                    </div>

                    <div class="security-actions">
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="card security-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="security-icon me-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-1">Statistik Keamanan</h5>
                            <p class="card-subtitle mb-0">Ringkasan aktivitas keamanan akun</p>
                        </div>
                    </div>

                    <div class="security-stats">
                        <div class="stat-item mb-2">
                            <span class="stat-label">Total Login:</span>
                            <span class="stat-value">{{ $securityStats['total_logins'] }}</span>
                        </div>
                        <div class="stat-item mb-2">
                            <span class="stat-label">Login Gagal (30 hari):</span>
                            <span class="stat-value">{{ $securityStats['failed_attempts'] }}</span>
                        </div>
                        <div class="stat-item mb-2">
                            <span class="stat-label">Login Terakhir:</span>
                            <span class="stat-value">
                                {{ $securityStats['last_login'] ? $securityStats['last_login']->format('d M Y H:i') : 'Belum ada' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Security Activities -->
        <div class="col-lg-6 mb-4">
            <div class="card security-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="security-icon me-3">
                            <i class="fas fa-history"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-1">Aktivitas Terbaru</h5>
                            <p class="card-subtitle mb-0">Log aktivitas keamanan</p>
                        </div>
                    </div>

                    <div class="security-activities">
                        @forelse($recentActivities->take(5) as $activity)
                            <div class="activity-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="activity-type">
                                            @switch($activity->event_type)
                                                @case('login_success')
                                                    <i class="fas fa-sign-in-alt text-success me-1"></i>Login Berhasil
                                                    @break
                                                @case('login_failed')
                                                    <i class="fas fa-times-circle text-danger me-1"></i>Login Gagal
                                                    @break
                                                @case('2fa_enabled')
                                                    <i class="fas fa-shield-alt text-primary me-1"></i>2FA Diaktifkan
                                                    @break
                                                @case('2fa_disabled')
                                                    <i class="fas fa-shield-alt text-warning me-1"></i>2FA Dinonaktifkan
                                                    @break
                                                @default
                                                    <i class="fas fa-info-circle text-info me-1"></i>{{ ucfirst($activity->event_type) }}
                                            @endswitch
                                        </span>
                                        @if($activity->ip_address)
                                            <small class="text-muted d-block">IP: {{ $activity->ip_address }}</small>
                                        @endif
                                    </div>
                                    <small class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                                </div>
                            </div>
                        @empty
                            <p class="text-muted">Belum ada aktivitas keamanan</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ubah Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('settings.security.password') }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Password Saat Ini</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">Password Baru</label>
                        <input type="password" class="form-control" id="new_password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="password_confirmation" class="form-label">Konfirmasi Password Baru</label>
                        <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Ubah Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Disable 2FA Modal -->
@if(Auth::user()->has2FAEnabled())
<div class="modal fade" id="disable2faModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nonaktifkan 2FA</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('settings.2fa.disable') }}" method="POST">
                @csrf
                @method('DELETE')
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Peringatan! Menonaktifkan 2FA akan mengurangi keamanan akun Anda.
                    </div>
                    <div class="mb-3">
                        <label for="verification_code" class="form-label">Kode Verifikasi 2FA atau Backup Code</label>
                        <input type="text" class="form-control" id="verification_code" name="code" 
                               placeholder="Masukkan kode dari aplikasi authenticator atau backup code" required>
                        <small class="form-text text-muted">
                            Masukkan kode 6 digit dari aplikasi authenticator atau salah satu backup code Anda.
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Nonaktifkan 2FA</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@push('styles')
<style>
.security-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.security-card:hover {
    transform: translateY(-2px);
}

.security-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.security-status .badge {
    font-size: 0.8rem;
}

.security-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.security-stats .stat-item:last-child {
    border-bottom: none;
}

.security-stats .stat-label {
    font-weight: 500;
    color: #666;
}

.security-stats .stat-value {
    font-weight: 600;
    color: #333;
}

.activity-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

/* Theme-specific styles */
body[data-theme="dark"] .security-card {
    background: #2d3748;
    color: #e2e8f0;
}

body[data-theme="dark"] .security-stats .stat-label {
    color: #a0aec0;
}

body[data-theme="dark"] .security-stats .stat-value {
    color: #e2e8f0;
}

/* Synth Wave Theme */
body[data-theme="synth-wave"] .security-card {
    background: #1a1a1a;
    border: 1px solid #ff00ff;
}

body[data-theme="synth-wave"] .card-content {
    color: #ffffff;
}

body[data-theme="synth-wave"] .action-btn {
    background: linear-gradient(45deg, #ff00ff, #00ffff);
    box-shadow: 0 0 20px rgba(255, 0, 255, 0.4);
}
</style>
@endpush
@endsection