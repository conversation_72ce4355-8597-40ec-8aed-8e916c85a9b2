<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UltraSafeController extends Controller
{
    public function updateData(Request $request)
    {
        $user = Auth::user();
        
        // Get data from query parameters (GET method)
        $name = $request->query('n');
        $email = $request->query('e');
        $phone = $request->query('p');
        $address = $request->query('a');
        $city = $request->query('c');
        
        $updateData = [];
        
        if ($name) {
            $updateData['full_name'] = $name;
        }
        
        if ($email) {
            $updateData['email'] = $email;
        }
        
        if ($phone) {
            $updateData['phone'] = $phone;
        }
        
        if ($address) {
            $updateData['address'] = $address;
        }
        
        if ($city) {
            $updateData['city'] = $city;
        }
        
        if (!empty($updateData)) {
            DB::table('users')->where('id', $user->id)->update($updateData);
        }
        
        return response()->json(['status' => 'ok']);
    }
    
    public function saveProfile(Request $request)
    {
        $user = Auth::user();
        
        // Simple approach - just get the raw input
        $input = $request->all();
        
        $allowed = ['full_name', 'username', 'email', 'phone', 'birth_date', 'gender', 'address', 'city', 'postal_code', 'country', 'bio', 'language', 'timezone'];
        
        $data = [];
        foreach ($allowed as $field) {
            if (isset($input[$field])) {
                $data[$field] = $input[$field];
            }
        }
        
        // Update without complex validation
        DB::table('users')->where('id', $user->id)->update($data);
        
        return response()->json(['success' => true, 'message' => 'Data tersimpan']);
    }
}
