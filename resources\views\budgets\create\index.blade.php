@extends('layouts.dashboard')

@section('title', 'Buat Anggaran Baru')

@section('page-title', 'Anggaran Baru')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Buat Anggaran Baru</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('budgets.index') }}">Anggaran</a></li>
                            <li class="breadcrumb-item active">Buat Anggaran</li>
                        </ol>
                    </nav>
                </div>
                <a href="{{ route('budgets.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
    </div>

    <!-- Form Create Budget -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Anggaran</h6>
                </div>
                <div class="card-body">
                    <form id="createBudgetForm" action="{{ route('budgets.store') }}" method="POST">
                        @csrf
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">Nama Anggaran <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" 
                                           placeholder="Contoh: Anggaran Bulanan Januari 2025" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category_id" class="form-label">Kategori <span class="text-danger">*</span></label>
                                    <select class="form-control @error('category_id') is-invalid @enderror" 
                                            id="category_id" name="category_id" required>
                                        <option value="">Pilih Kategori</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Budget Amount -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="amount" class="form-label">Jumlah Anggaran <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Rp</span>
                                        </div>
                                        <input type="text" class="form-control currency-input @error('amount') is-invalid @enderror" 
                                               id="amount" name="amount" value="{{ old('amount') }}" 
                                               placeholder="0" required>
                                        @error('amount')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">Masukkan jumlah anggaran yang direncanakan</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="type" class="form-label">Tipe Anggaran <span class="text-danger">*</span></label>
                                    <select class="form-control @error('type') is-invalid @enderror" 
                                            id="type" name="type" required>
                                        <option value="">Pilih Tipe</option>
                                        <option value="monthly" {{ old('type') == 'monthly' ? 'selected' : '' }}>Bulanan</option>
                                        <option value="yearly" {{ old('type') == 'yearly' ? 'selected' : '' }}>Tahunan</option>
                                        <option value="custom" {{ old('type') == 'custom' ? 'selected' : '' }}>Kustom</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Period -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                           id="start_date" name="start_date" value="{{ old('start_date') }}" required>
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date" class="form-label">Tanggal Selesai <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                           id="end_date" name="end_date" value="{{ old('end_date') }}" required>
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Deskripsi anggaran (opsional)">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Alert/Notification Settings -->
                        <div class="form-group">
                            <label class="form-label">Pengaturan Peringatan</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="alert_enabled" 
                                               name="alert_enabled" value="1" {{ old('alert_enabled') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="alert_enabled">
                                            Aktifkan Peringatan
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="alert_percentage" class="form-label">Peringatan pada (%):</label>
                                        <select class="form-control" id="alert_percentage" name="alert_percentage">
                                            <option value="50" {{ old('alert_percentage') == '50' ? 'selected' : '' }}>50%</option>
                                            <option value="75" {{ old('alert_percentage') == '75' ? 'selected' : '' }}>75%</option>
                                            <option value="80" {{ old('alert_percentage') == '80' ? 'selected' : '' }}>80%</option>
                                            <option value="90" {{ old('alert_percentage') == '90' ? 'selected' : '' }}>90%</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-group mb-0">
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-secondary mr-2" onclick="history.back()">
                                    <i class="fas fa-times"></i> Batal
                                </button>
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="fas fa-save"></i> Simpan Anggaran
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Pratinjau Anggaran</h6>
                </div>
                <div class="card-body">
                    <div id="budgetPreview">
                        <div class="text-center text-muted">
                            <i class="fas fa-eye fa-2x mb-3"></i>
                            <p>Isi form untuk melihat pratinjau anggaran</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tips Card -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Tips Anggaran</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <small>Buat anggaran realistis berdasarkan pengeluaran sebelumnya</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <small>Aktifkan peringatan untuk monitoring yang lebih baik</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <small>Review anggaran secara berkala</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <small>Sisakan buffer 10-20% untuk pengeluaran tak terduga</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Light Theme (Default) */
.currency-input {
    text-align: right;
}

.form-check-input:checked {
    background-color: #4e73df;
    border-color: #4e73df;
}

#budgetPreview .preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eaecf4;
}

#budgetPreview .preview-item:last-child {
    border-bottom: none;
}

.preview-label {
    font-weight: 600;
    color: #5a5c69;
}

.preview-value {
    color: #3a3b45;
}

/* Solarized Dark Theme */
body[data-theme="solarized-dark"] .card-body {
    background-color: #fdf6e3 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .card-header h6,
body[data-theme="solarized-dark"] h6 {
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] #budgetPreview .preview-item {
    border-bottom-color: #eee8d5 !important;
}

body[data-theme="solarized-dark"] .preview-label {
    color: #657b83 !important;
}

body[data-theme="solarized-dark"] .preview-value {
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .form-check-input:checked {
    background-color: #268bd2 !important;
    border-color: #268bd2 !important;
}

/* Synth Wave Theme */
body[data-theme="synth-wave"] .card-body {
    background: #1a1a1a !important;
    color: #ffffff !important;
}

/* H6 styling untuk semua konteks dalam synth-wave theme */
body[data-theme="synth-wave"] h6,
body[data-theme="synth-wave"] .h6,
body[data-theme="synth-wave"] .card h6,
body[data-theme="synth-wave"] .card .h6,
body[data-theme="synth-wave"] .card-header h6,
body[data-theme="synth-wave"] .card-header .h6,
body[data-theme="synth-wave"] .card-body h6,
body[data-theme="synth-wave"] .card-body .h6,
body[data-theme="synth-wave"] .card.shadow h6,
body[data-theme="synth-wave"] .card.shadow .h6 {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Override untuk class text-primary */
body[data-theme="synth-wave"] h6.text-primary,
body[data-theme="synth-wave"] .h6.text-primary,
body[data-theme="synth-wave"] .card h6.text-primary,
body[data-theme="synth-wave"] .card .h6.text-primary,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Override untuk class text-success */
body[data-theme="synth-wave"] h6.text-success,
body[data-theme="synth-wave"] .h6.text-success,
body[data-theme="synth-wave"] .card h6.text-success,
body[data-theme="synth-wave"] .card .h6.text-success,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold.text-success {
    color: #00ffff !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
}

/* Override untuk class m-0 font-weight-bold */
body[data-theme="synth-wave"] h6.m-0.font-weight-bold,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold,
body[data-theme="synth-wave"] .card h6.m-0.font-weight-bold,
body[data-theme="synth-wave"] .card .h6.m-0.font-weight-bold {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] #budgetPreview .preview-item {
    border-bottom-color: #ff00ff !important;
}

body[data-theme="synth-wave"] .preview-label {
    color: #00ffff !important;
    font-weight: 600;
}

body[data-theme="synth-wave"] .preview-value {
    color: #ff00ff !important;
    text-shadow: 0 0 5px rgba(255, 0, 255, 0.3) !important;
}

body[data-theme="synth-wave"] .form-check-input:checked {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    border-color: #ff00ff !important;
    box-shadow: 0 0 10px rgba(255, 0, 255, 0.3) !important;
}

body[data-theme="synth-wave"] .currency-input {
    background-color: #1a1a1a !important;
    color: #ff00ff !important;
    border-color: #ff00ff !important;
}

body[data-theme="synth-wave"] .currency-input:focus {
    background-color: #1a1a1a !important;
    color: #ff00ff !important;
    border-color: #00ffff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

/* CSS dengan specificity sangat tinggi untuk kombinasi class Bootstrap */
body[data-theme="synth-wave"] .card-header h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .card-body h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .card h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .card-header h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] .card-body h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] .card h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-success {
    color: #00ffff !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
}

/* Override untuk semua h6 dalam card header dengan class apapun */
body[data-theme="synth-wave"] .card-header.py-3 h6 {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Override khusus untuk h6 dengan kombinasi class m-0 font-weight-bold */
body[data-theme="synth-wave"] h6[class*="m-0"][class*="font-weight-bold"] {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] h6[class*="m-0"][class*="font-weight-bold"][class*="text-success"] {
    color: #00ffff !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
}

/* CSS dengan specificity sangat tinggi untuk override Bootstrap */
body[data-theme="synth-wave"] .card.shadow .card-header.py-3 h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .card .card-header h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .container-fluid .card .card-header h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] div.card div.card-header h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Force override dengan inline style priority */
body[data-theme="synth-wave"] h6.text-primary[class*="m-0"][class*="font-weight-bold"] {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* CSS untuk card header dengan background synth-wave */
body[data-theme="synth-wave"] .card-header {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
    border-bottom: 1px solid #ff00ff !important;
}

body[data-theme="synth-wave"] .card-header h6 {
    color: white !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

/* Override yang sangat spesifik untuk text dalam card header */
body[data-theme="synth-wave"] .card.shadow .card-header.py-3 h6,
body[data-theme="synth-wave"] .card .card-header.py-3 h6,
body[data-theme="synth-wave"] .card-header h6.m-0,
body[data-theme="synth-wave"] .card-header h6.font-weight-bold,
body[data-theme="synth-wave"] .card-header h6.text-primary {
    color: white !important;
    text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
    background: transparent !important;
}

/* Force override untuk semua h6 di card header tanpa memandang class */
body[data-theme="synth-wave"] .card-header.py-3 > h6,
body[data-theme="synth-wave"] .card-header > h6 {
    color: white !important;
    text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Currency formatting
    $('.currency-input').on('input', function() {
        let value = this.value.replace(/[^\d]/g, '');
        if (value) {
            this.value = parseInt(value).toLocaleString('id-ID');
        }
        updatePreview();
    });

    // Date validation
    $('#start_date, #end_date').on('change', function() {
        validateDates();
        updatePreview();
    });

    // Type change handler
    $('#type').on('change', function() {
        const type = $(this).val();
        const startDate = $('#start_date');
        const endDate = $('#end_date');
        
        if (type === 'monthly') {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            startDate.val(formatDate(firstDay));
            endDate.val(formatDate(lastDay));
        } else if (type === 'yearly') {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), 0, 1);
            const lastDay = new Date(today.getFullYear(), 11, 31);
            
            startDate.val(formatDate(firstDay));
            endDate.val(formatDate(lastDay));
        }
        updatePreview();
    });

    // Form change handlers
    $('#name, #category_id, #description, #alert_enabled, #alert_percentage').on('change input', updatePreview);

    // Form submission
    $('#createBudgetForm').on('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = $('#submitBtn');
        const originalText = submitBtn.html();
        const form = this;
        
        // Convert currency input to number
        const amountInput = $('#amount');
        const amount = amountInput.val().replace(/[^\d]/g, '');
        amountInput.val(amount);
        
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        
        // Get form data
        const formData = new FormData(form);
        
        // Submit via AJAX
        $.ajax({
            url: $(form).attr('action'),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: response.message || 'Anggaran berhasil dibuat!',
                        confirmButtonColor: '#667eea',
                        timer: 3000,
                        timerProgressBar: true
                    }).then(() => {
                        // Redirect to budgets dashboard
                        window.location.href = response.redirect || '{{ route("budgets.index") }}';
                    });
                }
            },
            error: function(xhr) {
                submitBtn.prop('disabled', false).html(originalText);
                
                if (xhr.status === 422) {
                    // Validation errors
                    const errors = xhr.responseJSON.errors;
                    let errorMessage = 'Terjadi kesalahan validasi:\n';
                    
                    for (const field in errors) {
                        errorMessage += `• ${errors[field].join(', ')}\n`;
                    }
                    
                    Swal.fire({
                        icon: 'error',
                        title: 'Validasi Gagal!',
                        text: errorMessage,
                        confirmButtonColor: '#667eea'
                    });
                } else {
                    // Other errors
                    const message = xhr.responseJSON?.message || 'Terjadi kesalahan saat menyimpan anggaran!';
                    Swal.fire({
                        icon: 'error',
                        title: 'Terjadi Kesalahan!',
                        text: message,
                        confirmButtonColor: '#667eea'
                    });
                }
            }
        });
    });

    function validateDates() {
        const startDate = new Date($('#start_date').val());
        const endDate = new Date($('#end_date').val());
        
        if (startDate && endDate && startDate >= endDate) {
            alert('Tanggal selesai harus lebih besar dari tanggal mulai');
            $('#end_date').focus();
        }
    }

    function updatePreview() {
        const name = $('#name').val();
        const categoryText = $('#category_id option:selected').text();
        const amount = $('#amount').val();
        const type = $('#type option:selected').text();
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();
        const alertEnabled = $('#alert_enabled').is(':checked');
        const alertPercentage = $('#alert_percentage').val();

        if (!name && !amount && !startDate) {
            $('#budgetPreview').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-eye fa-2x mb-3"></i>
                    <p>Isi form untuk melihat pratinjau anggaran</p>
                </div>
            `);
            return;
        }

        let preview = '<div class="preview-content">';
        
        if (name) {
            preview += `
                <div class="preview-item">
                    <span class="preview-label">Nama:</span>
                    <span class="preview-value">${name}</span>
                </div>
            `;
        }

        if (categoryText && categoryText !== 'Pilih Kategori') {
            preview += `
                <div class="preview-item">
                    <span class="preview-label">Kategori:</span>
                    <span class="preview-value">${categoryText}</span>
                </div>
            `;
        }

        if (amount) {
            preview += `
                <div class="preview-item">
                    <span class="preview-label">Anggaran:</span>
                    <span class="preview-value text-primary font-weight-bold">Rp ${amount}</span>
                </div>
            `;
        }

        if (type && type !== 'Pilih Tipe') {
            preview += `
                <div class="preview-item">
                    <span class="preview-label">Tipe:</span>
                    <span class="preview-value">${type}</span>
                </div>
            `;
        }

        if (startDate && endDate) {
            preview += `
                <div class="preview-item">
                    <span class="preview-label">Periode:</span>
                    <span class="preview-value">${formatDateDisplay(startDate)} - ${formatDateDisplay(endDate)}</span>
                </div>
            `;
        }

        if (alertEnabled) {
            preview += `
                <div class="preview-item">
                    <span class="preview-label">Peringatan:</span>
                    <span class="preview-value text-warning">Aktif (${alertPercentage}%)</span>
                </div>
            `;
        }

        preview += '</div>';
        $('#budgetPreview').html(preview);
    }

    function formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    function formatDateDisplay(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
        });
    }
});
</script>
@endpush
@endsection
