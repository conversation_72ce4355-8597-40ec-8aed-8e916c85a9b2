<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SimpleController extends Controller
{
    public function profile()
    {
        return view('simple.profile', ['user' => Auth::user()]);
    }

    public function save(Request $request)
    {
        $user = Auth::user();
        
        // Get only allowed fields
        $fields = [
            'full_name' => $request->input('full_name'),
            'username' => $request->input('username'), 
            'email' => $request->input('email'),
            'phone' => $request->input('phone'),
            'birth_date' => $request->input('birth_date'),
            'gender' => $request->input('gender'),
            'address' => $request->input('address'),
            'city' => $request->input('city'),
            'postal_code' => $request->input('postal_code'),
            'country' => $request->input('country'),
            'bio' => $request->input('bio'),
            'language' => $request->input('language'),
            'timezone' => $request->input('timezone')
        ];
        
        // Clean fields
        foreach ($fields as $key => $value) {
            if ($value !== null) {
                $fields[$key] = trim($value);
            }
        }
        
        // Basic checks
        if (empty($fields['full_name'])) {
            return response()->json(['ok' => false, 'msg' => 'Nama lengkap wajib diisi']);
        }
        
        if (empty($fields['email'])) {
            return response()->json(['ok' => false, 'msg' => 'Email wajib diisi']);
        }
        
        // Update user
        try {
            DB::table('users')->where('id', $user->id)->update(array_filter($fields));
            return response()->json(['ok' => true, 'msg' => 'Profil berhasil diperbarui']);
        } catch (\Exception $e) {
            return response()->json(['ok' => false, 'msg' => 'Gagal menyimpan data']);
        }
    }
}
