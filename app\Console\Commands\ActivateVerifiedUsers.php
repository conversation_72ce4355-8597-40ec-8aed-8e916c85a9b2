<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class ActivateVerifiedUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:activate-verified';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Activate all users who have verified their email but are not yet active';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Mencari user yang sudah verified email tapi belum active...');
        
        // Ambil user yang email sudah verified tapi is_active masih false
        $users = User::whereNotNull('email_verified_at')
                    ->where('is_active', false)
                    ->get();
                    
        if ($users->count() == 0) {
            $this->info('Tidak ada user yang perlu diaktifkan.');
            return Command::SUCCESS;
        }
        
        $this->info('Ditemukan ' . $users->count() . ' user yang perlu diaktifkan:');
        
        foreach ($users as $user) {
            $this->line('- Username: ' . $user->username . ', Email: ' . $user->email . ', Verified: ' . $user->email_verified_at);
        }
        
        if ($this->confirm('Apakah Anda ingin mengaktifkan semua user tersebut?')) {
            foreach ($users as $user) {
                $user->is_active = true;
                $user->save();
                $this->info('✓ User ' . $user->username . ' telah diaktifkan');
            }
            
            $this->info('Semua user berhasil diaktifkan!');
        } else {
            $this->info('Operasi dibatalkan.');
        }
        
        return Command::SUCCESS;
    }
}
