@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON> - ' . $category->name)

@section('page-title', '✏️ <PERSON><PERSON>')

@push('styles')
<style>
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    /* Theme support untuk breadcrumb */
    body[data-theme="solarized-dark"] .breadcrumb-item a {
        color: #b58900 !important;
    }
    
    body[data-theme="solarized-dark"] .breadcrumb-item.active {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item a {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item.active {
        color: #ffffff !important;
    }
    
    /* Theme support untuk h2 */
    body[data-theme="solarized-dark"] h2 {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] h2 {
        color: #ffffff !important;
    }
    
    /* Theme support untuk small text */
    body[data-theme="solarized-dark"] .text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="synth-wave"] .text-muted {
        color: #cccccc !important;
    }
    
    .form-card {
        background: #fdf6e3;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }
    
    /* Theme support */
    body[data-theme="solarized-dark"] .form-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
    }
    
    body[data-theme="synth-wave"] .form-card {
        background: rgba(255, 0, 110, 0.1);
        border: 1px solid #ff006e;
        color: #ffffff;
    }
    
    /* Perbaiki form labels untuk synth-wave theme */
    body[data-theme="synth-wave"] .form-card .form-label {
        color: #ff006e !important;
        background: transparent !important;
        border: none !important;
        text-shadow: 0 0 5px #ff006e;
        font-weight: 600;
    }
    
    body[data-theme="synth-wave"] .form-card .form-control,
    body[data-theme="synth-wave"] .form-card .form-select {
        color: #ffffff !important;
        background: rgba(0, 0, 0, 0.3) !important;
        border: 1px solid #ff006e !important;
    }
    
    body[data-theme="synth-wave"] .form-card .form-control::placeholder {
        color: #cccccc !important;
    }
    
    /* Theme support untuk h4 */
    body[data-theme="solarized-dark"] h4 {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] h4 {
        color: #ff006e !important;
        text-shadow: 0 0 10px #ff006e;
    }
    
    .color-picker {
        width: 50px;
        height: 40px;
        border-radius: 8px;
        border: 2px solid #ddd;
        cursor: pointer;
    }
    
    /* Theme support untuk color picker */
    body[data-theme="synth-wave"] .color-picker {
        border: 2px solid #ff006e !important;
        box-shadow: 0 0 10px rgba(255, 0, 110, 0.3) !important;
    }
    
    .icon-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
        gap: 12px;
        max-height: 350px;
        overflow-y: auto;
        padding: 15px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        border: 2px dashed rgba(220, 53, 69, 0.3);
    }
    
    .icon-option {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
        position: relative;
    }
    
    .icon-option i {
        font-size: 24px !important;
        font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome" !important;
        font-weight: 900 !important;
        color: #dc3545;
        transition: all 0.3s;
        display: inline-block !important;
        line-height: 1 !important;
        width: 24px;
        text-align: center;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-rendering: auto !important;
    }
    
    /* Fallback emoji untuk expense icons */
    .icon-option .fa-shopping-cart::before { content: "\\f07a"; }
    .icon-option .fa-utensils::before { content: "\\f2e7"; }
    .icon-option .fa-gas-pump::before { content: "\\f52f"; }
    .icon-option .fa-home::before { content: "\\f015"; }
    .icon-option .fa-hospital::before { content: "\\f0f8"; }
    .icon-option .fa-graduation-cap::before { content: "\\f19d"; }
    .icon-option .fa-bus::before { content: "\\f207"; }
    .icon-option .fa-film::before { content: "\\f008"; }
    .icon-option .fa-shirt::before { content: "\\f553"; }
    .icon-option .fa-gift::before { content: "\\f06b"; }
    .icon-option .fa-phone::before { content: "\\f095"; }
    .icon-option .fa-tools::before { content: "\\f7d9"; }
    .icon-option .fa-heart::before { content: "\\f004"; }
    .icon-option .fa-paw::before { content: "\\f1b0"; }
    .icon-option .fa-credit-card::before { content: "\\f09d"; }
    
    /* FontAwesome fallback untuk emoji */
    body.fa-fallback .icon-option i::before {
        font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI Symbol", "Noto Emoji" !important;
        font-weight: normal !important;
        font-size: 20px !important;
    }
    
    /* Fallback emoji untuk expense icons - Makanan & Minuman */
    body.fa-fallback .icon-option .fa-utensils::before { content: "🍽️"; }
    body.fa-fallback .icon-option .fa-coffee::before { content: "☕"; }
    body.fa-fallback .icon-option .fa-wine-glass::before { content: "🍷"; }
    body.fa-fallback .icon-option .fa-birthday-cake::before { content: "🎂"; }
    body.fa-fallback .icon-option .fa-pizza-slice::before { content: "🍕"; }
    body.fa-fallback .icon-option .fa-hamburger::before { content: "🍔"; }
    body.fa-fallback .icon-option .fa-ice-cream::before { content: "🍦"; }
    body.fa-fallback .icon-option .fa-apple-alt::before { content: "🍎"; }
    
    /* Transportasi */
    body.fa-fallback .icon-option .fa-car::before { content: "�"; }
    body.fa-fallback .icon-option .fa-motorcycle::before { content: "🏍️"; }
    body.fa-fallback .icon-option .fa-bus::before { content: "🚌"; }
    body.fa-fallback .icon-option .fa-train::before { content: "🚆"; }
    body.fa-fallback .icon-option .fa-plane::before { content: "✈️"; }
    body.fa-fallback .icon-option .fa-bicycle::before { content: "🚲"; }
    body.fa-fallback .icon-option .fa-taxi::before { content: "🚕"; }
    body.fa-fallback .icon-option .fa-gas-pump::before { content: "⛽"; }
    body.fa-fallback .icon-option .fa-parking::before { content: "🅿️"; }
    body.fa-fallback .icon-option .fa-ship::before { content: "🚢"; }
    body.fa-fallback .icon-option .fa-truck::before { content: "🚛"; }
    body.fa-fallback .icon-option .fa-subway::before { content: "🚇"; }
    
    /* Belanja */
    body.fa-fallback .icon-option .fa-shopping-cart::before { content: "🛒"; }
    body.fa-fallback .icon-option .fa-shopping-bag::before { content: "🛍️"; }
    body.fa-fallback .icon-option .fa-store::before { content: "�"; }
    body.fa-fallback .icon-option .fa-tshirt::before { content: "👕"; }
    body.fa-fallback .icon-option .fa-shirt::before { content: "👕"; }
    body.fa-fallback .icon-option .fa-shoe-prints::before { content: "👟"; }
    body.fa-fallback .icon-option .fa-glasses::before { content: "👓"; }
    body.fa-fallback .icon-option .fa-watch::before { content: "⌚"; }
    body.fa-fallback .icon-option .fa-gem::before { content: "💎"; }
    body.fa-fallback .icon-option .fa-ring::before { content: "💍"; }
    body.fa-fallback .icon-option .fa-handbag::before { content: "👜"; }
    
    /* Rumah & Utilitas */
    body.fa-fallback .icon-option .fa-home::before { content: "🏠"; }
    body.fa-fallback .icon-option .fa-bolt::before { content: "⚡"; }
    body.fa-fallback .icon-option .fa-water::before { content: "💧"; }
    body.fa-fallback .icon-option .fa-wifi::before { content: "📶"; }
    body.fa-fallback .icon-option .fa-phone::before { content: "📱"; }
    body.fa-fallback .icon-option .fa-tv::before { content: "📺"; }
    body.fa-fallback .icon-option .fa-couch::before { content: "🛋️"; }
    body.fa-fallback .icon-option .fa-bed::before { content: "🛏️"; }
    body.fa-fallback .icon-option .fa-bath::before { content: "🛁"; }
    body.fa-fallback .icon-option .fa-hammer::before { content: "🔨"; }
    body.fa-fallback .icon-option .fa-tools::before { content: "🔧"; }
    body.fa-fallback .icon-option .fa-paint-roller::before { content: "�"; }
    
    /* Kesehatan & Kecantikan */
    body.fa-fallback .icon-option .fa-medkit::before { content: "🩹"; }
    body.fa-fallback .icon-option .fa-user-md::before { content: "👨‍⚕️"; }
    body.fa-fallback .icon-option .fa-hospital::before { content: "🏥"; }
    body.fa-fallback .icon-option .fa-pills::before { content: "💊"; }
    body.fa-fallback .icon-option .fa-syringe::before { content: "💉"; }
    body.fa-fallback .icon-option .fa-heartbeat::before { content: "💓"; }
    body.fa-fallback .icon-option .fa-eye::before { content: "👁️"; }
    body.fa-fallback .icon-option .fa-tooth::before { content: "🦷"; }
    body.fa-fallback .icon-option .fa-spa::before { content: "🧖‍♀️"; }
    body.fa-fallback .icon-option .fa-cut::before { content: "✂️"; }
    
    /* Hiburan & Rekreasi */
    body.fa-fallback .icon-option .fa-gamepad::before { content: "🎮"; }
    body.fa-fallback .icon-option .fa-film::before { content: "🎬"; }
    body.fa-fallback .icon-option .fa-music::before { content: "🎵"; }
    body.fa-fallback .icon-option .fa-headphones::before { content: "🎧"; }
    body.fa-fallback .icon-option .fa-camera::before { content: "📷"; }
    body.fa-fallback .icon-option .fa-guitar::before { content: "�"; }
    body.fa-fallback .icon-option .fa-microphone::before { content: "🎤"; }
    body.fa-fallback .icon-option .fa-ticket-alt::before { content: "🎫"; }
    body.fa-fallback .icon-option .fa-bowling-ball::before { content: "🎳"; }
    body.fa-fallback .icon-option .fa-football-ball::before { content: "🏈"; }
    body.fa-fallback .icon-option .fa-basketball-ball::before { content: "🏀"; }
    body.fa-fallback .icon-option .fa-swimming-pool::before { content: "🏊‍♂️"; }
    
    /* Pendidikan & Buku */
    body.fa-fallback .icon-option .fa-book::before { content: "📚"; }
    body.fa-fallback .icon-option .fa-graduation-cap::before { content: "�🎓"; }
    body.fa-fallback .icon-option .fa-university::before { content: "🏛️"; }
    body.fa-fallback .icon-option .fa-pen::before { content: "✒️"; }
    body.fa-fallback .icon-option .fa-pencil-alt::before { content: "✏️"; }
    body.fa-fallback .icon-option .fa-calculator::before { content: "🧮"; }
    body.fa-fallback .icon-option .fa-microscope::before { content: "�"; }
    body.fa-fallback .icon-option .fa-flask::before { content: "⚗️"; }
    body.fa-fallback .icon-option .fa-chalkboard-teacher::before { content: "👨‍🏫"; }
    
    /* Olahraga & Fitness */
    body.fa-fallback .icon-option .fa-dumbbell::before { content: "🏋️"; }
    body.fa-fallback .icon-option .fa-running::before { content: "�‍♂️"; }
    body.fa-fallback .icon-option .fa-swimmer::before { content: "🏊‍♂️"; }
    body.fa-fallback .icon-option .fa-walking::before { content: "�‍♂️"; }
    body.fa-fallback .icon-option .fa-skiing::before { content: "⛷️"; }
    body.fa-fallback .icon-option .fa-horse::before { content: "🐎"; }
    body.fa-fallback .icon-option .fa-table-tennis::before { content: "�"; }
    body.fa-fallback .icon-option .fa-golf-ball::before { content: "⛳"; }
    body.fa-fallback .icon-option .fa-volleyball-ball::before { content: "🏐"; }
    
    /* Keuangan & Asuransi */
    body.fa-fallback .icon-option .fa-credit-card::before { content: "�"; }
    body.fa-fallback .icon-option .fa-money-bill::before { content: "💵"; }
    body.fa-fallback .icon-option .fa-coins::before { content: "🪙"; }
    body.fa-fallback .icon-option .fa-shield-alt::before { content: "�️"; }
    body.fa-fallback .icon-option .fa-file-invoice-dollar::before { content: "🧾"; }
    
    /* Lainnya */
    body.fa-fallback .icon-option .fa-gift::before { content: "🎁"; }
    body.fa-fallback .icon-option .fa-heart::before { content: "❤️"; }
    body.fa-fallback .icon-option .fa-paw::before { content: "🐾"; }
    body.fa-fallback .icon-option .fa-seedling::before { content: "🌱"; }
    body.fa-fallback .icon-option .fa-recycle::before { content: "♻️"; }
    body.fa-fallback .icon-option .fa-envelope::before { content: "✉️"; }
    body.fa-fallback .icon-option .fa-newspaper::before { content: "📰"; }
    body.fa-fallback .icon-option .fa-clipboard-list::before { content: "📋"; }
    body.fa-fallback .icon-option .fa-calendar::before { content: "📅"; }
    body.fa-fallback .icon-option .fa-clock::before { content: "�"; }
    
    /* Theme support untuk icon options */
    body[data-theme="synth-wave"] .icon-option {
        background: rgba(0, 0, 0, 0.3) !important;
        border: 1px solid #ff006e !important;
        color: #ff006e !important;
    }
    
    body[data-theme="synth-wave"] .icon-option i {
        color: #ff006e !important;
    }
    
    body[data-theme="synth-wave"] .icon-option:hover,
    body[data-theme="synth-wave"] .icon-option.selected {
        border-color: #00ffff !important;
        background: rgba(0, 255, 255, 0.3) !important;
        color: #ffffff !important;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5) !important;
    }
    
    body[data-theme="synth-wave"] .icon-option:hover i,
    body[data-theme="synth-wave"] .icon-option.selected i {
        color: #ffffff !important;
    }
    
    .icon-option:hover,
    .icon-option.selected {
        background: rgba(220, 53, 69, 0.3);
        border-color: #dc3545;
        transform: scale(1.1);
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.4);
    }
    
    .icon-option:hover i,
    .icon-option.selected i {
        color: #fff !important;
        text-shadow: 0 0 8px rgba(220, 53, 69, 0.8);
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border: none;
        border-radius: 15px;
        padding: 12px 30px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        color: white;
    }
    
    .btn-cancel {
        border: 2px solid #6c757d;
        border-radius: 15px;
        padding: 10px 30px;
        color: #6c757d;
        background: transparent;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #6c757d;
        color: white;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border: none;
        border-radius: 15px;
        padding: 12px 30px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        color: white;
    }
    
    .stats-info {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('dashboard') }}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ route('categories.expense.index') }}" class="text-decoration-none">
                    <i class="fas fa-tags me-1"></i>Kategori Pengeluaran
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-edit me-1"></i>Edit {{ $category->name }}
            </li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Category Stats -->
            <div class="stats-info">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="fw-bold fs-4">{{ $category->transactions->count() }}</div>
                        <div class="small">Total Transaksi</div>
                    </div>
                    <div class="col-md-4">
                        <div class="fw-bold fs-4">{{ $category->is_active ? 'Aktif' : 'Nonaktif' }}</div>
                        <div class="small">Status Kategori</div>
                    </div>
                    <div class="col-md-4">
                        <div class="fw-bold fs-4">Rp {{ number_format($category->transactions->sum('amount'), 0, ',', '.') }}</div>
                        <div class="small">Total Pengeluaran</div>
                    </div>
                </div>
            </div>
            
            <div class="form-card">
                <div class="card-header bg-transparent border-0 p-4">
                    <h4 class="mb-0">
                        <i class="fas fa-arrow-down text-danger me-2"></i>
                        Edit Kategori Pengeluaran: {{ $category->name }}
                    </h4>
                </div>
                
                <div class="card-body p-4">
                    <form action="{{ route('categories.update', $category->id) }}" method="POST" id="categoryForm">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- Nama Kategori -->
                            <div class="col-md-12 mb-3">
                                <label for="name" class="form-label fw-bold">Nama Kategori *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $category->name) }}" 
                                       placeholder="Misal: Makanan, Transport, Belanja..." required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Deskripsi -->
                        <div class="mb-3">
                            <label for="description" class="form-label fw-bold">Deskripsi</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Deskripsi singkat tentang kategori ini...">{{ old('description', $category->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <!-- Icon -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Pilih Icon *</label>
                                <input type="hidden" name="icon" id="selectedIcon" value="{{ old('icon', $category->icon) }}">
                                <div class="icon-grid">
                                    @php
                                    $expenseIcons = [
                                        // Makanan & Minuman
                                        'fas fa-utensils', 'fas fa-coffee', 'fas fa-wine-glass', 'fas fa-birthday-cake',
                                        'fas fa-pizza-slice', 'fas fa-hamburger', 'fas fa-ice-cream', 'fas fa-apple-alt',
                                        
                                        // Transportasi
                                        'fas fa-car', 'fas fa-motorcycle', 'fas fa-bus', 'fas fa-train',
                                        'fas fa-plane', 'fas fa-bicycle', 'fas fa-taxi', 'fas fa-gas-pump',
                                        'fas fa-parking', 'fas fa-ship', 'fas fa-truck', 'fas fa-subway',
                                        
                                        // Belanja
                                        'fas fa-shopping-cart', 'fas fa-shopping-bag', 'fas fa-store', 'fas fa-tshirt',
                                        'fas fa-shoe-prints', 'fas fa-glasses', 'fas fa-watch', 'fas fa-gem',
                                        'fas fa-ring', 'fas fa-handbag',
                                        
                                        // Rumah & Utilitas
                                        'fas fa-home', 'fas fa-bolt', 'fas fa-water', 'fas fa-wifi',
                                        'fas fa-phone', 'fas fa-tv', 'fas fa-couch', 'fas fa-bed',
                                        'fas fa-bath', 'fas fa-hammer', 'fas fa-tools', 'fas fa-paint-roller',
                                        
                                        // Kesehatan & Kecantikan
                                        'fas fa-medkit', 'fas fa-user-md', 'fas fa-hospital',
                                        'fas fa-pills', 'fas fa-syringe', 'fas fa-heartbeat',
                                        'fas fa-eye', 'fas fa-tooth', 'fas fa-spa', 'fas fa-cut',
                                        
                                        // Hiburan & Rekreasi
                                        'fas fa-gamepad', 'fas fa-film', 'fas fa-music',
                                        'fas fa-headphones', 'fas fa-camera', 'fas fa-guitar',
                                        'fas fa-microphone', 'fas fa-ticket-alt', 'fas fa-bowling-ball',
                                        'fas fa-football-ball', 'fas fa-basketball-ball', 'fas fa-swimming-pool',
                                        
                                        // Pendidikan & Buku
                                        'fas fa-book', 'fas fa-graduation-cap', 'fas fa-university',
                                        'fas fa-pen', 'fas fa-pencil-alt', 'fas fa-calculator',
                                        'fas fa-microscope', 'fas fa-flask', 'fas fa-chalkboard-teacher',
                                        
                                        // Olahraga & Fitness
                                        'fas fa-dumbbell', 'fas fa-running', 'fas fa-swimmer',
                                        'fas fa-walking', 'fas fa-skiing', 'fas fa-horse',
                                        'fas fa-table-tennis', 'fas fa-golf-ball', 'fas fa-volleyball-ball',
                                        
                                        // Keuangan & Asuransi
                                        'fas fa-credit-card', 'fas fa-money-bill', 'fas fa-coins',
                                        'fas fa-shield-alt', 'fas fa-file-invoice-dollar',
                                        
                                        // Lainnya
                                        'fas fa-gift', 'fas fa-heart', 'fas fa-paw', 'fas fa-seedling',
                                        'fas fa-recycle', 'fas fa-envelope', 'fas fa-newspaper',
                                        'fas fa-clipboard-list', 'fas fa-calendar', 'fas fa-clock'
                                    ];
                                    @endphp
                                    @foreach($expenseIcons as $icon)
                                        <div class="icon-option {{ old('icon', $category->icon) === $icon ? 'selected' : '' }}" 
                                             data-icon="{{ $icon }}">
                                            <i class="{{ $icon }} fa-lg"></i>
                                        </div>
                                    @endforeach
                                </div>
                                @error('icon')
                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Color -->
                            <div class="col-md-6 mb-3">
                                <label for="color" class="form-label fw-bold">Warna *</label>
                                <div class="d-flex align-items-center gap-3">
                                    <input type="color" class="color-picker @error('color') is-invalid @enderror" 
                                           id="color" name="color" value="{{ old('color', $category->color) }}">
                                    <span class="small text-muted">Pilih warna untuk kategori</span>
                                </div>
                                @error('color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Budget Settings -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="budget_limit" class="form-label fw-bold">Batas Anggaran</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="number" class="form-control @error('budget_limit') is-invalid @enderror" 
                                           id="budget_limit" name="budget_limit" value="{{ old('budget_limit', $category->budget_limit) }}" 
                                           min="0" step="1000" placeholder="0">
                                </div>
                                <small class="text-muted">Tetapkan batas maksimal pengeluaran untuk kategori ini</small>
                                @error('budget_limit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="budget_period" class="form-label fw-bold">Periode Anggaran</label>
                                <select class="form-select @error('budget_period') is-invalid @enderror" id="budget_period" name="budget_period">
                                    <option value="monthly" {{ old('budget_period', $category->budget_period) === 'monthly' ? 'selected' : '' }}>Bulanan</option>
                                    <option value="weekly" {{ old('budget_period', $category->budget_period) === 'weekly' ? 'selected' : '' }}>Mingguan</option>
                                    <option value="daily" {{ old('budget_period', $category->budget_period) === 'daily' ? 'selected' : '' }}>Harian</option>
                                    <option value="yearly" {{ old('budget_period', $category->budget_period) === 'yearly' ? 'selected' : '' }}>Tahunan</option>
                                </select>
                                @error('budget_period')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <div>
                                <a href="{{ route('categories.expense.index') }}" class="btn btn-cancel">
                                    <i class="fas fa-arrow-left me-2"></i>Kembali
                                </a>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-delete" onclick="deleteCategory()">
                                    <i class="fas fa-trash me-2"></i>Hapus
                                </button>
                                <button type="submit" class="btn btn-submit">
                                    <i class="fas fa-save me-2"></i>Update Kategori
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if FontAwesome is loaded
    function isFontAwesomeLoaded() {
        const testElement = document.createElement('i');
        testElement.className = 'fas fa-heart';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement, ':before');
        const content = computedStyle.getPropertyValue('content');
        
        document.body.removeChild(testElement);
        
        // FontAwesome loaded if content is not "none" or empty
        return content && content !== 'none' && content !== '""';
    }
    
    // Add fallback class if FontAwesome is not loaded
    if (!isFontAwesomeLoaded()) {
        console.log('FontAwesome not detected, using emoji fallbacks');
        document.body.classList.add('fa-fallback');
    }

    // Icon selection
    document.querySelectorAll('.icon-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            document.querySelectorAll('.icon-option').forEach(opt => opt.classList.remove('selected'));
            
            // Add selected class to clicked option
            this.classList.add('selected');
            
            // Update hidden input
            document.getElementById('selectedIcon').value = this.dataset.icon;
            
            console.log('Selected icon:', this.dataset.icon);
        });
    });

    // Form validation
    document.getElementById('categoryForm').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const icon = document.getElementById('selectedIcon').value;

        if (!name) {
            e.preventDefault();
            Swal.fire({
                title: 'Error!',
                text: 'Nama kategori harus diisi!',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            return;
        }

        if (!icon) {
            e.preventDefault();
            Swal.fire({
                title: 'Error!',
                text: 'Silakan pilih icon untuk kategori!',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Show loading
        Swal.fire({
            title: 'Menyimpan...',
            text: 'Sedang mengupdate kategori',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    });
});

function deleteCategory() {
    const categoryId = {{ $category->id }};
    const categoryName = "{{ $category->name }}";
    const transactionCount = {{ $category->transactions->count() }};
    
    if (transactionCount > 0) {
        Swal.fire({
            title: 'Tidak dapat menghapus!',
            html: `Kategori <strong>${categoryName}</strong> masih memiliki <strong>${transactionCount}</strong> transaksi.<br><br>Hapus atau pindahkan transaksi terlebih dahulu.`,
            icon: 'warning',
            confirmButtonText: 'Mengerti'
        });
        return;
    }
    
    Swal.fire({
        title: 'Hapus Kategori?',
        html: `Apakah Anda yakin ingin menghapus kategori <strong>${categoryName}</strong>?<br><br><small class="text-muted">Tindakan ini tidak dapat dibatalkan!</small>`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus kategori',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Send delete request
            fetch(`/categories/${categoryId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        window.location.href = data.redirect;
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menghapus kategori.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}
</script>
@endpush
