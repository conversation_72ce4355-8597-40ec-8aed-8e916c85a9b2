@extends('layouts.dashboard')

@section('title', 'Grafik Trend')

@section('page-title', 'Grapich Update')

@push('styles')
<style>
    .trend-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
        transition: all 0.3s ease;
        overflow: hidden;
    }
    
    .trend-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    }
    
    .chart-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-bottom: none;
    }
    
    .chart-header h5 {
        margin: 0;
        font-weight: 600;
    }
    
    .chart-body {
        padding: 1.5rem;
    }
    
    .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
    }
    
    .chart-container.large {
        height: 500px;
    }
    
    .trend-filter {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        margin-bottom: 2rem;
    }
    
    .filter-group {
        margin-bottom: 1rem;
    }
    
    .filter-group:last-child {
        margin-bottom: 0;
    }
    
    .filter-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .filter-select {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        color: #495057;
        width: 100%;
    }
    
    .filter-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }
    
    .trend-indicator {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
        margin-right: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .trend-up {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }
    
    .trend-down {
        background-color: rgba(220, 53, 69, 0.2);
        color: #dc3545;
    }
    
    .trend-stable {
        background-color: rgba(255, 193, 7, 0.2);
        color: #ffc107;
    }
    
    .summary-stats {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .period-selector {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 50px;
        padding: 0.5rem;
        display: flex;
        margin-bottom: 1rem;
    }
    
    .period-btn {
        flex: 1;
        background: transparent;
        border: none;
        padding: 0.75rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        color: #6c757d;
        transition: all 0.3s ease;
    }
    
    .period-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        border-radius: 15px;
    }
    
    .export-toolbar {
        background: white;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .export-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin-left: 0.5rem;
    }
    
    .export-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.25rem 0.5rem rgba(40, 167, 69, 0.3);
        color: white;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-chart-line mr-2 text-primary"></i>
                        Grafik Trend
                    </h1>
                    <p class="text-muted mb-0">Analisis tren keuangan dan prediksi masa depan</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Toolbar -->
    <div class="export-toolbar">
        <div>
            <span class="font-weight-bold text-muted">Periode Analisis:</span>
            <span id="currentPeriodDisplay" class="ml-2 text-primary font-weight-bold">12 Bulan Terakhir</span>
        </div>
        <div>
            <button class="btn export-btn" onclick="exportTrendChart('png')">
                <i class="fas fa-image mr-2"></i>Export PNG
            </button>
            <button class="btn export-btn" onclick="exportTrendChart('pdf')">
                <i class="fas fa-file-pdf mr-2"></i>Export PDF
            </button>
        </div>
    </div>

    <!-- Filter Panel -->
    <div class="trend-filter">
        <div class="row">
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="filter-label">Periode Analisis</label>
                    <div class="period-selector">
                        <button class="period-btn active" data-period="6">6 Bulan</button>
                        <button class="period-btn" data-period="12">12 Bulan</button>
                        <button class="period-btn" data-period="24">24 Bulan</button>
                        <button class="period-btn" data-period="36">36 Bulan</button>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="filter-label">Jenis Transaksi</label>
                    <select id="transactionType" class="filter-select">
                        <option value="all">Semua Transaksi</option>
                        <option value="income">Pemasukan Saja</option>
                        <option value="expense">Pengeluaran Saja</option>
                        <option value="net">Saldo Bersih</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="filter-label">Kategori</label>
                    <select id="categoryFilter" class="filter-select">
                        <option value="all">Semua Kategori</option>
                        <!-- Will be populated dynamically -->
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="filter-label">Akun</label>
                    <select id="accountFilter" class="filter-select">
                        <option value="all">Semua Akun</option>
                        <!-- Will be populated dynamically -->
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="summary-stats" id="summaryStats" style="display: none;">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value" id="avgMonthly">Rp 0</div>
                    <div class="stat-label">Rata-rata Bulanan</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value" id="trendPercentage">0%</div>
                    <div class="stat-label">Perubahan Trend</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value" id="volatility">0%</div>
                    <div class="stat-label">Volatilitas</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value" id="prediction">Rp 0</div>
                    <div class="stat-label">Prediksi Bulan Depan</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Trend Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="trend-card">
                <div class="chart-header">
                    <h5><i class="fas fa-chart-line mr-2"></i>Trend Keuangan</h5>
                    <div class="mt-2">
                        <span id="trendIndicator" class="trend-indicator trend-stable">
                            <i class="fas fa-minus mr-2"></i>Memuat Data...
                        </span>
                    </div>
                </div>
                <div class="chart-body">
                    <div class="chart-container large" style="position: relative;">
                        <div id="mainChartLoading" class="loading-overlay" style="display: none;">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p class="mt-2 text-muted">Memuat data trend...</p>
                            </div>
                        </div>
                        <canvas id="mainTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Charts -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="trend-card">
                <div class="chart-header">
                    <h5><i class="fas fa-chart-area mr-2"></i>Distribusi Kategori</h5>
                </div>
                <div class="chart-body">
                    <div class="chart-container" style="position: relative;">
                        <div id="categoryChartLoading" class="loading-overlay" style="display: none;">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                        <canvas id="categoryTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="trend-card">
                <div class="chart-header">
                    <h5><i class="fas fa-chart-bar mr-2"></i>Perbandingan Periode</h5>
                </div>
                <div class="chart-body">
                    <div class="chart-container" style="position: relative;">
                        <div id="comparisonChartLoading" class="loading-overlay" style="display: none;">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                        <canvas id="comparisonChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Growth Analysis -->
    <div class="row">
        <div class="col-md-6">
            <div class="trend-card">
                <div class="chart-header">
                    <h5><i class="fas fa-percentage mr-2"></i>Analisis Pertumbuhan</h5>
                </div>
                <div class="chart-body">
                    <div class="chart-container" style="position: relative;">
                        <div id="growthChartLoading" class="loading-overlay" style="display: none;">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                        <canvas id="growthChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="trend-card">
                <div class="chart-header">
                    <h5><i class="fas fa-crystal-ball mr-2"></i>Prediksi Trend</h5>
                </div>
                <div class="chart-body">
                    <div class="chart-container" style="position: relative;">
                        <div id="predictionChartLoading" class="loading-overlay" style="display: none;">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                        <canvas id="predictionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let charts = {};
    let currentPeriod = 12;
    let currentFilters = {
        type: 'all',
        category: 'all',
        account: 'all'
    };
    
    // Initialize
    initializeCharts();
    loadFilters();
    loadTrendData();
    
    // Event listeners
    setupEventListeners();
    
    function initializeCharts() {
        // Main Trend Chart
        const mainCtx = document.getElementById('mainTrendChart').getContext('2d');
        charts.main = new Chart(mainCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + formatCurrency(context.parsed.y);
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Periode'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Jumlah (Rupiah)'
                        },
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
        
        // Category Trend Chart
        const categoryCtx = document.getElementById('categoryTrendChart').getContext('2d');
        charts.category = new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: [
                        '#667eea', '#764ba2', '#f093fb', '#f5576c',
                        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                        '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
        
        // Comparison Chart
        const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
        charts.comparison = new Chart(comparisonCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Perbandingan',
                    data: [],
                    backgroundColor: 'rgba(75, 192, 192, 0.6)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
        
        // Growth Chart
        const growthCtx = document.getElementById('growthChart').getContext('2d');
        charts.growth = new Chart(growthCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Pertumbuhan (%)',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
        
        // Prediction Chart
        const predictionCtx = document.getElementById('predictionChart').getContext('2d');
        charts.prediction = new Chart(predictionCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    }
    
    function setupEventListeners() {
        // Period selector
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.period-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentPeriod = parseInt(this.dataset.period);
                updatePeriodDisplay();
                loadTrendData();
            });
        });
        
        // Filter changes
        document.getElementById('transactionType').addEventListener('change', function() {
            currentFilters.type = this.value;
            loadTrendData();
        });
        
        document.getElementById('categoryFilter').addEventListener('change', function() {
            currentFilters.category = this.value;
            loadTrendData();
        });
        
        document.getElementById('accountFilter').addEventListener('change', function() {
            currentFilters.account = this.value;
            loadTrendData();
        });
    }
    
    function loadFilters() {
        // Load categories
        fetch('/emergency/api/categories', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                const categorySelect = document.getElementById('categoryFilter');
                data.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });
            });
        
        // Load accounts
        fetch('/emergency/api/accounts', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                const accountSelect = document.getElementById('accountFilter');
                data.forEach(account => {
                    const option = document.createElement('option');
                    option.value = account.id;
                    option.textContent = account.name;
                    accountSelect.appendChild(option);
                });
            });
    }
    
    function loadTrendData() {
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - currentPeriod);
        const endDate = new Date();
        
        const params = new URLSearchParams({
            start_date: startDate.toISOString().split('T')[0],
            end_date: endDate.toISOString().split('T')[0],
            period: 'monthly',
            category_id: currentFilters.category !== 'all' ? currentFilters.category : '',
            account_id: currentFilters.account !== 'all' ? currentFilters.account : ''
        });
        
        console.log('Loading trends data with params:', params.toString()); // Debug
        
        fetch(`{{ route('reports.api.trends') }}?${params}`, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
            .then(response => {
                console.log('API Response status:', response.status); // Debug
                return response.json();
            })
            .then(data => {
                console.log('Trends data received:', data); // Debug
                console.log('Data structure:', {
                    success: data.success,
                    hasData: !!data.data,
                    hasTrend: !!(data.data && data.data.trend),
                    hasComparison: !!(data.data && data.data.comparison), 
                    hasGrowth: !!(data.data && data.data.growth),
                    hasPrediction: !!(data.data && data.data.prediction)
                }); // Debug
                
                if (data.success) {
                    updateCharts(data.data);
                    updateSummaryStats(data.data.summary);
                    updateTrendIndicator(data.data.summary.trend);
                } else {
                    console.error('API returned error:', data);
                    showError('Gagal memuat data trend');
                }
            })
            .catch(error => {
                console.error('Error loading trends:', error);
                showError('Terjadi kesalahan saat memuat data');
            });
    }
    
    function updateCharts(data) {
        console.log('updateCharts called with:', data); // Debug
        
        // Update main trend chart
        if (data.trend) {
            console.log('Updating main chart with trend data:', data.trend); // Debug
            updateMainChart(data.trend);
        } else {
            console.warn('No trend data available'); // Debug
        }
        
        // Update category chart
        if (data.categories) {
            console.log('Updating category chart with data:', data.categories); // Debug
            updateCategoryChart(data.categories);
        } else {
            console.warn('No categories data available'); // Debug
        }
        
        // Update comparison chart
        if (data.comparison) {
            console.log('Updating comparison chart with data:', data.comparison); // Debug
            updateComparisonChart(data.comparison);
        } else {
            console.warn('No comparison data available'); // Debug
            // Provide default data if none available
            updateComparisonChart({
                labels: ['Tidak ada data'],
                data: [0]
            });
        }
        
        // Update growth chart
        if (data.growth) {
            console.log('Updating growth chart with data:', data.growth); // Debug
            updateGrowthChart(data.growth);
        } else {
            console.warn('No growth data available'); // Debug
            // Provide default data if none available
            updateGrowthChart({
                income_growth: 0,
                expense_growth: 0,
                net_growth: 0
            });
        }
        
        // Update prediction chart
        if (data.prediction) {
            console.log('Updating prediction chart with data:', data.prediction); // Debug
            updatePredictionChart(data.prediction);
        } else {
            console.warn('No prediction data available'); // Debug
            // Provide default data if none available
            updatePredictionChart({
                predicted_income: 0,
                predicted_expense: 0
            });
        }
    }
    
    function updateMainChart(trendData) {
        if (!trendData || !trendData.labels || !trendData.datasets) {
            console.warn('Invalid trend data:', trendData);
            return;
        }
        
        const chart = charts.main;
        if (!chart) {
            console.warn('Main chart not initialized');
            return;
        }
        
        chart.data.labels = trendData.labels || [];
        chart.data.datasets = (trendData.datasets || []).map((dataset, index) => ({
            ...dataset,
            data: dataset.data || [],
            borderColor: ['#28a745', '#dc3545', '#007bff'][index],
            backgroundColor: ['rgba(40, 167, 69, 0.1)', 'rgba(220, 53, 69, 0.1)', 'rgba(0, 123, 255, 0.1)'][index],
            fill: true,
            tension: 0.4
        }));
        chart.update();
    }
    
    function updateCategoryChart(categoryData) {
        if (!categoryData) {
            console.warn('Invalid category data:', categoryData);
            return;
        }
        
        const chart = charts.category;
        if (!chart) {
            console.warn('Category chart not initialized');
            return;
        }
        
        // Convert categories object to labels and data arrays
        const expenseCategories = categoryData.expense || {};
        const labels = Object.keys(expenseCategories);
        const data = Object.values(expenseCategories);
        
        chart.data.labels = labels;
        
        // Ensure datasets array exists and has at least one dataset
        if (!chart.data.datasets || chart.data.datasets.length === 0) {
            chart.data.datasets = [{
                data: data,
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }];
        } else {
            chart.data.datasets[0].data = data;
        }
        
        chart.update();
    }
    
    function updateComparisonChart(comparisonData) {
        console.log('updateComparisonChart called with:', comparisonData); // Debug
        
        const chart = charts.comparison;
        if (!chart) {
            console.warn('Comparison chart not initialized');
            return;
        }
        
        // Handle empty or invalid data
        if (!comparisonData || !comparisonData.labels || comparisonData.labels.length === 0) {
            console.log('Using default comparison data'); // Debug
            chart.data.labels = ['Pemasukan', 'Pengeluaran'];
            chart.data.datasets[0].data = [0, 0];
        } else {
            chart.data.labels = comparisonData.labels;
            chart.data.datasets[0].data = comparisonData.data || [];
        }
        
        chart.update();
    }
    
    function updateGrowthChart(growthData) {
        console.log('updateGrowthChart called with:', growthData); // Debug
        
        const chart = charts.growth;
        if (!chart) {
            console.warn('Growth chart not initialized');
            return;
        }
        
        // Convert growth object to chart format
        const labels = ['Pemasukan', 'Pengeluaran', 'Bersih'];
        const data = [
            growthData ? (growthData.income_growth || 0) : 0,
            growthData ? (growthData.expense_growth || 0) : 0,
            growthData ? (growthData.net_growth || 0) : 0
        ];
        
        console.log('Growth chart data:', { labels, data }); // Debug
        
        chart.data.labels = labels;
        chart.data.datasets[0].data = data;
        
        chart.update();
    }
    
    function updatePredictionChart(predictionData) {
        console.log('updatePredictionChart called with:', predictionData); // Debug
        
        const chart = charts.prediction;
        if (!chart) {
            console.warn('Prediction chart not initialized');
            return;
        }
        
        // Convert prediction object to chart format
        const labels = ['Bulan Depan'];
        const datasets = [
            {
                label: 'Prediksi Pemasukan',
                data: [predictionData ? (predictionData.predicted_income || 0) : 0],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                fill: false,
                tension: 0.4
            },
            {
                label: 'Prediksi Pengeluaran',
                data: [predictionData ? (predictionData.predicted_expense || 0) : 0],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                borderDash: [5, 5],
                fill: false,
                tension: 0.4
            }
        ];
        
        console.log('Prediction chart data:', { labels, datasets }); // Debug
        
        chart.data.labels = labels;
        chart.data.datasets = datasets;
        chart.update();
    }
    
    function updateSummaryStats(summary) {
        // Update summary statistics with safe defaults
        const avgMonthly = summary.avg_monthly || 0;
        const trendPercentage = summary.trend_percentage || 0;
        const volatility = summary.volatility || 0;
        const prediction = summary.prediction || 0;
        
        document.getElementById('avgMonthly').textContent = formatCurrency(avgMonthly);
        document.getElementById('trendPercentage').textContent = trendPercentage + '%';
        document.getElementById('volatility').textContent = volatility + '%';
        document.getElementById('prediction').textContent = formatCurrency(prediction);
        
        document.getElementById('summaryStats').style.display = 'block';
    }
    
    function updateTrendIndicator(trend) {
        const indicator = document.getElementById('trendIndicator');
        let icon, text, className;
        
        // Handle string trend or object trend
        const direction = typeof trend === 'string' ? trend : (trend.direction || 'stable');
        const percentage = typeof trend === 'object' ? (trend.percentage || 0) : 0;
        
        if (direction === 'up') {
            icon = 'fa-arrow-up';
            text = 'Trend Naik';
            className = 'trend-up';
        } else if (direction === 'down') {
            icon = 'fa-arrow-down';
            text = 'Trend Turun';
            className = 'trend-down';
        } else {
            icon = 'fa-minus';
            text = 'Trend Stabil';
            className = 'trend-stable';
        }
        
        const displayText = percentage !== 0 ? `${text} (${percentage}%)` : text;
        
        if (indicator) {
            indicator.className = `trend-indicator ${className}`;
            indicator.innerHTML = `<i class="fas ${icon} mr-2"></i>${displayText}`;
        }
    }
    
    function updatePeriodDisplay() {
        const periodText = currentPeriod === 6 ? '6 Bulan Terakhir' :
                          currentPeriod === 12 ? '12 Bulan Terakhir' :
                          currentPeriod === 24 ? '24 Bulan Terakhir' :
                          '36 Bulan Terakhir';
        document.getElementById('currentPeriodDisplay').textContent = periodText;
    }
    
    function showLoading(show) {
        const loadingElements = ['mainChartLoading', 'categoryChartLoading', 'comparisonChartLoading', 'growthChartLoading', 'predictionChartLoading'];
        loadingElements.forEach(id => {
            document.getElementById(id).style.display = show ? 'flex' : 'none';
        });
    }
    
    function showError(message) {
        console.error(message);
        // Could implement toast notification here
    }
    
    function formatCurrency(amount) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }
    
    // Export functions
    window.exportTrendChart = function(format) {
        const params = new URLSearchParams({
            period: currentPeriod,
            type: currentFilters.type,
            category: currentFilters.category,
            account: currentFilters.account,
            format: format
        });
        
        window.open(`/api/reports/trends/export?${params}`, '_blank');
    };
});
</script>
@endpush
