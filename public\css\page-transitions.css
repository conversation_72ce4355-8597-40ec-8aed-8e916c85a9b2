/**
 * Ultra Smooth Page Transition CSS
 * CSS untuk transisi slide yang sangat halus tanpa overlay loading
 */

/* Base transition styles */
body.page-transitioning {
    overflow-x: hidden;
    user-select: none;
    pointer-events: none;
}

body.page-transitioning * {
    cursor: wait !important;
}

/* Content container dengan smooth transitions */
.main-content,
.content-wrapper,
.page-content,
main,
.container-fluid {
    will-change: transform, opacity;
}

/* Smooth transition saat slide */
.page-slide-transition {
    transition: transform 450ms cubic-bezier(0.25, 0.8, 0.25, 1), 
                opacity 450ms ease;
}

/* Optimasi performa untuk animasi */
.page-transitioning .main-content,
.page-transitioning .content-wrapper,
.page-transitioning .page-content,
.page-transitioning main,
.page-transitioning .container-fluid {
    backface-visibility: hidden;
    perspective: 1000px;
    transform-style: preserve-3d;
}

/* Disable interactions selama transisi */
.page-transitioning a,
.page-transitioning button,
.page-transitioning input,
.page-transitioning select,
.page-transitioning textarea {
    pointer-events: none;
}

/* Smooth scrolling untuk finalisasi */
html {
    scroll-behavior: smooth;
}

/* Hide scrollbar selama transisi untuk menghindari flickering */
body.page-transitioning::-webkit-scrollbar {
    display: none;
}

body.page-transitioning {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* Menu active states untuk consistency */
.menu-item.active > a,
.submenu-item.active > a {
    background-color: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border-left: 3px solid #667eea;
}

/* Loading state untuk trigger elements */
.loading {
    opacity: 0.7;
    position: relative;
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Optimasi untuk mobile */
@media (max-width: 768px) {
    .page-slide-transition {
        transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1), 
                    opacity 350ms ease;
    }
    
    body.page-transitioning {
        touch-action: none;
    }
}

/* Reduced motion untuk accessibility */
@media (prefers-reduced-motion: reduce) {
    .page-slide-transition {
        transition: opacity 150ms ease;
    }
    
    body.page-transitioning .main-content,
    body.page-transitioning .content-wrapper,
    body.page-transitioning .page-content,
    body.page-transitioning main,
    body.page-transitioning .container-fluid {
        transform: none !important;
    }
}

/* Untuk mencegah flash saat load */
.main-content,
.content-wrapper,
.page-content,
main {
    opacity: 1;
    transform: translateX(0);
}

/* Anti-flicker untuk elemen yang mungkin berubah */
.navbar,
.sidebar,
.footer {
    transform: translateZ(0);
    backface-visibility: hidden;
}
