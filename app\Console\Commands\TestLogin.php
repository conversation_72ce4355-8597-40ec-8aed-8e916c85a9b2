<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class TestLogin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:login {username}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test login process manually';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $username = $this->argument('username');
        
        $user = User::where('username', $username)->first();
        
        if (!$user) {
            $this->error("User '{$username}' not found!");
            return Command::FAILURE;
        }
        
        $this->info("Testing login for user: {$user->username}");
        $this->line("Name: {$user->name}");
        $this->line("Email: {$user->email}");
        $this->line("Is Active: " . ($user->is_active ? 'Yes' : 'No'));
        $this->line("Email Verified: " . ($user->hasVerifiedEmail() ? 'Yes' : 'No'));
        $this->line("Is Locked: " . ($user->isLocked() ? 'Yes' : 'No'));
        $this->line("Password Needs Change: " . ($user->passwordNeedsChange() ? 'Yes' : 'No'));
        $this->line("Has Active Session: " . ($user->hasActiveSession() ? 'Yes' : 'No'));
        
        // Test password
        if ($this->confirm('Test with password "password123"?')) {
            $valid = \Hash::check('password123', $user->password);
            $this->line("Password Valid: " . ($valid ? 'Yes' : 'No'));
        }
        
        return Command::SUCCESS;
    }
}
