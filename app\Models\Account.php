<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Account extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'type',
        'account_number',
        'bank_name',
        'initial_balance',
        'current_balance',
        'currency',
        'icon',
        'color',
        'description',
        'is_active',
        'include_in_total',
        'credit_limit',
        'due_date',
        'interest_rate',
        'metadata'
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'initial_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'credit_limit' => 'decimal:2',
        'interest_rate' => 'decimal:2',
        'due_date' => 'date',
        'is_active' => 'boolean',
        'include_in_total' => 'boolean',
        'metadata' => 'array'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function transfersFrom(): HasMany
    {
        return $this->hasMany(Transaction::class, 'account_id')->where('type', 'transfer');
    }

    public function transfersTo(): HasMany
    {
        return $this->hasMany(Transaction::class, 'to_account_id')->where('type', 'transfer');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeIncludeInTotal($query)
    {
        return $query->where('include_in_total', true);
    }

    // Accessors
    public function getFormattedBalanceAttribute()
    {
        return 'Rp ' . number_format($this->current_balance, 0, ',', '.');
    }

    public function getFormattedInitialBalanceAttribute()
    {
        return 'Rp ' . number_format($this->initial_balance, 0, ',', '.');
    }

    public function getFormattedCreditLimitAttribute()
    {
        if (!$this->credit_limit) return null;
        return 'Rp ' . number_format($this->credit_limit, 0, ',', '.');
    }

    public function getTypeIconAttribute()
    {
        return match($this->type) {
            'cash' => 'fas fa-money-bill-wave',
            'bank' => 'fas fa-university',
            'credit_card' => 'fas fa-credit-card',
            'e_wallet' => 'fas fa-mobile-alt',
            'investment' => 'fas fa-chart-line',
            default => 'fas fa-wallet'
        };
    }

    public function getTypeColorAttribute()
    {
        return match($this->type) {
            'cash' => 'success',
            'bank' => 'primary',
            'credit_card' => 'warning',
            'e_wallet' => 'info',
            'investment' => 'purple',
            default => 'secondary'
        };
    }

    // Helper methods
    public function isCreditCard(): bool
    {
        return $this->type === 'credit_card';
    }

    public function isBank(): bool
    {
        return $this->type === 'bank';
    }

    public function isCash(): bool
    {
        return $this->type === 'cash';
    }

    public function isEWallet(): bool
    {
        return $this->type === 'e_wallet';
    }

    public function isInvestment(): bool
    {
        return $this->type === 'investment';
    }

    public function updateBalance()
    {
        $totalIncome = $this->transactions()
            ->where('type', 'income')
            ->where('status', 'completed')
            ->sum('amount');

        $totalExpense = $this->transactions()
            ->where('type', 'expense')
            ->where('status', 'completed')
            ->sum('amount');

        $transfersIn = $this->transfersTo()
            ->where('status', 'completed')
            ->sum('amount');

        $transfersOut = $this->transfersFrom()
            ->where('status', 'completed')
            ->sum('amount');

        $this->current_balance = $this->initial_balance + $totalIncome - $totalExpense + $transfersIn - $transfersOut;
        $this->save();

        return $this->current_balance;
    }

    public function getBalance(): float
    {
        return $this->current_balance;
    }

    public function canWithdraw(float $amount): bool
    {
        if ($this->isCreditCard()) {
            return ($this->current_balance + $amount) <= $this->credit_limit;
        }
        
        return $this->current_balance >= $amount;
    }

    public function getAvailableBalance(): float
    {
        if ($this->isCreditCard()) {
            return $this->credit_limit - $this->current_balance;
        }
        
        return $this->current_balance;
    }

    protected static function booted()
    {
        // Broadcast dashboard update when account is created, updated, or deleted
        static::created(function ($account) {
            static::broadcastDashboardUpdate($account->user_id);
        });
        
        static::updated(function ($account) {
            static::broadcastDashboardUpdate($account->user_id);
        });
        
        static::deleted(function ($account) {
            static::broadcastDashboardUpdate($account->user_id);
        });
    }
    
    protected static function broadcastDashboardUpdate($userId)
    {
        try {
            $dashboardService = app(\App\Services\DashboardRealtimeService::class);
            
            // Broadcast specific account update
            $dashboardService->broadcastAccountUpdate($userId);
            
            // Broadcast general stats update
            $stats = $dashboardService->getDashboardStats($userId);
            $dashboardService->broadcastStatsUpdate($stats);
        } catch (\Exception $e) {
            \Log::error('Failed to broadcast dashboard update: ' . $e->getMessage());
        }
    }
}
