<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class TestAmountProcessing extends Command
{
    protected $signature = 'test:amount-processing';
    protected $description = 'Test amount processing logic';

    public function handle()
    {
        $testCases = [
            '5.000.000',    // Indonesian format
            '5000000',      // Plain number
            '5,000,000',    // US format with commas
            '5.000.000,50', // Indonesian with decimals
            '5000000.50',   // US format with decimals
        ];
        
        $this->info('Testing amount processing logic:');
        
        foreach ($testCases as $input) {
            $processed = $this->processAmount($input);
            $isNumeric = is_numeric($processed);
            
            $this->line("Input: '$input' -> Processed: '$processed' -> Numeric: " . ($isNumeric ? 'YES' : 'NO'));
        }
    }
    
    private function processAmount($amount)
    {
        // Same logic as controller
        if (strpos($amount, ',') !== false) {
            // Format: 5.000.000,50 (Indonesian with decimal)
            $parts = explode(',', $amount);
            $integerPart = str_replace('.', '', $parts[0]); // Remove dots from integer part
            $decimalPart = isset($parts[1]) ? $parts[1] : '';
            $processed = $integerPart . ($decimalPart ? '.' . $decimalPart : '');
        } else {
            // Format: 5.000.000 (Indonesian without decimal) or 5000000 (plain number)
            // Check if there are multiple dots (Indonesian format) or single dot (US decimal)
            $dotCount = substr_count($amount, '.');
            if ($dotCount > 1) {
                // Multiple dots = Indonesian thousands separator
                $processed = str_replace('.', '', $amount);
            } else if ($dotCount == 1) {
                // Single dot - could be decimal or thousands separator
                $dotPos = strrpos($amount, '.');
                $afterDot = substr($amount, $dotPos + 1);
                
                if (strlen($afterDot) <= 2 && ctype_digit($afterDot)) {
                    // Likely decimal (1-2 digits after dot)
                    $processed = $amount;
                } else {
                    // Likely thousands separator
                    $processed = str_replace('.', '', $amount);
                }
            } else {
                // No dots - plain number
                $processed = $amount;
            }
        }
        
        // Final cleanup - ensure only digits and one decimal point
        $processed = preg_replace('/[^\d.]/', '', $processed);
        return $processed;
    }
}
