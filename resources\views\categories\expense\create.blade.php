@extends('layouts.dashboard')

@section('title', 'Tambah Kategori Pengeluaran')

@section('page-title', '➖ Tambah Kategori Pengeluaran')

@push('styles')
<style>
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    /* Theme support untuk breadcrumb */
    body[data-theme="solarized-dark"] .breadcrumb-item a {
        color: #b58900 !important;
    }
    
    body[data-theme="solarized-dark"] .breadcrumb-item.active {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item a {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item.active {
        color: #ffffff !important;
    }
    
    /* Theme support untuk h2 */
    body[data-theme="solarized-dark"] h2 {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] h2 {
        color: #ffffff !important;
    }
    
    /* Theme support untuk small text */
    body[data-theme="solarized-dark"] .text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="synth-wave"] .text-muted {
        color: #cccccc !important;
    }
    
    .form-card {
        background: #ffffff;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.2);
        overflow: hidden;
    }
    
    /* Theme support */
    body[data-theme="solarized-dark"] .form-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
    }
    
    body[data-theme="synth-wave"] .form-card {
        background: rgba(255, 0, 102, 0.1);
        border: 1px solid #ff0066;
        color: #ffffff;
    }
    
    /* Perbaiki form labels untuk synth-wave theme */
    body[data-theme="synth-wave"] .form-card .form-label {
        color: #ff0066 !important;
        background: transparent !important;
        border: none !important;
        text-shadow: 0 0 5px #ff0066;
        font-weight: 600;
    }
    
    body[data-theme="synth-wave"] .form-card .form-control,
    body[data-theme="synth-wave"] .form-card .form-select {
        color: #ffffff !important;
        background: rgba(0, 0, 0, 0.3) !important;
        border: 1px solid #ff0066 !important;
    }
    
    body[data-theme="synth-wave"] .form-card .form-control::placeholder {
        color: #cccccc !important;
    }
    
    /* Theme support untuk h4 */
    body[data-theme="solarized-dark"] h4 {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] h4 {
        color: #ff0066 !important;
        text-shadow: 0 0 10px #ff0066;
    }
    
    .color-picker {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
        gap: 10px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        border: 2px dashed rgba(220, 53, 69, 0.3);
    }
    
    .color-option {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 3px solid transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }
    
    .color-option:hover {
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }
    
    .color-option.selected {
        border-color: #fff;
        transform: scale(1.15);
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.4);
    }
    
    .color-option::after {
        content: '✓';
        position: absolute;
        color: white;
        font-weight: bold;
        font-size: 18px;
        opacity: 0;
        transition: opacity 0.3s;
        text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
    }
    
    .color-option.selected::after {
        opacity: 1;
    }
    
    .icon-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
        gap: 12px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        border: 2px dashed rgba(220, 53, 69, 0.3);
        max-height: 400px;
        overflow-y: auto;
    }
    
    .icon-option {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
    }
    
    .icon-option:hover {
        transform: scale(1.1);
        background: rgba(220, 53, 69, 0.2);
        border-color: rgba(220, 53, 69, 0.5);
    }
    
    .icon-option.selected {
        background: rgba(220, 53, 69, 0.3);
        border-color: #dc3545;
        transform: scale(1.15);
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.4);
    }
    
    .icon-option i {
        font-size: 24px !important;
        font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome" !important;
        font-weight: 900 !important;
        color: #dc3545;
        transition: all 0.3s;
        display: inline-block !important;
        line-height: 1 !important;
        width: 24px;
        text-align: center;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-rendering: auto !important;
    }
    
    .icon-option:hover i,
    .icon-option.selected i {
        color: #fff;
        text-shadow: 0 0 8px rgba(220, 53, 69, 0.8);
    }
    
    /* Theme support untuk icons */
    body[data-theme="solarized-dark"] .icon-option i {
        color: #dc322f;
    }
    
    body[data-theme="synth-wave"] .icon-option i {
        color: #ff0066;
    }
    
    body[data-theme="synth-wave"] .icon-option:hover i,
    body[data-theme="synth-wave"] .icon-option.selected i {
        color: #ffffff;
        text-shadow: 0 0 10px #ff0066;
    }
    
    /* FontAwesome fallback untuk emoji */
    body.fa-fallback .icon-option i::before {
        font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI Symbol", "Noto Emoji" !important;
        font-weight: normal !important;
        font-size: 20px !important;
    }
    
    /* Fallback emoji untuk expense icons - Makanan & Minuman */
    body.fa-fallback .icon-option .fa-utensils::before { content: "🍽️"; }
    body.fa-fallback .icon-option .fa-coffee::before { content: "☕"; }
    body.fa-fallback .icon-option .fa-wine-glass::before { content: "🍷"; }
    body.fa-fallback .icon-option .fa-birthday-cake::before { content: "🎂"; }
    body.fa-fallback .icon-option .fa-pizza-slice::before { content: "🍕"; }
    body.fa-fallback .icon-option .fa-hamburger::before { content: "🍔"; }
    body.fa-fallback .icon-option .fa-ice-cream::before { content: "🍦"; }
    body.fa-fallback .icon-option .fa-apple-alt::before { content: "🍎"; }
    
    /* Transportasi */
    body.fa-fallback .icon-option .fa-car::before { content: "🚗"; }
    body.fa-fallback .icon-option .fa-motorcycle::before { content: "🏍️"; }
    body.fa-fallback .icon-option .fa-bus::before { content: "🚌"; }
    body.fa-fallback .icon-option .fa-train::before { content: "🚆"; }
    body.fa-fallback .icon-option .fa-plane::before { content: "✈️"; }
    body.fa-fallback .icon-option .fa-bicycle::before { content: "🚲"; }
    body.fa-fallback .icon-option .fa-taxi::before { content: "🚕"; }
    body.fa-fallback .icon-option .fa-gas-pump::before { content: "⛽"; }
    body.fa-fallback .icon-option .fa-parking::before { content: "🅿️"; }
    body.fa-fallback .icon-option .fa-ship::before { content: "🚢"; }
    body.fa-fallback .icon-option .fa-truck::before { content: "🚛"; }
    body.fa-fallback .icon-option .fa-subway::before { content: "🚇"; }
    
    /* Belanja */
    body.fa-fallback .icon-option .fa-shopping-cart::before { content: "🛒"; }
    body.fa-fallback .icon-option .fa-shopping-bag::before { content: "🛍️"; }
    body.fa-fallback .icon-option .fa-store::before { content: "🏪"; }
    body.fa-fallback .icon-option .fa-tshirt::before { content: "👕"; }
    body.fa-fallback .icon-option .fa-shoe-prints::before { content: "👟"; }
    body.fa-fallback .icon-option .fa-glasses::before { content: "👓"; }
    body.fa-fallback .icon-option .fa-watch::before { content: "⌚"; }
    body.fa-fallback .icon-option .fa-gem::before { content: "💎"; }
    body.fa-fallback .icon-option .fa-ring::before { content: "💍"; }
    body.fa-fallback .icon-option .fa-handbag::before { content: "👜"; }
    
    /* Rumah & Utilitas */
    body.fa-fallback .icon-option .fa-home::before { content: "🏠"; }
    body.fa-fallback .icon-option .fa-bolt::before { content: "⚡"; }
    body.fa-fallback .icon-option .fa-water::before { content: "💧"; }
    body.fa-fallback .icon-option .fa-wifi::before { content: "📶"; }
    body.fa-fallback .icon-option .fa-phone::before { content: "📱"; }
    body.fa-fallback .icon-option .fa-tv::before { content: "📺"; }
    body.fa-fallback .icon-option .fa-couch::before { content: "🛋️"; }
    body.fa-fallback .icon-option .fa-bed::before { content: "🛏️"; }
    body.fa-fallback .icon-option .fa-bath::before { content: "🛁"; }
    body.fa-fallback .icon-option .fa-hammer::before { content: "🔨"; }
    body.fa-fallback .icon-option .fa-tools::before { content: "🔧"; }
    body.fa-fallback .icon-option .fa-paint-roller::before { content: "🎨"; }
    
    /* Kesehatan & Kecantikan */
    body.fa-fallback .icon-option .fa-medkit::before { content: "🩹"; }
    body.fa-fallback .icon-option .fa-user-md::before { content: "👨‍⚕️"; }
    body.fa-fallback .icon-option .fa-hospital::before { content: "🏥"; }
    body.fa-fallback .icon-option .fa-pills::before { content: "💊"; }
    body.fa-fallback .icon-option .fa-syringe::before { content: "💉"; }
    body.fa-fallback .icon-option .fa-heartbeat::before { content: "💓"; }
    body.fa-fallback .icon-option .fa-eye::before { content: "👁️"; }
    body.fa-fallback .icon-option .fa-tooth::before { content: "🦷"; }
    body.fa-fallback .icon-option .fa-spa::before { content: "🧖‍♀️"; }
    body.fa-fallback .icon-option .fa-cut::before { content: "✂️"; }
    
    /* Hiburan & Rekreasi */
    body.fa-fallback .icon-option .fa-gamepad::before { content: "🎮"; }
    body.fa-fallback .icon-option .fa-film::before { content: "🎬"; }
    body.fa-fallback .icon-option .fa-music::before { content: "🎵"; }
    body.fa-fallback .icon-option .fa-headphones::before { content: "🎧"; }
    body.fa-fallback .icon-option .fa-camera::before { content: "📷"; }
    body.fa-fallback .icon-option .fa-guitar::before { content: "🎸"; }
    body.fa-fallback .icon-option .fa-microphone::before { content: "🎤"; }
    body.fa-fallback .icon-option .fa-ticket-alt::before { content: "🎫"; }
    body.fa-fallback .icon-option .fa-bowling-ball::before { content: "🎳"; }
    body.fa-fallback .icon-option .fa-football-ball::before { content: "🏈"; }
    body.fa-fallback .icon-option .fa-basketball-ball::before { content: "🏀"; }
    body.fa-fallback .icon-option .fa-swimming-pool::before { content: "🏊‍♂️"; }
    
    /* Lainnya */
    body.fa-fallback .icon-option .fa-gift::before { content: "🎁"; }
    body.fa-fallback .icon-option .fa-heart::before { content: "❤️"; }
    body.fa-fallback .icon-option .fa-paw::before { content: "🐾"; }
    body.fa-fallback .icon-option .fa-credit-card::before { content: "💳"; }
    
    /* Pendidikan & Buku */
    body.fa-fallback .icon-option .fa-book::before { content: "📚"; }
    body.fa-fallback .icon-option .fa-graduation-cap::before { content: "🎓"; }
    body.fa-fallback .icon-option .fa-university::before { content: "🏛️"; }
    body.fa-fallback .icon-option .fa-pen::before { content: "✒️"; }
    body.fa-fallback .icon-option .fa-pencil-alt::before { content: "✏️"; }
    body.fa-fallback .icon-option .fa-calculator::before { content: "🧮"; }
    body.fa-fallback .icon-option .fa-microscope::before { content: "🔬"; }
    body.fa-fallback .icon-option .fa-flask::before { content: "⚗️"; }
    body.fa-fallback .icon-option .fa-chalkboard-teacher::before { content: "👨‍🏫"; }
    
    /* Olahraga & Fitness */
    body.fa-fallback .icon-option .fa-dumbbell::before { content: "🏋️"; }
    body.fa-fallback .icon-option .fa-running::before { content: "🏃‍♂️"; }
    body.fa-fallback .icon-option .fa-swimmer::before { content: "🏊‍♂️"; }
    body.fa-fallback .icon-option .fa-walking::before { content: "🚶‍♂️"; }
    body.fa-fallback .icon-option .fa-skiing::before { content: "⛷️"; }
    body.fa-fallback .icon-option .fa-horse::before { content: "🐎"; }
    body.fa-fallback .icon-option .fa-table-tennis::before { content: "🏓"; }
    body.fa-fallback .icon-option .fa-golf-ball::before { content: "⛳"; }
    body.fa-fallback .icon-option .fa-volleyball-ball::before { content: "🏐"; }
    
    /* Keuangan & Asuransi */
    body.fa-fallback .icon-option .fa-money-bill::before { content: "💵"; }
    body.fa-fallback .icon-option .fa-coins::before { content: "🪙"; }
    body.fa-fallback .icon-option .fa-shield-alt::before { content: "🛡️"; }
    body.fa-fallback .icon-option .fa-file-invoice-dollar::before { content: "🧾"; }
    
    /* Lainnya Tambahan */
    body.fa-fallback .icon-option .fa-seedling::before { content: "🌱"; }
    body.fa-fallback .icon-option .fa-recycle::before { content: "♻️"; }
    body.fa-fallback .icon-option .fa-envelope::before { content: "✉️"; }
    body.fa-fallback .icon-option .fa-newspaper::before { content: "📰"; }
    body.fa-fallback .icon-option .fa-clipboard-list::before { content: "📋"; }
    body.fa-fallback .icon-option .fa-calendar::before { content: "📅"; }
    body.fa-fallback .icon-option .fa-clock::before { content: "🕐"; }
    
    .btn-create {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border: none;
        border-radius: 12px;
        padding: 15px 30px;
        font-weight: 600;
        color: white;
        transition: all 0.3s;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        background: linear-gradient(135deg, #c82333, #bd2130);
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 12px;
        padding: 15px 30px;
        font-weight: 600;
        color: white;
        transition: all 0.3s;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }
    
    /* Loading animation */
    .loading-icon {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    /* Form validation styles */
    .form-control.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
    
    .invalid-feedback {
        color: #dc3545;
        font-weight: 500;
    }
    
    /* Scroll bar untuk icon grid */
    .icon-grid::-webkit-scrollbar {
        width: 8px;
    }
    
    .icon-grid::-webkit-scrollbar-track {
        background: rgba(220, 53, 69, 0.1);
        border-radius: 4px;
    }
    
    .icon-grid::-webkit-scrollbar-thumb {
        background: rgba(220, 53, 69, 0.5);
        border-radius: 4px;
    }
    
    .icon-grid::-webkit-scrollbar-thumb:hover {
        background: rgba(220, 53, 69, 0.7);
    }
    
    /* Animation untuk form elements */
    .form-control, .form-select {
        transition: all 0.3s;
    }
    
    .form-control:focus, .form-select:focus {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.2);
    }
    
    /* Category type indicator */
    .category-type-indicator {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 20px;
    }
    
    /* Help text */
    .help-text {
        font-size: 13px;
        color: #6c757d;
        margin-top: 5px;
    }
    
    body[data-theme="synth-wave"] .help-text {
        color: #cccccc;
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('dashboard') }}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ route('categories.expense.index') }}" class="text-decoration-none">
                    <i class="fas fa-arrow-down me-1"></i>Kategori Pengeluaran
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-plus me-1"></i>Tambah Kategori
            </li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-card">
                <!-- Header -->
                <div class="card-header bg-transparent border-0 p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h2 class="mb-2">
                                <i class="fas fa-arrow-down text-danger me-2"></i>
                                Tambah Kategori Pengeluaran
                            </h2>
                            <p class="text-muted mb-0">Buat kategori baru untuk mengorganisir pengeluaran Anda dengan lebih baik</p>
                        </div>
                        <div class="category-type-indicator">
                            <i class="fas fa-arrow-down me-2"></i>
                            Pengeluaran
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-4">
                    <form action="{{ route('categories.store') }}" method="POST" id="categoryForm">
                        @csrf
                        <input type="hidden" name="type" value="expense">
                        <input type="hidden" name="icon" id="selectedIcon" value="">
                        <input type="hidden" name="color" id="selectedColor" value="#dc3545">
                        
                        <div class="row">
                            <!-- Form Section - Full Width -->
                            <div class="col-lg-12">
                                <!-- Nama Kategori -->
                                <div class="mb-4">
                                    <label for="name" class="form-label fw-bold">
                                        <i class="fas fa-tag me-2"></i>Nama Kategori *
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-lg @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name') }}" 
                                           placeholder="Contoh: Makanan & Minuman, Transportasi, Belanja..." 
                                           required>
                                    <div class="help-text">Berikan nama yang deskriptif untuk memudahkan kategorisasi</div>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Deskripsi -->
                                <div class="mb-4">
                                    <label for="description" class="form-label fw-bold">
                                        <i class="fas fa-align-left me-2"></i>Deskripsi
                                    </label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" 
                                              name="description" 
                                              rows="3" 
                                              placeholder="Jelaskan jenis pengeluaran yang termasuk dalam kategori ini...">{{ old('description') }}</textarea>
                                    <div class="help-text">Opsional: Tambahkan deskripsi untuk memperjelas penggunaan kategori</div>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Pilih Icon -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-icons me-2"></i>Pilih Icon *
                                    </label>
                                    <div class="icon-grid" id="iconGrid">
                                        @php
                                        $expenseIcons = [
                                            // Makanan & Minuman
                                            'fas fa-utensils', 'fas fa-coffee', 'fas fa-wine-glass', 'fas fa-birthday-cake',
                                            'fas fa-pizza-slice', 'fas fa-hamburger', 'fas fa-ice-cream', 'fas fa-apple-alt',
                                            
                                            // Transportasi
                                            'fas fa-car', 'fas fa-motorcycle', 'fas fa-bus', 'fas fa-train',
                                            'fas fa-plane', 'fas fa-bicycle', 'fas fa-taxi', 'fas fa-gas-pump',
                                            'fas fa-parking', 'fas fa-ship', 'fas fa-truck', 'fas fa-subway',
                                            
                                            // Belanja
                                            'fas fa-shopping-cart', 'fas fa-shopping-bag', 'fas fa-store', 'fas fa-tshirt',
                                            'fas fa-shoe-prints', 'fas fa-glasses', 'fas fa-watch', 'fas fa-gem',
                                            'fas fa-ring', 'fas fa-handbag',
                                            
                                            // Rumah & Utilitas
                                            'fas fa-home', 'fas fa-bolt', 'fas fa-water', 'fas fa-wifi',
                                            'fas fa-phone', 'fas fa-tv', 'fas fa-couch', 'fas fa-bed',
                                            'fas fa-bath', 'fas fa-hammer', 'fas fa-tools', 'fas fa-paint-roller',
                                            
                                            // Kesehatan & Kecantikan
                                            'fas fa-medkit', 'fas fa-user-md', 'fas fa-hospital',
                                            'fas fa-pills', 'fas fa-syringe', 'fas fa-heartbeat',
                                            'fas fa-eye', 'fas fa-tooth', 'fas fa-spa', 'fas fa-cut',
                                            
                                            // Hiburan & Rekreasi
                                            'fas fa-gamepad', 'fas fa-film', 'fas fa-music',
                                            'fas fa-headphones', 'fas fa-camera', 'fas fa-guitar',
                                            'fas fa-microphone', 'fas fa-ticket-alt', 'fas fa-bowling-ball',
                                            'fas fa-football-ball', 'fas fa-basketball-ball', 'fas fa-swimming-pool',
                                            
                                            // Pendidikan & Buku
                                            'fas fa-book', 'fas fa-graduation-cap', 'fas fa-university',
                                            'fas fa-pen', 'fas fa-pencil-alt', 'fas fa-calculator',
                                            'fas fa-microscope', 'fas fa-flask', 'fas fa-chalkboard-teacher',
                                            
                                            // Olahraga & Fitness
                                            'fas fa-dumbbell', 'fas fa-running', 'fas fa-swimmer',
                                            'fas fa-walking', 'fas fa-skiing', 'fas fa-horse',
                                            'fas fa-table-tennis', 'fas fa-golf-ball', 'fas fa-volleyball-ball',
                                            
                                            // Keuangan & Asuransi
                                            'fas fa-credit-card', 'fas fa-money-bill', 'fas fa-coins',
                                            'fas fa-shield-alt', 'fas fa-file-invoice-dollar',
                                            
                                            // Lainnya
                                            'fas fa-gift', 'fas fa-heart', 'fas fa-paw', 'fas fa-seedling',
                                            'fas fa-recycle', 'fas fa-envelope', 'fas fa-newspaper',
                                            'fas fa-clipboard-list', 'fas fa-calendar', 'fas fa-clock'
                                        ];
                                        @endphp
                                        @foreach($expenseIcons as $icon)
                                            <div class="icon-option" data-icon="{{ $icon }}" title="{{ $icon }}">
                                                <i class="{{ $icon }}"></i>
                                            </div>
                                        @endforeach
                                    </div>
                                    <div class="help-text">Pilih icon yang merepresentasikan kategori pengeluaran Anda</div>
                                    @error('icon')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Pilih Warna -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-palette me-2"></i>Pilih Warna *
                                    </label>
                                    <div class="color-picker" id="colorPicker">
                                        @php
                                        $expenseColors = [
                                            '#dc3545', '#e74c3c', '#c0392b', '#a93226',
                                            '#922b21', '#7b241c', '#641e16', '#fd79a8',
                                            '#e84393', '#d63031', '#b71c1c', '#ad1457',
                                            '#8e24aa', '#7b1fa2', '#6a1b9a', '#4a148c',
                                            '#ff5722', '#ff3d00', '#e65100', '#bf360c',
                                            '#795548', '#6d4c41', '#5d4037', '#4e342e'
                                        ];
                                        @endphp
                                        @foreach($expenseColors as $color)
                                            <div class="color-option {{ $color == '#dc3545' ? 'selected' : '' }}" 
                                                 data-color="{{ $color }}" 
                                                 style="background-color: {{ $color }};"
                                                 title="{{ $color }}"></div>
                                        @endforeach
                                    </div>
                                    <div class="help-text">Pilih warna yang akan digunakan untuk kategori ini dalam laporan dan grafik</div>
                                    @error('color')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('categories.expense.index') }}" class="btn btn-cancel">
                                        <i class="fas fa-arrow-left me-2"></i>Kembali
                                    </a>
                                    <div>
                                        <button type="reset" class="btn btn-outline-secondary me-2" id="resetForm">
                                            <i class="fas fa-undo me-2"></i>Reset
                                        </button>
                                        <button type="submit" class="btn btn-create" id="submitBtn">
                                            <i class="fas fa-save me-2"></i>Simpan Kategori
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if FontAwesome is loaded
    function isFontAwesomeLoaded() {
        const testElement = document.createElement('i');
        testElement.className = 'fas fa-heart';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement, ':before');
        const content = computedStyle.getPropertyValue('content');
        
        document.body.removeChild(testElement);
        
        // FontAwesome loaded if content is not "none" or empty
        return content && content !== 'none' && content !== '""';
    }
    
    // Add fallback class if FontAwesome is not loaded
    if (!isFontAwesomeLoaded()) {
        console.log('FontAwesome not detected, using emoji fallbacks');
        document.body.classList.add('fa-fallback');
    }

    // Initialize variables
    let selectedIcon = '';
    let selectedColor = '#dc3545';
    
    // Elements
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const iconOptions = document.querySelectorAll('.icon-option');
    const colorOptions = document.querySelectorAll('.color-option');
    const selectedIconInput = document.getElementById('selectedIcon');
    const selectedColorInput = document.getElementById('selectedColor');
    const form = document.getElementById('categoryForm');
    const submitBtn = document.getElementById('submitBtn');
    const resetBtn = document.getElementById('resetForm');

    // Icon selection handler
    iconOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all icons
            iconOptions.forEach(opt => opt.classList.remove('selected'));
            
            // Add selected class to clicked icon
            this.classList.add('selected');
            
            // Update selected icon
            selectedIcon = this.dataset.icon;
            selectedIconInput.value = selectedIcon;
            
            console.log('Selected icon:', selectedIcon);
        });
    });

    // Color selection handler
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all colors
            colorOptions.forEach(opt => opt.classList.remove('selected'));
            
            // Add selected class to clicked color
            this.classList.add('selected');
            
            // Update selected color
            selectedColor = this.dataset.color;
            selectedColorInput.value = selectedColor;
            
            console.log('Selected color:', selectedColor);
        });
    });

    // Name input handler
    nameInput.addEventListener('input', function() {
        // Optional: Add any name validation here
    });

    // Description input handler
    descriptionInput.addEventListener('input', function() {
        // Optional: Add any description validation here
    });

    // Form validation and submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validate required fields
        const name = nameInput.value.trim();
        
        if (!name) {
            Swal.fire({
                title: 'Error!',
                text: 'Nama kategori harus diisi!',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            nameInput.focus();
            return;
        }

        if (!selectedIcon) {
            Swal.fire({
                title: 'Error!',
                text: 'Silakan pilih icon untuk kategori!',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            document.getElementById('iconGrid').scrollIntoView({ behavior: 'smooth' });
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner loading-icon me-2"></i>Menyimpan...';
        
        // Show loading alert
        Swal.fire({
            title: 'Menyimpan Kategori',
            text: 'Sedang memproses data kategori pengeluaran...',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Submit form after short delay (for better UX)
        setTimeout(() => {
            form.submit();
        }, 1000);
    });

    // Reset form handler
    resetBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        Swal.fire({
            title: 'Reset Form?',
            text: 'Semua data yang telah diisi akan hilang!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Reset!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                // Reset form
                form.reset();
                
                // Reset selections
                iconOptions.forEach(opt => opt.classList.remove('selected'));
                colorOptions.forEach(opt => opt.classList.remove('selected'));
                
                // Reset to default color
                document.querySelector('[data-color="#dc3545"]').classList.add('selected');
                selectedColor = '#dc3545';
                selectedIcon = '';
                selectedIconInput.value = '';
                selectedColorInput.value = '#dc3545';
                
                console.log('Form has been reset');
                
                Swal.fire({
                    title: 'Form Direset!',
                    text: 'Semua field telah dikosongkan.',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    });

    // Add smooth scroll for better UX
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
});
</script>
@endpush
