<?php

require_once 'vendor/autoload.php';
require_once 'bootstrap/app.php';

$app = require_once 'bootstrap/app.php';

// Login as user 2 
auth()->loginUsingId(2);

// Create request
$request = new \Illuminate\Http\Request([
    'month' => '06',
    'year' => '2025'
]);

// Test the API
$controller = new \App\Http\Controllers\ReportController();
$response = $controller->apiMonthly($request);

echo "API Monthly Response:\n";
echo $response->getContent();
echo "\n";
