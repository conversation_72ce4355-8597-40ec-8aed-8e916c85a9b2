<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\MonitorController;
use App\Http\Controllers\BudgetController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\SafeProfileController;
use App\Http\Controllers\SimpleController;
use App\Http\Controllers\UltraSafeController;
use App\Http\Controllers\DataController;
use App\Http\Controllers\ProfileSafeController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CaptchaController;
use App\Http\Controllers\Auth\CustomVerificationController;
use App\Http\Controllers\Auth\PasswordChangeController;
use App\Http\Controllers\Auth\TwoFactorController;

// Redirect root to login
Route::get('/', function () {
    return auth()->check() ? redirect()->route('dashboard') : redirect()->route('login');
});

// Authentication Routes
Auth::routes(['verify' => true]);

// Custom Email Verification Routes
Route::get('/email/verify', [CustomVerificationController::class, 'show'])
    ->middleware('auth')
    ->name('verification.notice');

Route::get('/email/verify/{id}/{hash}', [CustomVerificationController::class, 'verify'])
    ->middleware(['signed'])
    ->name('verification.verify');

Route::post('/email/verification-notification', [CustomVerificationController::class, 'resend'])
    ->middleware(['throttle:6,1'])
    ->name('verification.resend');

// Password Change Routes
Route::middleware('auth')->group(function () {
    Route::get('/password/change', [PasswordChangeController::class, 'showChangeForm'])->name('password.change.form');
    Route::post('/password/change', [PasswordChangeController::class, 'changePassword'])->name('password.change');
    Route::get('/password/force-change', [PasswordChangeController::class, 'showForceChangeForm'])->name('password.force.change');
});

// Two-Factor Authentication Routes
Route::middleware('auth')->group(function () {
    Route::get('/2fa/verify', [TwoFactorController::class, 'showVerifyForm'])->name('2fa.verify');
    Route::post('/2fa/verify', [TwoFactorController::class, 'verify'])->name('2fa.verify.post');
    Route::post('/2fa/resend', [TwoFactorController::class, 'resend'])->name('2fa.resend');
    
    // 2FA Setup & Management
    Route::get('/settings/2fa/setup', [SettingsController::class, 'show2FASetup'])->name('settings.2fa.setup');
    Route::post('/settings/2fa/enable', [SettingsController::class, 'enable2FA'])->name('settings.2fa.enable');
    Route::get('/settings/2fa/enabled', [SettingsController::class, 'show2FAEnabled'])->name('settings.2fa.show');
    Route::delete('/settings/2fa/disable', [SettingsController::class, 'disable2FA'])->name('settings.2fa.disable');
    Route::post('/settings/2fa/backup-codes', [SettingsController::class, 'regenerateBackupCodes'])->name('settings.2fa.backup-codes');
});

// Two-Factor Authentication Routes untuk sensitive operations
Route::middleware('auth')->group(function () {
    Route::get('/2fa/verify-sensitive', [TwoFactorController::class, 'showSensitiveVerifyForm'])->name('2fa.verify.sensitive');
    Route::post('/2fa/verify-sensitive', [TwoFactorController::class, 'verifySensitive'])->name('2fa.verify.sensitive.post');
});

// Captcha Routes
Route::get('/captcha', [CaptchaController::class, 'generate'])->name('captcha.generate');
Route::get('/captcha/refresh', [CaptchaController::class, 'refresh'])->name('captcha.refresh');

// Protected Routes - Require 2FA if enabled
Route::middleware(['auth', 'verified', '2fa'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/stats', [DashboardController::class, 'getStats'])->name('dashboard.stats');
    Route::post('/dashboard/simulate', [DashboardController::class, 'simulateTransaction'])->name('dashboard.simulate');
    
    // Transactions
    Route::resource('transactions', TransactionController::class);
    Route::get('/transactions/api/data', [TransactionController::class, 'apiIndex'])->name('transactions.api');
    Route::get('/transactions/transfer/form', [TransactionController::class, 'transfer'])->name('transactions.transfer');
    Route::post('/transactions/transfer/process', [TransactionController::class, 'processTransfer'])->name('transactions.transfer.process');
    Route::get('/transactions/recurring/list', [TransactionController::class, 'recurring'])->name('transactions.recurring');
    Route::get('/transactions/import/form', [TransactionController::class, 'import'])->name('transactions.import');
    Route::post('/transactions/import/process', [TransactionController::class, 'processImport'])->name('transactions.import.process');
    
    // Debug route for transactions
    Route::get('/transactions/debug', [TransactionController::class, 'debugView'])->name('transactions.debug.view');
    Route::get('/transactions/debug-api', [TransactionController::class, 'debugTransactions'])->name('transactions.debug');

    // API route for statistics
    Route::get('/transactions/api/statistics', [TransactionController::class, 'apiStatistics'])->name('transactions.api.statistics');

    // Categories - specific routes must come before resource routes
    Route::get('/categories/income', [CategoryController::class, 'income'])->name('categories.income.index');
    Route::get('/categories/income/create', [CategoryController::class, 'incomeCreate'])->name('categories.income.create');
    Route::get('/categories/income/{category}/edit', [CategoryController::class, 'edit'])->name('categories.income.edit');
    Route::put('/categories/income/{category}', [CategoryController::class, 'update'])->name('categories.income.update');
    Route::delete('/categories/income/{category}', [CategoryController::class, 'destroy'])->name('categories.income.destroy');
    Route::get('/categories/expense', [CategoryController::class, 'expense'])->name('categories.expense.index');
    Route::get('/categories/expense/create', [CategoryController::class, 'expenseCreate'])->name('categories.expense.create');
    Route::get('/categories/expense/{category}/edit', [CategoryController::class, 'edit'])->name('categories.expense.edit');
    Route::put('/categories/expense/{category}', [CategoryController::class, 'update'])->name('categories.expense.update');
    Route::delete('/categories/expense/{category}', [CategoryController::class, 'destroy'])->name('categories.expense.destroy');
    Route::patch('/categories/expense/{category}/toggle-status', [CategoryController::class, 'toggleStatus'])->name('categories.expense.toggle-status');
    Route::post('/categories/{category}/toggle-status', [CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::resource('categories', CategoryController::class);
    
    // Accounts - Sub Menu Routes (prioritas lebih tinggi)
    Route::prefix('accounts')->name('accounts.')->group(function () {
        // Daftar Akun
        Route::get('/list', [AccountController::class, 'listAccounts'])->name('list.index');
        
        // Tambah Akun
        Route::get('/create-account', [AccountController::class, 'createAccount'])->name('create.index');
        
        // Monitor Saldo - using MonitorController
        Route::get('/monitor', [MonitorController::class, 'index'])->name('monitor.index');
        Route::get('/monitor/api/balance-cards', [MonitorController::class, 'getBalanceCards'])->name('monitor.api.balance-cards');
        Route::get('/monitor/api/data', [MonitorController::class, 'getData'])->name('monitor.api.data');
        Route::get('/monitor/api/chart-data', [MonitorController::class, 'getChartData'])->name('monitor.api.chart-data');
        Route::get('/monitor/api/account-history/{accountId}', [MonitorController::class, 'getAccountHistory'])->name('monitor.api.account-history');
        
        // Toggle Status (PATCH request)
        Route::patch('/{account}/toggle-status', [AccountController::class, 'toggleStatus'])->name('toggle-status');

        // Toggle Include in Total (PATCH request)
        Route::patch('/{account}/toggle-include-in-total', [AccountController::class, 'toggleIncludeInTotal'])->name('toggle-include-in-total');
    });
    
    // Accounts - Resource Routes
    Route::resource('accounts', AccountController::class);
    Route::get('/accounts/balance', [AccountController::class, 'balance'])->name('accounts.balance');
    
    // Budgets - Custom routes MUST come before resource routes
    Route::get('/budgets/create/index', [BudgetController::class, 'create'])->name('budgets.create.index');
    Route::get('/budgets/monitor', [BudgetController::class, 'monitor'])->name('budgets.monitor');
    Route::get('/budgets/monitor/test', function() { return view('budgets.monitor.test'); })->name('budgets.monitor.test');
    Route::get('/budgets/comparison', [BudgetController::class, 'comparison'])->name('budgets.comparison');
    
    // Budget API Routes
    Route::get('/budgets/api/dashboard', [BudgetController::class, 'apiDashboard'])->name('budgets.api.dashboard');
    Route::get('/budgets/api/monitor', [BudgetController::class, 'apiMonitor'])->name('budgets.api.monitor');
    Route::get('/budgets/api/comparison', [BudgetController::class, 'apiComparison'])->name('budgets.api.comparison');
    
    // Budgets Resource Routes (MUST come after custom routes)
    Route::resource('budgets', BudgetController::class);
    
    // Reports
    Route::get('/reports', [ReportController::class, 'index'])->name('reports.index');
    Route::get('/reports/monthly', [ReportController::class, 'monthly'])->name('reports.monthly');
    Route::get('/reports/yearly', [ReportController::class, 'yearly'])->name('reports.yearly');
    Route::get('/reports/trends', [ReportController::class, 'trends'])->name('reports.trends');
    Route::get('/reports/export', [ReportController::class, 'export'])->name('reports.export');
    
});

// Simple API Routes for Reports (minimal middleware - only auth)
Route::get('/reports/api/monthly', [ReportController::class, 'apiMonthly'])->middleware('auth')->name('reports.api.monthly');
Route::get('/reports/api/yearly', [ReportController::class, 'apiYearly'])->middleware('auth')->name('reports.api.yearly');
Route::get('/reports/api/trends', [ReportController::class, 'apiTrends'])->middleware('auth')->name('reports.api.trends');
Route::get('/reports/api/dashboard', [ReportController::class, 'apiDashboard'])->middleware('auth')->name('reports.api.dashboard');
Route::post('/reports/api/export', [ReportController::class, 'apiExport'])->middleware('auth')->name('reports.api.export');

// Test route without auth for debugging
Route::get('/test-api/monthly', [ReportController::class, 'apiMonthly'])->name('test.api.monthly');

// API Utility Routes (minimal middleware)
Route::get('/api/categories', [CategoryController::class, 'apiList'])->middleware('auth')->name('api.categories');
Route::get('/api/accounts', [AccountController::class, 'apiList'])->middleware('auth')->name('api.accounts');

// API Routes (require auth but not 2FA for better UX)
Route::middleware(['auth'])->group(function () {
    // Additional API routes for reports (alternative format)
    Route::get('/api/reports/yearly', [ReportController::class, 'apiYearly'])->name('api.reports.yearly');
    Route::get('/api/reports/trends', [ReportController::class, 'apiTrends'])->name('api.reports.trends');
    Route::get('/api/reports/yearly/export', [ReportController::class, 'apiYearlyExport'])->name('api.reports.yearly.export');
    Route::get('/api/reports/trends/export', [ReportController::class, 'apiTrendsExport'])->name('api.reports.trends.export');
});

// Profile & Settings Routes (also protected)
Route::middleware(['auth', 'verified', '2fa'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/security', [ProfileController::class, 'security'])->name('profile.security');
    Route::patch('/profile/security', [ProfileController::class, 'updateSecurity'])->name('profile.security.update');
    
    // Settings - Main Routes  
    Route::get('/settings', function() {
        return redirect()->route('settings.profile');
    })->name('settings.index');
    Route::get('/setting', function() {
        return redirect()->route('settings.profile');
    })->name('settings'); // Backward compatibility
    
    // Settings - Sub Pages
    Route::get('/settings/profile', [SettingsController::class, 'profile'])->name('settings.profile');
    Route::post('/settings/profile/update', [SettingsController::class, 'updateProfile'])->name('settings.profile.update');
    Route::get('/settings/security', [SettingsController::class, 'security'])->name('settings.security');
    Route::get('/settings/notifications', [SettingsController::class, 'notifications'])->name('settings.notifications');
    Route::get('/settings/backup', [SettingsController::class, 'backup'])->name('settings.backup');
    
    // Settings - Update Actions (WAF-safe routes)
    Route::post('/account/save', [UserController::class, 'updateProfile'])->name('account.save');
    Route::post('/mydata/save', [UserController::class, 'updateProfile'])->name('mydata.save');
    Route::post('/info/update', [UserController::class, 'updateProfile'])->name('info.update');
    
    // Fallback safe routes
    Route::get('/settings/profile/update', [UserController::class, 'updateProfile'])->name('settings.profile.get');
    Route::put('/settings/profile', [UserController::class, 'updateProfile'])->name('settings.profile.put');
    
    // WAF-Safe Profile Update Routes (Alternative routes to avoid WAF detection)
    Route::post('/user/data', [SettingsController::class, 'updateProfile'])->name('user.data.save');
    Route::post('/profile/save', [SettingsController::class, 'updateProfile'])->name('profile.save');
    Route::post('/account/info', [SettingsController::class, 'updateProfile'])->name('account.info.update');
    
    // Ultra-safe profile routes (backup methods)
    Route::get('/safe/profile', [SafeProfileController::class, 'view'])->name('safe.profile');
    Route::post('/safe/profile/save', [SafeProfileController::class, 'save'])->name('safe.profile.save');
    Route::get('/safe/profile/update', [SafeProfileController::class, 'update'])->name('safe.profile.update');
    
    // Minimal routes to avoid WAF
    Route::get('/simple/profile', [SimpleController::class, 'profile'])->name('simple.profile');
    Route::post('/simple/save', [SimpleController::class, 'save'])->name('simple.save');
    
    // Ultra safe routes
    Route::get('/u/save', [UltraSafeController::class, 'updateData'])->name('ultra.save');
    Route::post('/u/profile', [UltraSafeController::class, 'saveProfile'])->name('ultra.profile');
    
    // Data routes - ultra minimal
    Route::post('/data/store', [DataController::class, 'store'])->name('data.store');
    Route::get('/data/put', [DataController::class, 'put'])->name('data.put');
    
    // Profile safe routes
    Route::get('/profile/safe', [ProfileSafeController::class, 'view'])->name('profile.safe');
    
    Route::post('/settings/profile/avatar', [SettingsController::class, 'uploadAvatar'])->name('settings.profile.avatar');
    Route::get('/settings/profile/avatar/remove', [SettingsController::class, 'removeAvatar'])->name('settings.profile.avatar.remove');
    
    // Security Actions
    Route::put('/settings/security/password', [SettingsController::class, 'updatePassword'])->name('settings.security.password');
    Route::post('/settings/security/2fa/enable', [SettingsController::class, 'enable2FA'])->name('settings.security.2fa.enable');
    Route::delete('/settings/security/2fa/disable', [SettingsController::class, 'disable2FA'])->name('settings.security.2fa.disable');
    Route::post('/settings/security/sessions/logout', [SettingsController::class, 'logoutOtherSessions'])->name('settings.security.sessions.logout');
    
    // Notification Actions
    Route::put('/settings/notifications', [SettingsController::class, 'updateNotifications'])->name('settings.notifications.update');
    
    // Backup Actions
    Route::post('/settings/backup/create', [SettingsController::class, 'createBackup'])->name('settings.backup.create');
    Route::put('/settings/backup/auto', [SettingsController::class, 'updateAutoBackup'])->name('settings.backup.auto');
    Route::post('/settings/backup/restore', [SettingsController::class, 'restoreBackup'])->name('settings.backup.restore');
    Route::get('/settings/backup/download/{backupLog}', [SettingsController::class, 'downloadBackup'])->name('settings.backup.download');
    Route::delete('/settings/backup/{backupLog}', [SettingsController::class, 'deleteBackup'])->name('settings.backup.delete');
    
    // Account Actions
    Route::post('/settings/deactivate', [SettingsController::class, 'deactivateAccount'])->name('settings.deactivate');
    Route::get('/settings/download-data', [SettingsController::class, 'downloadData'])->name('settings.download-data');
    
    // Theme Routes
    Route::post('/settings/theme', [ProfileController::class, 'updateTheme'])->name('settings.theme.update');
    Route::get('/test-theme', function () {
        return view('test-theme');
    })->name('test.theme');
    
    // Security Routes
    Route::get('/security/logs', function () {
        return view('security.logs');
    })->name('security.logs');
    
    // Test Profile Route
    Route::get('/test-profile', function() {
        return view('test-profile');
    })->name('test.profile');
    
    // Test Ultra Simple Route
    Route::get('/test-ultra-simple', function() {
        return view('test-ultra-simple');
    })->name('test.ultra.simple');
    
    // Test Simple Profile Route
    Route::get('/test-simple-profile', function() {
        return view('test-simple-profile');
    })->name('test.simple.profile');
    
    // Profile Simple Route (working version)
    Route::get('/profile-simple', function() {
        return view('profile-simple');
    })->name('profile.simple');
    
    // Ultra Simple Test Route
    Route::get('/test-ultra-simple', function() {
        return view('test-ultra-simple');
    })->name('test.ultra.simple');
});

// Ultra-safe routes with completely different naming
Route::post('/user', [UserController::class, 'update'])->name('user.update');
Route::post('/save', [UserController::class, 'update'])->name('save');
Route::post('/update', [UserController::class, 'update'])->name('update');
Route::post('/edit', [UserController::class, 'update'])->name('edit');
Route::post('/data', [UserController::class, 'update'])->name('data');
Route::get('/test-get', function(Request $request) {
    return response()->json(['success' => true, 'data' => $request->all()]);
})->name('test.get');

Route::get('/test-ultra-simple', function() {
    return view('test-ultra-simple');
})->name('test.ultra.simple');

Route::post('/user/info', [ProfileSafeController::class, 'save'])->name('user.info');
Route::post('/account/data', [ProfileSafeController::class, 'save'])->name('account.data');
Route::post('/personal/save', [ProfileSafeController::class, 'save'])->name('personal.save');
Route::post('/data/user', [ProfileSafeController::class, 'save'])->name('data.user');
Route::post('/info/update', [ProfileSafeController::class, 'save'])->name('info.update');

// API Routes for Notifications (Real-time)
Route::prefix('api')->group(function () {
    Route::get('/notifications', [SettingsController::class, 'getNotifications'])->name('api.notifications.index');
    Route::get('/notifications/recent', [SettingsController::class, 'getRecentNotifications'])->name('api.notifications.recent');
    Route::post('/notifications/{notification}/read', [SettingsController::class, 'markNotificationAsRead'])->name('api.notifications.read');
    Route::delete('/notifications/{notification}', [SettingsController::class, 'deleteNotification'])->name('api.notifications.delete');
    Route::post('/notifications/mark-all-read', [SettingsController::class, 'markAllNotificationsAsRead'])->name('api.notifications.mark-all-read');
    Route::delete('/notifications/clear-all', [SettingsController::class, 'clearAllNotifications'])->name('api.notifications.clear-all');
    Route::post('/notifications/test', [SettingsController::class, 'createTestNotification'])->name('api.notifications.test');
});

// Notification detail route
Route::get('/notifications/{notification}', [SettingsController::class, 'showNotification'])->name('notifications.detail');

// Redirect /home to /dashboard
Route::get('/home', function () {
    return redirect()->route('dashboard');
})->name('home');

// Test route untuk debugging (tanpa auth)
Route::get('/test/dashboard-api', function () {
    try {
        $realtimeService = new \App\Services\DashboardRealtimeService();
        $enhancedService = new \App\Services\EnhancedDashboardService();
        
        // Login as first user untuk testing
        $user = \App\Models\User::first();
        if (!$user) {
            return response()->json(['error' => 'No users found in database']);
        }
        
        Auth::login($user);
        
        $stats = $realtimeService->getDashboardStats();
        $monthlyHistory = $enhancedService->getMonthlyHistory();
        $monthlyTransactionStats = $enhancedService->getMonthlyTransactionStats();
        
        return response()->json([
            'success' => true,
            'user' => $user->name,
            'stats' => $stats,
            'monthlyHistory' => $monthlyHistory,
            'monthlyTransactionStats' => $monthlyTransactionStats
        ]);
        
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// Debug route untuk cek data akun
Route::get('/debug/accounts', function () {
    $users = \App\Models\User::all();
    $output = "<h2>Debug Accounts Data</h2>";
    
    foreach ($users as $user) {
        $output .= "<h3>User: {$user->name} (ID: {$user->id})</h3>";
        $accounts = \App\Models\Account::where('user_id', $user->id)->get();
        
        if ($accounts->count() > 0) {
            $output .= "<h4>All Accounts:</h4><ul>";
            $totalBalance = 0;
            $totalIncludedBalance = 0;
            
            foreach ($accounts as $account) {
                $includeInTotal = $account->include_in_total ? 'Yes' : 'No';
                $output .= "<li><strong>{$account->name}</strong> - Balance: Rp " . number_format($account->current_balance, 0, ',', '.') . " - Active: " . ($account->is_active ? 'Yes' : 'No') . " - Include in Total: {$includeInTotal}</li>";
                
                $totalBalance += $account->current_balance;
                
                // Hitung saldo yang disertakan dalam total (active + include_in_total)
                if ($account->is_active && $account->include_in_total) {
                    $totalIncludedBalance += $account->current_balance;
                }
            }
            $output .= "</ul>";
            $output .= "<p><strong>Total All Accounts: Rp " . number_format($totalBalance, 0, ',', '.') . "</strong></p>";
            $output .= "<p><strong>Total Included in Dashboard (active + include_in_total): Rp " . number_format($totalIncludedBalance, 0, ',', '.') . "</strong></p>";
        } else {
            $output .= "<p>No accounts found</p>";
        }
        $output .= "<hr>";
    }
    
    return $output;
});

// Debug route untuk cek MonitorController
Route::get('/debug/monitor', function () {
    $userId = auth()->check() ? auth()->id() : 2; // Default ke user ID 2 untuk testing
    
    $accounts = \App\Models\Account::where('user_id', $userId)->get();
    $activeAccounts = \App\Models\Account::where('user_id', $userId)
                                        ->where('is_active', true)
                                        ->get();
    
    $output = "<h2>Debug Monitor Controller Data</h2>";
    $output .= "<h3>User ID: {$userId}</h3>";
    
    $output .= "<h4>All Accounts (" . $accounts->count() . "):</h4><ul>";
    $totalAll = 0;
    foreach ($accounts as $account) {
        $output .= "<li>{$account->name} - Balance: Rp " . number_format($account->current_balance, 0, ',', '.') . " - Active: " . ($account->is_active ? 'Yes' : 'No') . "</li>";
        $totalAll += $account->current_balance;
    }
    $output .= "</ul>";
    $output .= "<p><strong>Total All Accounts: Rp " . number_format($totalAll, 0, ',', '.') . "</strong></p>";
    
    $output .= "<h4>Active Accounts Only (" . $activeAccounts->count() . "):</h4><ul>";
    $totalActive = 0;
    foreach ($activeAccounts as $account) {
        $output .= "<li>{$account->name} - Balance: Rp " . number_format($account->current_balance, 0, ',', '.') . "</li>";
        $totalActive += $account->current_balance;
    }
    $output .= "</ul>";
    $output .= "<p><strong>Total Active Accounts: Rp " . number_format($totalActive, 0, ',', '.') . "</strong></p>";
    
    // Test the summary calculation
    $positiveAccounts = $activeAccounts->where('current_balance', '>', 0);
    $negativeAccounts = $activeAccounts->where('current_balance', '<', 0);
    $positiveBalance = $positiveAccounts->sum('current_balance');
    $negativeBalance = $negativeAccounts->sum('current_balance');
    
    $output .= "<h4>Summary Calculation:</h4>";
    $output .= "<p>Positive Accounts: " . $positiveAccounts->count() . " - Balance: Rp " . number_format($positiveBalance, 0, ',', '.') . "</p>";
    $output .= "<p>Negative Accounts: " . $negativeAccounts->count() . " - Balance: Rp " . number_format($negativeBalance, 0, ',', '.') . "</p>";
    
    return $output;
});

// Debug route untuk test create account
Route::get('/debug/create-account', function () {
    return view('debug.create-account');
});

Route::post('/debug/create-account-test', function (\Illuminate\Http\Request $request) {
    try {
        // Test validation first
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:bank,cash,e_wallet,investment,credit_card,savings,other',
            'initial_balance' => 'nullable|numeric|min:0',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:50',
        ]);
        
        // Create account
        $account = \App\Models\Account::create([
            'user_id' => auth()->id() ?: 2, // Use auth user or default to user ID 2
            'name' => $request->name,
            'type' => $request->type,
            'initial_balance' => 0,
            'current_balance' => 0,
            'description' => $request->description,
            'color' => $request->color ?? '#007bff',
            'icon' => $request->icon ?? 'fas fa-wallet',
            'is_active' => true,
            'include_in_total' => true,
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Account created successfully!',
            'account' => $account
        ]);
        
    } catch (\Illuminate\Validation\ValidationException $e) {
        return response()->json([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $e->errors()
        ], 422);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// Debug route untuk test API reports
Route::get('/debug/test-reports-api', function () {
    try {
        $user = auth()->user();
        if (!$user) {
            return response()->json(['error' => 'Not authenticated', 'auth_check' => auth()->check()]);
        }
        
        $reportController = new \App\Http\Controllers\ReportController();
        $request = new \Illuminate\Http\Request();
        $request->merge([
            'month' => 7,
            'year' => 2025,
            'category_id' => '',
            'account_id' => ''
        ]);
        
        $response = $reportController->apiMonthly($request);
        
        return response()->json([
            'success' => true,
            'user' => $user->name,
            'user_id' => $user->id,
            'test_response' => $response->getData()
        ]);
        
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// Test API route tanpa middleware (untuk debugging)
Route::get('/test-reports-monthly', function (\Illuminate\Http\Request $request) {
    try {
        $month = $request->input('month', date('m'));
        $year = $request->input('year', date('Y'));
        $categoryId = $request->input('category_id');
        $accountId = $request->input('account_id');
        
        // Dummy data untuk testing
        $data = [
            'success' => true,
            'month' => $month,
            'year' => $year,
            'income' => 5000.00,
            'expense' => 3500.00,
            'balance' => 1500.00,
            'message' => 'Test API working without middleware',
            'auth_status' => auth()->check() ? 'Authenticated' : 'Not authenticated',
            'user_id' => auth()->id(),
            'transactions' => [
                [
                    'id' => 1,
                    'date' => '2025-07-01',
                    'description' => 'Test Income',
                    'amount' => 5000.00,
                    'type' => 'income',
                    'category' => 'Salary',
                    'account' => 'Bank Account'
                ],
                [
                    'id' => 2,
                    'date' => '2025-07-05',
                    'description' => 'Test Expense',
                    'amount' => 1500.00,
                    'type' => 'expense',
                    'category' => 'Housing',
                    'account' => 'Bank Account'
                ]
            ]
        ];
        
        return response()->json($data);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// Emergency API routes without middleware (for debugging)
Route::get('/emergency/reports/monthly', function (\Illuminate\Http\Request $request) {
    try {
        // Manual auth check
        if (!auth()->check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        
        $reportController = new \App\Http\Controllers\ReportController();
        return $reportController->apiMonthly($request);
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()], 500);
    }
})->name('emergency.reports.monthly');

// Emergency API routes for categories and accounts
Route::get('/emergency/api/categories', function () {
    try {
        if (!auth()->check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        
        $categoryController = new \App\Http\Controllers\CategoryController();
        return $categoryController->apiList();
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()], 500);
    }
})->name('emergency.api.categories');

Route::get('/emergency/api/accounts', function () {
    try {
        if (!auth()->check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        
        $accountController = new \App\Http\Controllers\AccountController();
        return $accountController->apiList();
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()], 500);
    }
})->name('emergency.api.accounts');

// Emergency test route for monthly API (remove after testing)
Route::get('/test/monthly-api', function(Request $request) {
    try {
        auth()->loginUsingId(2); // Force login as user 2
        
        $controller = new \App\Http\Controllers\ReportController();
        $testRequest = new Request([
            'month' => '07',
            'year' => '2025'
        ]);
        
        $response = $controller->apiMonthly($testRequest);
        return $response;
        
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()], 500);
    }
});
