<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TwoFactorAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        // Skip if user not authenticated
        if (!$user) {
            return $next($request);
        }
        
        // Skip if 2FA not enabled for user
        if (!$user->has2FAEnabled()) {
            return $next($request);
        }
        
        // Skip if already verified in this session
        if (session('2fa_verified') === true) {
            return $next($request);
        }
        
        // Skip for 2FA routes themselves and API routes
        if ($request->is('2fa*') || $request->is('logout') || $request->is('password/change') || 
            $request->is('*/api/*') || $request->is('api/*') || 
            $request->is('reports/api/*') || $request->is('budgets/api/*') || 
            $request->is('transactions/api/*') || $request->is('accounts/api/*')) {
            return $next($request);
        }
        
        // Redirect to 2FA verification
        return redirect()->route('2fa.verify');
    }
}
