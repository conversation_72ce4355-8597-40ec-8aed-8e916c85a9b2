<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Login;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Request as RequestFacade;

class LoginListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        $user = $event->user;
        $ip = RequestFacade::ip();
        $userAgent = RequestFacade::userAgent();
        
        $user->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip,
        ]);
        
        // Create login notification
        \App\Services\NotificationService::createLoginAlert($user->id, [
            'message' => "Login berhasil dari IP {$ip} pada " . now()->format('d M Y H:i'),
            'data' => [
                'ip' => $ip,
                'user_agent' => $userAgent,
                'login_time' => now()->toISOString(),
            ],
        ]);
    }
}
