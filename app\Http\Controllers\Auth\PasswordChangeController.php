<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\SecurityLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class PasswordChangeController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the password change form.
     */
    public function showChangeForm()
    {
        return view('auth.passwords.change');
    }

    /**
     * Handle password change request.
     */
    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'new_password' => [
                'required',
                'string',
                'min:8',
                'max:128',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&^#()])[A-Za-z\d@$!%*?&^#()]+$/',
                'confirmed',
                'different:current_password'
            ],
        ], [
            'current_password.required' => 'Password lama wajib diisi.',
            'new_password.required' => 'Password baru wajib diisi.',
            'new_password.min' => 'Password minimal 8 karakter.',
            'new_password.max' => 'Password maksimal 128 karakter.',
            'new_password.regex' => 'Password harus mengandung: huruf besar, huruf kecil, angka, dan karakter khusus (@$!%*?&^#()).',
            'new_password.confirmed' => 'Konfirmasi password tidak cocok.',
            'new_password.different' => 'Password baru harus berbeda dengan password lama.',
        ]);

        $user = Auth::user();

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            SecurityLog::logEvent(
                $user->id,
                'password_change_failed',
                'Password change attempt with wrong current password',
                'medium',
                [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]
            );

            throw ValidationException::withMessages([
                'current_password' => ['Password lama tidak cocok.'],
            ]);
        }

        // Check if new password is same as old ones (basic check)
        if (Hash::check($request->new_password, $user->password)) {
            throw ValidationException::withMessages([
                'new_password' => ['Password baru harus berbeda dengan password lama.'],
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->new_password),
            'password_changed_at' => now(),
        ]);

        // Clear all sessions except current if requested
        if ($request->has('logout_other_devices')) {
            $this->clearOtherSessions($user, $request->session()->getId());
        }

        SecurityLog::logEvent(
            $user->id,
            'password_changed',
            'Password berhasil diganti',
            'medium',
            [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]
        );

        // Clear password change requirement from session
        $request->session()->forget('password_change_required');

        return redirect()->route('dashboard')
            ->with('success', 'Password berhasil diganti. Semua session lain telah diakhiri untuk keamanan.');
    }

    /**
     * Force password change for expired passwords.
     */
    public function showForceChangeForm()
    {
        $userId = session('password_change_required');
        
        if (!$userId) {
            return redirect()->route('login');
        }

        return view('auth.passwords.force-change');
    }

    /**
     * Clear all other user sessions except current one.
     */
    private function clearOtherSessions(User $user, string $currentSessionId): void
    {
        // In a real application, you might want to implement session storage
        // that allows clearing specific user sessions
        
        // For now, we'll just clear the stored session ID
        $user->update(['current_session_id' => $currentSessionId]);
    }
}
