<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;
use App\Models\SecurityLog;
use App\Models\NotificationSetting;
use App\Models\Notification;
use App\Models\BackupLog;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\Image\SvgImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use BaconQrCode\Writer;

class SettingsController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get security statistics
        $securityStats = [
            'total_logins' => SecurityLog::where('user_id', $user->id)
                ->where('event_type', 'login_success')
                ->count(),
            'failed_attempts' => SecurityLog::where('user_id', $user->id)
                ->where('event_type', 'login_failed')
                ->where('created_at', '>=', now()->subDays(30))
                ->count(),
            'last_login' => $user->last_login_at,
            'account_created' => $user->created_at,
            'password_changed' => $user->password_changed_at,
        ];

        // Get recent security activities
        $recentActivities = SecurityLog::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Check if 2FA is enabled
        $twoFactorEnabled = !empty($user->two_factor_secret);

        return view('settings.index', compact('user', 'securityStats', 'recentActivities', 'twoFactorEnabled'));
    }    /**
     * Update profile information
     */
    public function updateProfile(Request $request)
    {
        try {
            // Log request for debugging WAF issues
            \Log::info('Profile update request', [
                'user_id' => Auth::id(),
                'route' => $request->route()->getName(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            $user = Auth::user();

            // Validate input according to database schema
            $validated = $request->validate([
                'full_name' => 'required|string|max:255',
                'username' => 'required|string|max:255|unique:users,username,' . $user->id . '|alpha_dash',
                'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
                'phone' => 'nullable|string|max:255',
                'birth_date' => 'nullable|date|before:today',
                'gender' => 'nullable|in:L,P',
                'address' => 'nullable|string',
                'bio' => 'nullable|string',
                'city' => 'nullable|string|max:255',
                'postal_code' => 'nullable|string|max:10',
                'currency' => 'nullable|string|max:10',
                'date_format' => 'nullable|string|max:20',
                'time_format' => 'nullable|string|max:5',
                'language' => 'nullable|string|max:255',
                'timezone' => 'nullable|string|max:255',
            ]);

            $oldEmail = $user->email;

            // Sanitize data to avoid WAF detection
            $sanitizedData = [
                'name' => strip_tags(trim($validated['full_name'])),
                'full_name' => strip_tags(trim($validated['full_name'])),
                'username' => preg_replace('/[^a-zA-Z0-9_-]/', '', $validated['username']),
                'email' => filter_var($validated['email'], FILTER_SANITIZE_EMAIL),
                'phone' => preg_replace('/[^0-9+\-\s()]/', '', $validated['phone'] ?? ''),
                'birth_date' => $validated['birth_date'],
                'gender' => $validated['gender'],
                'address' => strip_tags(trim($validated['address'] ?? '')),
                'bio' => strip_tags(trim($validated['bio'] ?? '')),
                'city' => strip_tags(trim($validated['city'] ?? '')),
                'postal_code' => preg_replace('/[^0-9]/', '', $validated['postal_code'] ?? ''),
                'currency' => $validated['currency'] ?? 'IDR',
                'date_format' => $validated['date_format'] ?? 'd/m/Y',
                'time_format' => $validated['time_format'] ?? '24',
                'language' => $validated['language'] ?? 'id',
                'timezone' => $validated['timezone'] ?? 'Asia/Jakarta',
            ];

            // Update user data
            $user->update($sanitizedData);

            // Log profile update with minimal data
            SecurityLog::create([
                'user_id' => $user->id,
                'event_type' => 'profile_updated',
                'ip_address' => $request->ip(),
                'user_agent' => substr($request->userAgent(), 0, 255),
                'description' => 'Profile data updated successfully',
            ]);

            // Handle email verification if changed
            if ($oldEmail !== $user->email) {
                $user->email_verified_at = null;
                $user->save();
                
                return redirect()->route('settings.profile')
                    ->with('warning', '✅ Profil berhasil diperbarui! Email baru perlu diverifikasi ulang.');
            }

            return redirect()->route('settings.profile')
                ->with('success', '🎉 Data profil berhasil disimpan dengan sempurna!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::warning('Profile validation failed', [
                'user_id' => Auth::id(),
                'errors' => $e->errors()
            ]);
            
            return redirect()->route('settings.profile')
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', '❌ Terdapat kesalahan pada data yang dimasukkan. Silakan periksa kembali.');
        } catch (\Exception $e) {
            \Log::error('Profile update error', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('settings.profile')
                ->with('error', '⚠️ Terjadi kesalahan saat menyimpan data. Silakan coba lagi.');
        }
    }/**
     * Save profile with WAF-safe method
     */
    public function saveProfile(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Get JSON data if sent as JSON, otherwise use form data
            $input = $request->json()->all() ?: $request->all();
            
            $data = [];
            
            // Sanitize and validate each field individually
            if (isset($input['full_name']) && !empty($input['full_name'])) {
                $data['full_name'] = substr(strip_tags(trim($input['full_name'])), 0, 255);
            }
            
            if (isset($input['username']) && !empty($input['username'])) {
                $username = preg_replace('/[^a-zA-Z0-9_-]/', '', $input['username']);
                if ($username && $username !== $user->username) {
                    // Check if username is unique
                    $exists = \App\Models\User::where('username', $username)->where('id', '!=', $user->id)->exists();
                    if (!$exists) {
                        $data['username'] = $username;
                    }
                }
            }
            
            if (isset($input['email']) && !empty($input['email'])) {
                $email = filter_var($input['email'], FILTER_SANITIZE_EMAIL);
                if (filter_var($email, FILTER_VALIDATE_EMAIL) && $email !== $user->email) {
                    // Check if email is unique
                    $exists = \App\Models\User::where('email', $email)->where('id', '!=', $user->id)->exists();
                    if (!$exists) {
                        $data['email'] = $email;
                    }
                }
            }
            
            if (isset($input['phone'])) {
                $data['phone'] = preg_replace('/[^0-9+\-\s()]/', '', $input['phone']);
            }
            
            if (isset($input['birth_date']) && !empty($input['birth_date'])) {
                $date = $input['birth_date'];
                if ($date && strtotime($date) < time()) {
                    $data['birth_date'] = $date;
                }
            }
            
            if (isset($input['gender']) && in_array($input['gender'], ['male', 'female', 'other', ''])) {
                $data['gender'] = $input['gender'] ?: null;
            }
            
            if (isset($input['address'])) {
                $data['address'] = substr(strip_tags(trim($input['address'])), 0, 500);
            }
            
            if (isset($input['city'])) {
                $data['city'] = substr(strip_tags(trim($input['city'])), 0, 255);
            }
            
            if (isset($input['postal_code'])) {
                $data['postal_code'] = preg_replace('/[^0-9]/', '', $input['postal_code']);
            }
            
            if (isset($input['country'])) {
                $data['country'] = strtoupper(substr($input['country'], 0, 3));
            }
            
            if (isset($input['bio'])) {
                $data['bio'] = substr(strip_tags(trim($input['bio'])), 0, 500);
            }
            
            if (isset($input['language']) && in_array($input['language'], ['id', 'en'])) {
                $data['language'] = $input['language'];
            }
            
            if (isset($input['timezone'])) {
                $data['timezone'] = $input['timezone'];
            }
            
            // Update user data
            if (!empty($data)) {
                $user->update($data);
                
                // Simple logging
                try {
                    SecurityLog::create([
                        'user_id' => $user->id,
                        'activity' => 'profile_updated',
                        'ip_address' => $request->ip(),
                        'user_agent' => 'Profile Update',
                        'description' => 'Profile updated via safe method',
                        'status' => 'success'
                    ]);
                } catch (\Exception $e) {
                    // Continue even if logging fails
                }
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Profil berhasil diperbarui!'
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Profile update error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan data.'
            ], 500);
        }
    }

    /**
     * Update avatar
     */
    public function updateAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        $user = Auth::user();

        // Delete old avatar if exists
        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
        }

        // Store new avatar
        $path = $request->file('avatar')->store('avatars', 'public');
        
        $user->update(['avatar' => $path]);

        // Log avatar update
        SecurityLog::create([
            'user_id' => $user->id,
            'activity' => 'avatar_updated',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'description' => 'Profile avatar updated',
            'status' => 'success'
        ]);

        return redirect()->route('settings.profile')->with('success', 'Foto profil berhasil diperbarui!');
    }

    /**
     * Upload avatar
     */
    public function uploadAvatar(Request $request)
    {
        try {
            $request->validate([
                'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            $user = Auth::user();

            // Delete old avatar if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Store new avatar
            $path = $request->file('avatar')->store('avatars', 'public');
            $user->update(['avatar' => $path]);

            // Log avatar update
            SecurityLog::create([
                'user_id' => $user->id,
                'event_type' => 'avatar_updated',
                'ip_address' => $request->ip(),
                'user_agent' => substr($request->userAgent(), 0, 255),
                'description' => 'Avatar uploaded successfully',
            ]);

            return redirect()->route('settings.profile')
                ->with('success', '📸 Avatar berhasil diupload!');

        } catch (\Exception $e) {
            return redirect()->route('settings.profile')
                ->with('error', '❌ Gagal mengupload avatar. Silakan coba lagi.');
        }
    }

    /**
     * Remove avatar
     */
    public function removeAvatar()
    {
        try {
            $user = Auth::user();

            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
                $user->update(['avatar' => null]);

                // Log avatar removal
                SecurityLog::create([
                    'user_id' => $user->id,
                    'event_type' => 'avatar_removed',
                    'ip_address' => request()->ip(),
                    'user_agent' => substr(request()->userAgent(), 0, 255),
                    'description' => 'Avatar removed successfully',
                ]);

                return redirect()->route('settings.profile')
                    ->with('success', '🗑️ Avatar berhasil dihapus!');
            }

            return redirect()->route('settings.profile')
                ->with('info', 'ℹ️ Tidak ada avatar untuk dihapus.');

        } catch (\Exception $e) {
            return redirect()->route('settings.profile')
                ->with('error', '❌ Gagal menghapus avatar. Silakan coba lagi.');
        }
    }

    /**
     * Update password
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user = Auth::user();

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->withErrors(['current_password' => 'Password saat ini tidak benar.']);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
            'password_changed_at' => now()
        ]);

        // Log password change
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'password_changed',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'details' => json_encode(['changed_at' => now()])
        ]);

        return redirect()->back()->with('success', 'Password berhasil diperbarui.');
    }

    /**
     * Update preferences
     */
    public function updatePreferences(Request $request)
    {
        $request->validate([
            'theme' => 'nullable|in:light,solarized-dark,synth-wave',
            'language' => 'nullable|in:id,en',
            'timezone' => 'nullable|string|max:50',
            'currency' => 'nullable|string|max:10',
            'date_format' => 'nullable|in:d/m/Y,m/d/Y,Y-m-d',
            'time_format' => 'nullable|in:12,24',
        ]);

        $user = Auth::user();

        $user->update($request->only([
            'theme', 'language', 'timezone', 'currency', 'date_format', 'time_format'
        ]));

        // Log preferences update
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'preferences_updated',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'details' => json_encode($request->only([
                'theme', 'language', 'timezone', 'currency', 'date_format', 'time_format'
            ]))
        ]);

        return redirect()->back()->with('success', 'Preferensi berhasil diperbarui.');
    }

    /**
     * Generate backup codes
     */
    public function generateBackupCodes(Request $request)
    {
        $user = Auth::user();

        // Generate 10 backup codes
        $backupCodes = [];
        for ($i = 0; $i < 10; $i++) {
            $backupCodes[] = strtoupper(substr(md5(uniqid()), 0, 8));
        }

        $user->update(['backup_codes' => $backupCodes]);

        // Log backup codes generation
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'backup_codes_generated',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'details' => json_encode(['codes_count' => count($backupCodes)])
        ]);

        return redirect()->back()->with([
            'success' => 'Kode backup berhasil dibuat.',
            'backup_codes' => $backupCodes
        ]);
    }

    /**
     * Deactivate account
     */
    public function deactivateAccount(Request $request)
    {
        $request->validate([
            'password' => 'required',
            'reason' => 'nullable|string|max:500'
        ]);

        $user = Auth::user();

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            return redirect()->back()->withErrors(['password' => 'Password tidak benar.']);
        }

        // Deactivate account
        $user->update(['is_active' => false]);

        // Log account deactivation
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'account_deactivated',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'details' => json_encode(['reason' => $request->reason])
        ]);

        // Logout user
        Auth::logout();

        return redirect()->route('login')->with('info', 'Akun Anda telah dinonaktifkan.');
    }

    /**
     * Download data (GDPR compliance)
     */
    public function downloadData(Request $request)
    {
        $user = Auth::user();

        // Prepare user data
        $userData = [
            'profile' => $user->only([
                'name', 'username', 'full_name', 'email', 'phone',
                'gender', 'birth_date', 'address', 'city', 'postal_code',
                'country', 'timezone', 'language', 'theme', 'created_at'
            ]),
            'security_logs' => SecurityLog::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->get()
                ->toArray(),
            'account_status' => [
                'is_active' => $user->is_active,
                'email_verified_at' => $user->email_verified_at,
                'last_login_at' => $user->last_login_at,
                'password_changed_at' => $user->password_changed_at,
            ]
        ];

        // Log data download
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'data_downloaded',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'details' => json_encode(['download_at' => now()])
        ]);

        $fileName = 'my_money_data_' . $user->username . '_' . now()->format('Y-m-d') . '.json';

        return response()->json($userData)
            ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"')
            ->header('Content-Type', 'application/json');
    }

    /**
     * Clear security logs
     */
    public function clearSecurityLogs(Request $request)
    {
        $user = Auth::user();
        
        SecurityLog::where('user_id', $user->id)->delete();

        // Log this action
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'security_logs_cleared',
            'description' => 'User cleared all security logs',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return redirect()->route('settings.security')
            ->with('success', 'Log keamanan berhasil dibersihkan!');
    }

    /**
     * Display profile & account page
     */
    public function profile()
    {
        $user = Auth::user();
        
        // Get profile statistics
        $profileStats = [
            'profile_completed' => $this->calculateProfileCompletion($user),
            'avatar_uploaded' => !empty($user->avatar),
            'email_verified' => $user->hasVerifiedEmail(),
            'phone_verified' => !empty($user->phone_verified_at),
            'two_factor_enabled' => !empty($user->two_factor_secret),
            'last_profile_update' => $user->updated_at,
        ];

        return view('settings.profile', compact('user', 'profileStats'));
    }

    /**
     * Display security page
     */
    public function security()
    {
        $user = Auth::user();
        
        // Get security statistics
        $securityStats = [
            'total_logins' => SecurityLog::where('user_id', $user->id)
                ->where('event_type', 'login_success')
                ->count(),
            'failed_attempts' => SecurityLog::where('user_id', $user->id)
                ->where('event_type', 'login_failed')
                ->where('created_at', '>=', now()->subDays(30))
                ->count(),
            'last_login' => $user->last_login_at,
        ];

        // Get recent security activities
        $recentActivities = SecurityLog::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return view('settings.security', compact('user', 'securityStats', 'recentActivities'));
    }

    /**
     * Display notifications page
     */    public function notifications()
    {
        $user = Auth::user();
        
        // Get or create notification settings
        $notificationSetting = NotificationSetting::firstOrCreate(
            ['user_id' => $user->id],
            [
                'email_enabled' => true,
                'email_login_alerts' => true,
                'email_security_alerts' => true,
                'email_transaction_alerts' => true,
                'email_budget_alerts' => true,
                'email_weekly_summary' => false,
                'email_monthly_report' => false,
                'web_enabled' => true,
                'web_transaction_alerts' => true,
                'web_budget_warnings' => true,
                'web_goal_progress' => true,
                'web_reminders' => true,
            ]
        );

        // Get recent security logs for notification settings page
        $securityLogs = SecurityLog::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('settings.notifications', compact('user', 'notificationSetting', 'securityLogs'));
    }

    /**
     * Display backup & restore page
     */
    public function backup()
    {
        $user = Auth::user();
        
        // Get backup history
        $backupLogs = BackupLog::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        // Calculate backup statistics
        $backupStats = [
            'total_backups' => BackupLog::where('user_id', $user->id)->count(),
            'successful_backups' => BackupLog::where('user_id', $user->id)->where('status', 'success')->count(),
            'failed_backups' => BackupLog::where('user_id', $user->id)->where('status', 'failed')->count(),
            'last_backup' => BackupLog::where('user_id', $user->id)->where('status', 'success')->latest()->first(),
            'total_backup_size' => BackupLog::where('user_id', $user->id)->where('status', 'success')->sum('file_size'),
        ];

        return view('settings.backup', compact('user', 'backupLogs', 'backupStats'));
    }

    /**
     * Update notification settings
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();
        
        $validatedData = $request->validate([
            'email_enabled' => 'boolean',
            'email_login_alerts' => 'boolean',
            'email_security_alerts' => 'boolean',
            'email_transaction_alerts' => 'boolean',
            'email_budget_alerts' => 'boolean',
            'email_weekly_summary' => 'boolean',
            'email_monthly_report' => 'boolean',
            'web_enabled' => 'boolean',
            'web_transaction_alerts' => 'boolean',
            'web_budget_warnings' => 'boolean',
            'web_goal_progress' => 'boolean',
            'web_reminders' => 'boolean',
        ]);

        // Convert checkboxes to boolean (unchecked = false)
        foreach ($validatedData as $key => $value) {
            $validatedData[$key] = $request->boolean($key);
        }

        NotificationSetting::updateOrCreate(
            ['user_id' => $user->id],
            $validatedData
        );

        // Log security activity
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'notification_settings_updated',
            'description' => 'User updated notification preferences',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return redirect()->route('settings.notifications')
            ->with('success', 'Pengaturan notifikasi berhasil diperbarui!');
    }

    /**
     * Create backup
     */
    public function createBackup(Request $request)
    {
        $user = Auth::user();
        
        try {
            // Start backup process
            $backupLog = BackupLog::create([
                'user_id' => $user->id,
                'backup_type' => $request->input('backup_type', 'full'),
                'status' => 'processing',
                'file_name' => 'backup_' . $user->id . '_' . now()->format('Y_m_d_H_i_s') . '.json',
            ]);

            // Prepare backup data
            $backupData = [
                'user_profile' => $user->toArray(),
                'security_logs' => SecurityLog::where('user_id', $user->id)->get()->toArray(),
                'notification_settings' => NotificationSetting::where('user_id', $user->id)->first()?->toArray(),
                'backup_info' => [
                    'created_at' => now(),
                    'version' => '1.0',
                    'event_type' => $request->input('backup_type', 'full'),
                ],
            ];

            // Save backup file
            $fileName = $backupLog->file_name;
            $filePath = 'backups/user_' . $user->id . '/' . $fileName;
            
            Storage::disk('local')->put($filePath, json_encode($backupData, JSON_PRETTY_PRINT));
            
            // Update backup log
            $backupLog->update([
                'status' => 'success',
                'file_path' => $filePath,
                'file_size' => Storage::disk('local')->size($filePath),
                'completed_at' => now(),
            ]);

            // Log security activity
            SecurityLog::create([
                'user_id' => $user->id,
                'event_type' => 'backup_created',
                'description' => 'User created data backup',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return redirect()->route('settings.backup')
                ->with('success', 'Backup berhasil dibuat!');

        } catch (\Exception $e) {
            // Update backup log with error
            if (isset($backupLog)) {
                $backupLog->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                    'completed_at' => now(),
                ]);
            }

            return redirect()->route('settings.backup')
                ->with('error', 'Gagal membuat backup: ' . $e->getMessage());
        }
    }

    /**
     * Download backup file
     */
    public function downloadBackup(BackupLog $backupLog)
    {
        $user = Auth::user();
        
        // Check if user owns this backup
        if ($backupLog->user_id !== $user->id) {
            abort(403);
        }

        // Check if file exists
        if (!Storage::disk('local')->exists($backupLog->file_path)) {
            return redirect()->route('settings.backup')
                ->with('error', 'File backup tidak ditemukan.');
        }

        // Log download activity
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'backup_downloaded',
            'description' => 'User downloaded backup file: ' . $backupLog->file_name,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        return Storage::disk('local')->download($backupLog->file_path, $backupLog->file_name);
    }

    /**
     * Show 2FA setup page
     */
    public function show2FASetup()
    {
        $user = Auth::user();
        
        // Check if 2FA is already enabled
        if ($user->has2FAEnabled()) {
            return redirect()->route('settings.security')
                ->with('info', 'Two-Factor Authentication sudah aktif.');
        }
        
        // Generate secret if not exists
        if (!$user->two_factor_secret) {
            $user->generate2FASecret();
        }
        
        $qrCodeUrl = $user->get2FAQRCodeUrl();
        $secret = $user->get2FASecret();
        
        // Generate QR code as SVG
        $qrCodeImage = null;
        try {
            $renderer = new ImageRenderer(
                new RendererStyle(200),
                new SvgImageBackEnd()
            );
            $writer = new Writer($renderer);
            $qrCodeSvg = $writer->writeString($qrCodeUrl);
            $qrCodeImage = 'data:image/svg+xml;base64,' . base64_encode($qrCodeSvg);
        } catch (\Exception $e) {
            // Fallback: use URL for JavaScript generation
            \Log::warning('QR Code generation failed: ' . $e->getMessage());
        }
        
        return view('settings.2fa-setup', compact('qrCodeUrl', 'secret', 'qrCodeImage'));
    }
    
    /**
     * Verify and Enable 2FA
     */
    public function enable2FA(Request $request)
    {
        $request->validate([
            'verification_code' => 'required|string|size:6',
        ], [
            'verification_code.required' => 'Kode verifikasi wajib diisi.',
            'verification_code.size' => 'Kode verifikasi harus 6 digit.',
        ]);
        
        $user = Auth::user();
        
        // Verify the code
        if (!$user->verify2FACode($request->verification_code)) {
            return back()->withErrors([
                'verification_code' => 'Kode verifikasi tidak valid. Pastikan waktu di device Anda sudah benar.'
            ]);
        }
        
        // Enable 2FA
        $user->enable2FA();
        
        // Refresh user model to get updated data
        $user->refresh();
        
        // Generate and get backup codes
        $backupCodes = $user->generateBackupCodes();
        
        // Debug logging
        \Log::info('2FA Enabled for user', [
            'user_id' => $user->id,
            'two_factor_enabled' => $user->two_factor_enabled,
            'two_factor_confirmed_at' => $user->two_factor_confirmed_at,
            'has_secret' => !empty($user->two_factor_secret),
            'backup_codes_count' => count($backupCodes),
            'has2FA_check' => $user->has2FAEnabled()
        ]);
        
        return redirect()->route('settings.2fa.show')
            ->with('backup_codes', $backupCodes)
            ->with('success', 'Two-Factor Authentication berhasil diaktifkan!');
    }
    
    /**
     * Show 2FA enabled success page with backup codes
     */
    public function show2FAEnabled()
    {
        $user = Auth::user();
        
        if (!$user->has2FAEnabled()) {
            return redirect()->route('settings.security');
        }
        
        $backupCodes = session('backup_codes') ?? $user->backup_codes;
        
        return view('settings.2fa-enabled', compact('backupCodes'));
    }

    /**
     * Disable 2FA
     */
    public function disable2FA(Request $request)
    {
        $request->validate([
            'current_password' => ['required', function ($attribute, $value, $fail) {
                if (!Hash::check($value, Auth::user()->password)) {
                    $fail('Password saat ini tidak benar.');
                }
            }],
            'verification_code' => 'required|string',
        ], [
            'verification_code.required' => 'Kode verifikasi atau backup code wajib diisi.',
        ]);

        $user = Auth::user();
        
        // Verify 2FA code or backup code
        $isValidCode = $user->verify2FACode($request->verification_code);
        $isValidBackup = $user->useBackupCode($request->verification_code);
        
        if (!$isValidCode && !$isValidBackup) {
            return back()->withErrors([
                'verification_code' => 'Kode verifikasi atau backup code tidak valid.'
            ]);
        }
        
        // Disable 2FA
        $user->disable2FA();

        return redirect()->route('settings.security')
            ->with('success', 'Two-Factor Authentication berhasil dinonaktifkan!');
    }
    
    /**
     * Regenerate backup codes
     */
    public function regenerateBackupCodes(Request $request)
    {
        $request->validate([
            'current_password' => ['required', function ($attribute, $value, $fail) {
                if (!Hash::check($value, Auth::user()->password)) {
                    $fail('Password saat ini tidak benar.');
                }
            }],
        ]);
        
        $user = Auth::user();
        
        if (!$user->has2FAEnabled()) {
            return redirect()->route('settings.security')
                ->with('error', 'Two-Factor Authentication belum aktif.');
        }
        
        $newCodes = $user->generateBackupCodes();
        
        return view('settings.backup-codes', compact('newCodes'))
            ->with('success', 'Backup codes baru berhasil dibuat!');
    }

    /**
     * Calculate profile completion percentage
     */
    private function calculateProfileCompletion($user)
    {
        $fields = [
            'name' => !empty($user->name),
            'email' => !empty($user->email),
            'phone' => !empty($user->phone),
            'gender' => !empty($user->gender),
            'birth_date' => !empty($user->birth_date),
            'avatar' => !empty($user->avatar),
            'email_verified' => $user->hasVerifiedEmail(),
        ];

        $completed = count(array_filter($fields));
        $total = count($fields);

        return round(($completed / $total) * 100);
    }

    /**
     * Get recent login locations
     */
    private function getRecentLoginLocations($userId)
    {
        return SecurityLog::where('user_id', $userId)
            ->where('event_type', 'login_success')
            ->select('ip_address', 'created_at')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($log) {
                return [
                    'ip' => $log->ip_address,
                    'location' => $this->getLocationFromIP($log->ip_address),
                    'date' => $log->created_at,
                ];
            });
    }

    /**
     * Get location from IP (simplified)
     */
    private function getLocationFromIP($ip)
    {
        // This is a simplified version. In production, you'd use a proper IP geolocation service
        if ($ip === '127.0.0.1' || $ip === '::1') {
            return 'Local Development';
        }
        
        return 'Unknown Location';
    }    /**
     * Generate backup codes for 2FA
     */
    private function createBackupCodes()
    {
        $codes = [];
        for ($i = 0; $i < 10; $i++) {
            $codes[] = sprintf('%04d-%04d', mt_rand(1000, 9999), mt_rand(1000, 9999));
        }
        return $codes;
    }

    /**
     * Generate new backup codes
     */
    public function generateBackupCodesAction(Request $request)
    {
        $user = Auth::user();
        
        $backupCodes = $this->createBackupCodes();
        
        $user->update([
            'backup_codes' => $backupCodes
        ]);

        // Log security activity
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'backup_codes_generated',
            'description' => 'User generated new backup codes',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return redirect()->route('settings.security')
            ->with('success', 'Kode backup baru berhasil dibuat!');
    }

    /**
     * Delete backup
     */
    public function deleteBackup(BackupLog $backupLog)
    {
        $user = Auth::user();
        
        // Check if user owns this backup
        if ($backupLog->user_id !== $user->id) {
            abort(403);
        }

        // Delete the file if exists
        if ($backupLog->file_path && Storage::disk('local')->exists($backupLog->file_path)) {
            Storage::disk('local')->delete($backupLog->file_path);
        }

        // Delete the record
        $backupLog->delete();

        // Log deletion
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'backup_deleted',
            'description' => 'User deleted backup: ' . $backupLog->file_name,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Cleanup old backups
     */
    public function cleanupBackups(Request $request)
    {
        $user = Auth::user();
        
        // Delete backups older than 30 days
        $oldBackups = BackupLog::where('user_id', $user->id)
            ->where('created_at', '<', now()->subDays(30))
            ->get();

        $deletedCount = 0;
        foreach ($oldBackups as $backup) {
            if ($backup->file_path && Storage::disk('local')->exists($backup->file_path)) {
                Storage::disk('local')->delete($backup->file_path);
            }
            $backup->delete();
            $deletedCount++;
        }

        // Log cleanup
        SecurityLog::create([
            'user_id' => $user->id,
            'event_type' => 'backup_cleanup',
            'description' => "User cleaned up {$deletedCount} old backups",
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return response()->json(['success' => true, 'deleted_count' => $deletedCount]);
    }

    /**
     * Restore backup data (placeholder)
     */
    public function restoreBackup(Request $request)
    {
        $request->validate([
            'backup_file' => 'required|file|mimes:json,zip|max:10240', // 10MB max
        ]);

        $user = Auth::user();
        
        try {
            $file = $request->file('backup_file');
            $content = file_get_contents($file->getRealPath());
            
            // For JSON files
            if ($file->getClientOriginalExtension() === 'json') {
                $backupData = json_decode($content, true);
                
                if (!$backupData) {
                    throw new \Exception('Invalid backup file format');
                }

                // Log restore attempt
                SecurityLog::create([
                    'user_id' => $user->id,
                    'event_type' => 'backup_restore_attempted',
                    'description' => 'User attempted to restore backup',
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);

                return redirect()->route('settings.backup')
                    ->with('info', 'Fitur restore sedang dalam pengembangan. Data backup valid.');
            }

            throw new \Exception('Unsupported file format');

        } catch (\Exception $e) {
            return redirect()->route('settings.backup')
                ->with('error', 'Gagal restore backup: ' . $e->getMessage());
        }
    }

    /**
     * Update profile using GET method to bypass WAF
     */
    public function updateProfileSafe(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Decode base64 data if provided
            $data = [];
            
            if ($request->has('d')) {
                $decodedData = json_decode(base64_decode($request->get('d')), true);
                if ($decodedData) {
                    // Update with decoded data
                    foreach ($decodedData as $key => $value) {
                        if (in_array($key, ['full_name', 'username', 'email', 'phone', 'birth_date', 'gender', 'address', 'city', 'postal_code', 'country', 'bio', 'language', 'timezone'])) {
                            if ($key === 'email' && $value !== $user->email) {
                                // Check email uniqueness
                                $exists = \App\Models\User::where('email', $value)->where('id', '!=', $user->id)->exists();
                                if (!$exists && filter_var($value, FILTER_VALIDATE_EMAIL)) {
                                    $data[$key] = $value;
                                }
                            } elseif ($key === 'username' && $value !== $user->username) {
                                // Check username uniqueness
                                $exists = \App\Models\User::where('username', $value)->where('id', '!=', $user->id)->exists();
                                if (!$exists) {
                                    $data[$key] = $value;
                                }
                            } else {
                                $data[$key] = $value;
                            }
                        }
                    }
                }
            }
            
            // Update user
            if (!empty($data)) {
                $user->update($data);
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Update failed'
            ], 500);
        }
    }

    // ===== NOTIFICATION API METHODS =====
    
    /**
     * Get user notifications for API
     */
    public function getNotifications(Request $request)
    {
        $user = Auth::user();
        
        $query = $user->notifications()
            ->orderBy('created_at', 'desc');
        
        // Apply filters
        if ($request->has('type') && $request->type !== 'all') {
            if ($request->type === 'unread') {
                $query->whereNull('read_at');
            } elseif ($request->type === 'important') {
                $query->where('is_important', true);
            } else {
                $query->where('type', $request->type);
            }
        }
        
        $notifications = $query->paginate($request->get('limit', 20));
        
        return response()->json([
            'success' => true,
            'notifications' => $notifications->items(),
            'pagination' => [
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'total' => $notifications->total(),
                'per_page' => $notifications->perPage(),
            ]
        ]);
    }
    
    /**
     * Get recent notifications since timestamp
     */
    public function getRecentNotifications(Request $request)
    {
        $user = Auth::user();
        $since = $request->get('since', now()->subMinutes(5)->toISOString());
        
        $notifications = $user->notifications()
            ->where('created_at', '>', $since)
            ->orderBy('created_at', 'desc')
            ->get();
        
        return response()->json([
            'success' => true,
            'notifications' => $notifications,
            'count' => $notifications->count()
        ]);
    }
    
    /**
     * Mark notification as read
     */
    public function markNotificationAsRead(Notification $notification)
    {
        // Check if notification belongs to current user
        if ($notification->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $notification->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);
    }
    
    /**
     * Delete notification
     */
    public function deleteNotification(Notification $notification)
    {
        // Check if notification belongs to current user
        if ($notification->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $notification->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Notification deleted'
        ]);
    }
    
    /**
     * Mark all notifications as read
     */
    public function markAllNotificationsAsRead()
    {
        $user = Auth::user();
        
        $user->notifications()
            ->whereNull('read_at')
            ->update(['read_at' => now()]);
        
        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }
    
    /**
     * Clear all notifications
     */
    public function clearAllNotifications()
    {
        $user = Auth::user();
        
        $user->notifications()->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'All notifications cleared'
        ]);
    }
    
    /**
     * Create test notification
     */
    public function createTestNotification(Request $request)
    {
        $request->validate([
            'type' => 'required|in:success,warning,error,info',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
        ]);
        
        $user = Auth::user();
        
        $notification = Notification::createForUser($user->id, [
            'type' => $request->type,
            'title' => $request->title,
            'message' => $request->message,
            'is_important' => $request->type === 'error',
            'channel' => 'web',
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Test notification created',
            'notification' => $notification
        ]);
    }
    
    /**
     * Create system notification for user
     */
    public static function createNotificationForUser($userId, $data)
    {
        return Notification::createForUser($userId, $data);
    }
    
    /**
     * Create notification for current user
     */
    public function createNotification($data)
    {
        return self::createNotificationForUser(Auth::id(), $data);
    }

    /**
     * Show notification detail
     */
    public function showNotification(Notification $notification)
    {
        $user = Auth::user();
        
        // Check if notification belongs to user
        if ($notification->user_id !== $user->id) {
            abort(403);
        }
        
        // Get related notifications (same type, recent)
        $relatedNotifications = Notification::where('user_id', $user->id)
            ->where('type', $notification->type)
            ->where('id', '!=', $notification->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        
        // Determine type color
        $typeColors = [
            'success' => 'success',
            'error' => 'danger',
            'warning' => 'warning',
            'info' => 'info'
        ];
        $typeColor = $typeColors[$notification->type] ?? 'secondary';
        
        return view('notifications.detail', compact('notification', 'relatedNotifications', 'typeColor'));
    }
}
