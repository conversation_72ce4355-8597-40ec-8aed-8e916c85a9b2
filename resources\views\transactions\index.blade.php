@extends('layouts.dashboard')

@section('title', 'Riwayat Transaksi')

@section('page-title', '📊 Riwayat Transaksi - Daftar Lengkap')

@push('styles')
<style>
    /* Dashboard container consistency */
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    .transactions-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
    }
    
    /* Synth-wave theme overrides */
    body[data-theme="synth-wave"] .transactions-header {
        background: rgba(26, 26, 26, 0.9) !important;
        border: 1px solid #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.3) !important;
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .transactions-header h2 {
        color: #ff00ff !important;
        text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
    }
    
    body[data-theme="synth-wave"] .transactions-header p {
        color: #00ffff !important;
    }
    
    .transactions-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        animation: grain 20s linear infinite;
        pointer-events: none;
    }
    
    @keyframes grain {
        0%, 100% { transform: translate(0, 0); }
        25% { transform: translate(-10px, -10px); }
        50% { transform: translate(-20px, 0); }
        75% { transform: translate(-10px, 10px); }
    }
    
    .transactions-header h2 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 10px 0;
        position: relative;
        z-index: 2;
    }
    
    .transactions-header p {
        margin: 0;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .filter-card {
        background: #fdf6e3;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    /* Synth-wave filter card */
    body[data-theme="synth-wave"] .filter-card {
        background: #1a1a1a !important;
        border: 1px solid #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.2) !important;
        color: #ffffff !important;
    }
    
    .filter-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }
    
    body[data-theme="synth-wave"] .filter-card:hover {
        box-shadow: 0 0 30px rgba(255, 0, 255, 0.4) !important;
    }
    
    .transactions-card {
        background: #fdf6e3;
        border-radius: 20px;
        padding: 0;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    /* Synth-wave transactions card */
    body[data-theme="synth-wave"] .transactions-card {
        background: #1a1a1a !important;
        border: 1px solid #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.2) !important;
    }
    
    .transactions-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }
    
    body[data-theme="synth-wave"] .transactions-card:hover {
        box-shadow: 0 0 30px rgba(255, 0, 255, 0.4) !important;
    }
    
    .filter-header {
        display: flex;
        align-items: center;
        justify-content: between;
        margin-bottom: 25px;
        cursor: pointer;
    }
    
    .filter-content {
        display: none;
    }
    
    .filter-card.active .filter-content {
        display: block;
        animation: slideDown 0.3s ease;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .toggle-arrow {
        transition: transform 0.3s ease;
        margin-left: auto;
    }
    
    .filter-card.active .toggle-arrow {
        transform: rotate(180deg);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    /* Synth-wave form labels */
    body[data-theme="synth-wave"] .form-label {
        color: #ff00ff !important;
    }
    
    .form-label i {
        color: #667eea;
    }
    
    body[data-theme="synth-wave"] .form-label i {
        color: #00ffff !important;
    }
    
    .form-control {
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        transition: all 0.3s ease;
        background: #faf8f5;
    }
    
    /* Synth-wave form controls */
    body[data-theme="synth-wave"] .form-control {
        background-color: #1a1a1a !important;
        color: #ffffff !important;
        border-color: #ff00ff !important;
    }
    
    body[data-theme="synth-wave"] .form-control::placeholder {
        color: #00ffff !important;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
        background: white;
    }
    
    body[data-theme="synth-wave"] .form-control:focus {
        border-color: #00ffff !important;
        box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.25) !important;
        background: #1a1a1a !important;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    /* Synth-wave stat cards */
    body[data-theme="synth-wave"] .stat-card {
        background: linear-gradient(135deg, #ff00ff 0%, #00ffff 100%) !important;
        border: 1px solid #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.3) !important;
    }
    
    .stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        transition: all 0.5s ease;
    }
    
    .stat-card:hover::before {
        animation: shine 1s ease-in-out;
    }
    
    @keyframes shine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
        position: relative;
        z-index: 2;
    }
    
    .stat-label {
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .stat-icon {
        position: absolute;
        right: 20px;
        top: 20px;
        font-size: 2rem;
        opacity: 0.3;
    }
    
    .table-header {
        background: #f4f0e8;
        padding: 20px 30px;
        border-bottom: 1px solid #e6ddd1;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    /* Synth-wave table header */
    body[data-theme="synth-wave"] .table-header {
        background: linear-gradient(135deg, #1a1a1a 0%, #2a0040 100%) !important;
        border-bottom: 1px solid #ff00ff !important;
    }
    
    body[data-theme="synth-wave"] .table-header h5 {
        color: #ff00ff !important;
        text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
    }
    
    .transactions-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .transactions-table th {
        background: #f4f0e8;
        padding: 15px;
        text-align: center;
        font-weight: 600;
        color: #333;
        border-bottom: 2px solid #e6ddd1;
    }
    
    /* Synth-wave table headers */
    body[data-theme="synth-wave"] .transactions-table th {
        background: #1a1a1a !important;
        color: #ff00ff !important;
        border-bottom: 2px solid #ff00ff !important;
    }
    
    .transactions-table td {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        vertical-align: middle;
        text-align: center;
    }
    
    /* Synth-wave table cells */
    body[data-theme="synth-wave"] .transactions-table td {
        border-bottom: 1px solid #333333 !important;
        color: #ffffff !important;
    }
    
    .transactions-table tbody tr {
        transition: background 0.3s ease;
    }
    
    .transactions-table tbody tr:hover {
        background: #f9f7f4;
    }
    
    body[data-theme="synth-wave"] .transactions-table tbody tr:hover {
        background: rgba(255, 0, 255, 0.1) !important;
    }
    
    .transaction-type {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
    
    .type-income {
        background: #d4edda;
        color: #155724;
    }
    
    .type-expense {
        background: #f8d7da;
        color: #721c24;
    }
    
    .type-transfer {
        background: #cce7ff;
        color: #004085;
    }
    
    .amount-income {
        color: #28a745;
        font-weight: 600;
    }
    
    .amount-expense {
        color: #dc3545;
        font-weight: 600;
    }
    
    .amount-transfer {
        color: #007bff;
        font-weight: 600;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        margin: 0 auto;
        flex-wrap: nowrap;
    }
    
    /* Specific styling for action column */
    .transactions-table td:last-child {
        text-align: center !important;
        vertical-align: middle !important;
        min-width: 180px;
    }
    
    .btn-action {
        background: none !important;
        border: none !important;
        padding: 6px 8px !important;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.8rem;
        position: relative;
        box-shadow: none !important;
        border-radius: 6px !important;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        min-width: 50px;
        justify-content: center;
    }
    
    .btn-action::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover::after {
        width: 100%;
    }
    
    .btn-action:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        transform: translateY(-1px);
    }
    
    .btn-view {
        color: #17a2b8;
    }
    
    .btn-view::after {
        background: linear-gradient(90deg, #17a2b8, #138496);
    }
    
    .btn-view:hover {
        transform: translateY(-1px);
        color: #17a2b8;
    }
    
    body[data-theme="synth-wave"] .btn-view {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .btn-view::after {
        background: linear-gradient(90deg, #00ffff, #0099cc) !important;
    }
    
    body[data-theme="synth-wave"] .btn-view:hover {
        text-shadow: 0 0 8px #00ffff !important;
        color: #00ffff !important;
        transform: translateY(-1px);
    }
    
    body[data-theme="synth-wave"] .btn-view:hover::after {
        box-shadow: 0 0 8px #00ffff !important;
    }
    
    .btn-edit {
        color: #ffc107;
    }
    
    .btn-edit::after {
        background: linear-gradient(90deg, #ffc107, #e0a800);
    }
    
    .btn-edit:hover {
        transform: translateY(-1px);
        color: #ffc107;
    }
    
    body[data-theme="synth-wave"] .btn-edit {
        color: #ffff00 !important;
    }
    
    body[data-theme="synth-wave"] .btn-edit::after {
        background: linear-gradient(90deg, #ffff00, #ff9900) !important;
    }
    
    body[data-theme="synth-wave"] .btn-edit:hover {
        text-shadow: 0 0 8px #ffff00 !important;
        color: #ffff00 !important;
        transform: translateY(-1px);
    }
    
    body[data-theme="synth-wave"] .btn-edit:hover::after {
        box-shadow: 0 0 8px #ffff00 !important;
    }
    
    .btn-delete {
        color: #dc3545;
    }
    
    .btn-delete::after {
        background: linear-gradient(90deg, #dc3545, #c82333);
    }
    
    .btn-delete:hover {
        transform: translateY(-1px);
        color: #dc3545;
    }
    
    body[data-theme="synth-wave"] .btn-delete {
        color: #ff0066 !important;
    }
    
    body[data-theme="synth-wave"] .btn-delete::after {
        background: linear-gradient(90deg, #ff0066, #cc0033) !important;
    }
    
    body[data-theme="synth-wave"] .btn-delete:hover {
        text-shadow: 0 0 8px #ff0066 !important;
        color: #ff0066 !important;
        transform: translateY(-1px);
    }
    
    body[data-theme="synth-wave"] .btn-delete:hover::after {
        box-shadow: 0 0 8px #ff0066 !important;
    }
    
    .btn-action i {
        transition: all 0.3s ease;
    }
    
    .btn-action:hover i {
        transform: scale(1.1);
    }
    
    .pagination-wrapper {
        padding: 20px 30px;
        background: #f4f0e8;
        border-top: 1px solid #e6ddd1;
        display: flex;
        align-items: center;
        justify-content: between;
    }
    
    /* Synth-wave pagination */
    body[data-theme="synth-wave"] .pagination-wrapper {
        background: #1a1a1a !important;
        border-top: 1px solid #ff00ff !important;
        color: #ffffff !important;
    }
    
    .export-buttons {
        display: flex;
        gap: 20px;
        margin-left: auto;
        align-items: center;
    }
    
    .btn-export {
        background: none !important;
        border: none !important;
        padding: 8px 0 !important;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 0.95rem;
        position: relative;
        box-shadow: none !important;
        border-radius: 0 !important;
    }
    
    .btn-export::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        transition: all 0.3s ease;
    }
    
    .btn-export:hover::after {
        width: 100%;
    }
    
    .btn-pdf {
        color: #dc3545;
    }
    
    .btn-pdf::after {
        background: linear-gradient(90deg, #dc3545, #c82333);
    }
    
    .btn-pdf:hover {
        transform: translateY(-2px);
        color: #dc3545;
    }
    
    body[data-theme="synth-wave"] .btn-pdf {
        color: #ff0066 !important;
    }
    
    body[data-theme="synth-wave"] .btn-pdf::after {
        background: linear-gradient(90deg, #ff0066, #cc0033) !important;
    }
    
    body[data-theme="synth-wave"] .btn-pdf:hover {
        text-shadow: 0 0 10px #ff0066 !important;
        color: #ff0066 !important;
        transform: translateY(-2px);
    }
    
    body[data-theme="synth-wave"] .btn-pdf:hover::after {
        box-shadow: 0 0 10px #ff0066 !important;
    }
    
    .btn-excel {
        color: #28a745;
    }
    
    .btn-excel::after {
        background: linear-gradient(90deg, #28a745, #218838);
    }
    
    .btn-excel:hover {
        transform: translateY(-2px);
        color: #28a745;
    }
    
    body[data-theme="synth-wave"] .btn-excel {
        color: #00ff66 !important;
    }
    
    body[data-theme="synth-wave"] .btn-excel::after {
        background: linear-gradient(90deg, #00ff66, #00cc55) !important;
    }
    
    body[data-theme="synth-wave"] .btn-excel:hover {
        text-shadow: 0 0 10px #00ff66 !important;
        color: #00ff66 !important;
        transform: translateY(-2px);
    }
    
    body[data-theme="synth-wave"] .btn-excel:hover::after {
        box-shadow: 0 0 10px #00ff66 !important;
    }
    
    .btn-csv {
        color: #007bff;
    }
    
    .btn-csv::after {
        background: linear-gradient(90deg, #007bff, #0056b3);
    }
    
    .btn-csv:hover {
        transform: translateY(-2px);
        color: #007bff;
    }
    
    body[data-theme="synth-wave"] .btn-csv {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .btn-csv::after {
        background: linear-gradient(90deg, #00ffff, #0099cc) !important;
    }
    
    body[data-theme="synth-wave"] .btn-csv:hover {
        text-shadow: 0 0 10px #00ffff !important;
        color: #00ffff !important;
        transform: translateY(-2px);
    }
    
    body[data-theme="synth-wave"] .btn-csv:hover::after {
        box-shadow: 0 0 10px #00ffff !important;
    }
    
    .btn-export i {
        transition: all 0.3s ease;
    }
    
    .btn-export:hover i {
        transform: scale(1.1);
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 30px;
        color: #666;
    }
    
    .empty-state i {
        font-size: 4rem;
        color: #ddd;
        margin-bottom: 20px;
    }
    
    .loading-table {
        text-align: center;
        padding: 40px;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Theme support */
    body[data-theme="solarized-dark"] .filter-card,
    body[data-theme="solarized-dark"] .transactions-card {
        background: #fdf6e3;
    }
    
    body[data-theme="solarized-dark"] .form-control {
        background: #faf8f5;
    }
    
    body[data-theme="solarized-dark"] .form-label {
        color: #586e75;
    }
    
    body[data-theme="solarized-dark"] .form-label i {
        color: #268bd2;
    }
    
    body[data-theme="synth-wave"] .filter-card,
    body[data-theme="synth-wave"] .transactions-card {
        background: rgba(0, 0, 0, 0.8);
        border: 1px solid #ff00ff;
    }
    
    body[data-theme="synth-wave"] .form-control {
        background: rgba(0, 0, 0, 0.6);
        color: #ffffff;
        border-color: #ff00ff;
    }
    
    body[data-theme="synth-wave"] .form-label {
        color: #ffffff;
    }
    
    body[data-theme="synth-wave"] .form-label i {
        color: #ff00ff;
    }
    
    body[data-theme="synth-wave"] .transactions-table th {
        background: rgba(255, 0, 255, 0.1);
        color: #ffffff;
        border-color: #ff00ff;
    }
    
    body[data-theme="synth-wave"] .transactions-table td {
        color: #ffffff;
        border-color: rgba(255, 0, 255, 0.2);
    }
    
    body[data-theme="synth-wave"] .transactions-table tbody tr:hover {
        background: rgba(255, 0, 255, 0.1);
    }
    
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 15px !important;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .transactions-table {
            font-size: 0.85rem;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .export-buttons {
            flex-direction: column;
            margin-left: 0;
            margin-top: 15px;
        }
        
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Header -->
    <div class="transactions-header">
        <h2><i class="fas fa-list-alt"></i> Riwayat Transaksi</h2>
        <p>Kelola dan pantau semua aktivitas keuangan Anda dengan mudah</p>
    </div>
    
    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon"><i class="fas fa-arrow-up"></i></div>
            <div class="stat-value" id="totalIncome">Rp 0</div>
            <div class="stat-label">Total Pemasukan</div>
        </div>
        <div class="stat-card" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
            <div class="stat-icon"><i class="fas fa-arrow-down"></i></div>
            <div class="stat-value" id="totalExpense">Rp 0</div>
            <div class="stat-label">Total Pengeluaran</div>
        </div>
        <div class="stat-card" style="background: linear-gradient(135deg, #007bff 0%, #17a2b8 100%);">
            <div class="stat-icon"><i class="fas fa-exchange-alt"></i></div>
            <div class="stat-value" id="totalTransfer">Rp 0</div>
            <div class="stat-label">Total Transfer</div>
        </div>
        <div class="stat-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="stat-icon"><i class="fas fa-calculator"></i></div>
            <div class="stat-value" id="netIncome">Rp 0</div>
            <div class="stat-label">Saldo Bersih</div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="filter-card active">
        <div class="filter-header">
            <h5><i class="fas fa-filter"></i> Filter & Pencarian</h5>
            <i class="fas fa-chevron-down toggle-arrow"></i>
        </div>
        <div class="filter-content">
            <form id="filterForm">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-layer-group"></i> Jenis
                            </label>
                            <select name="type" id="filterType" class="form-control">
                                <option value="">Semua Jenis</option>
                                <option value="income">Pemasukan</option>
                                <option value="expense">Pengeluaran</option>
                                <option value="transfer">Transfer</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-wallet"></i> Akun
                            </label>
                            <select name="account_id" id="filterAccount" class="form-control">
                                <option value="">Semua Akun</option>
                                @foreach($accounts as $account)
                                    <option value="{{ $account->id }}">{{ $account->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tags"></i> Kategori
                            </label>
                            <select name="category_id" id="filterCategory" class="form-control">
                                <option value="">Semua Kategori</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-search"></i> Pencarian
                            </label>
                            <input type="text" name="search" id="filterSearch" class="form-control" 
                                   placeholder="Cari judul atau deskripsi...">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-calendar"></i> Dari Tanggal
                            </label>
                            <input type="date" name="date_from" id="filterDateFrom" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-calendar"></i> Sampai Tanggal
                            </label>
                            <input type="date" name="date_to" id="filterDateTo" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-sort-amount-down"></i> Jumlah Min
                            </label>
                            <input type="number" name="amount_min" id="filterAmountMin" class="form-control" 
                                   placeholder="0" step="1000">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-sort-amount-up"></i> Jumlah Max
                            </label>
                            <input type="number" name="amount_max" id="filterAmountMax" class="form-control" 
                                   placeholder="Tidak terbatas" step="1000">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-search"></i> Terapkan Filter
                        </button>
                        <button type="button" class="btn btn-secondary ml-2" onclick="resetFilters()">
                            <i class="fas fa-refresh"></i> Reset
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Transactions Table -->
    <div class="transactions-card">
        <div class="table-header">
            <h5><i class="fas fa-table"></i> Daftar Transaksi</h5>
            <div class="export-buttons">
                <button class="btn-export btn-pdf" onclick="exportData('pdf')">
                    <i class="fas fa-file-pdf"></i> PDF
                </button>
                <button class="btn-export btn-excel" onclick="exportData('excel')">
                    <i class="fas fa-file-excel"></i> Excel
                </button>
                <button class="btn-export btn-csv" onclick="exportData('csv')">
                    <i class="fas fa-file-csv"></i> CSV
                </button>
            </div>
        </div>
        
        <div id="tableContainer">
            <div class="loading-table">
                <div class="loading-spinner"></div>
                <p>Memuat data transaksi...</p>
            </div>
        </div>
        
        <div class="pagination-wrapper">
            <div id="paginationInfo"></div>
            <div id="paginationControls"></div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize
    loadTransactions();
    loadStatistics();
    
    // Filter toggle
    const filterHeader = document.querySelector('.filter-header');
    const filterCard = document.querySelector('.filter-card');
    
    filterHeader.addEventListener('click', function() {
        filterCard.classList.toggle('active');
    });
    
    // Real-time search
    const searchInput = document.getElementById('filterSearch');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            applyFilters();
        }, 500);
    });
    
    // Auto-apply filters on change
    const filterInputs = document.querySelectorAll('#filterForm select, #filterForm input[type="date"], #filterForm input[type="number"]');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            applyFilters();
        });
    });
});

// Load transactions with filters
function loadTransactions(page = 1) {
    const formData = new FormData(document.getElementById('filterForm'));
    formData.append('page', page);
    
    document.getElementById('tableContainer').innerHTML = `
        <div class="loading-table">
            <div class="loading-spinner"></div>
            <p>Memuat data transaksi...</p>
        </div>
    `;
    
    fetch('/transactions/api/data?' + new URLSearchParams(formData))
        .then(response => response.json())
        .then(data => {
            renderTransactionsTable(data);
            updatePagination(data);
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('tableContainer').innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h4>Gagal Memuat Data</h4>
                    <p>Terjadi kesalahan saat memuat data transaksi.</p>
                    <button class="btn btn-primary" onclick="loadTransactions()">
                        <i class="fas fa-refresh"></i> Coba Lagi
                    </button>
                </div>
            `;
        });
}

// Render transactions table
function renderTransactionsTable(data) {
    if (!data.data || data.data.length === 0) {
        document.getElementById('tableContainer').innerHTML = `
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <h4>Tidak Ada Transaksi</h4>
                <p>Belum ada transaksi yang sesuai dengan filter Anda.</p>
                <a href="/transactions/create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Tambah Transaksi
                </a>
            </div>
        `;
        return;
    }
    
    let tableHTML = `
        <table class="transactions-table">
            <thead>
                <tr>
                    <th class="text-center">Tanggal</th>
                    <th class="text-center">Jenis</th>
                    <th class="text-center">Judul</th>
                    <th class="text-center">Kategori</th>
                    <th class="text-center">Akun</th>
                    <th class="text-center">Jumlah</th>
                    <th class="text-center">Aksi</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.data.forEach(transaction => {
        const typeClass = `type-${transaction.type}`;
        const amountClass = `amount-${transaction.type}`;
        const typeIcon = transaction.type === 'income' ? 'arrow-up' : 
                        transaction.type === 'expense' ? 'arrow-down' : 'exchange-alt';
        const typeLabel = transaction.type === 'income' ? 'Pemasukan' : 
                         transaction.type === 'expense' ? 'Pengeluaran' : 'Transfer';
        
        tableHTML += `
            <tr>
                <td>${formatDate(transaction.transaction_date)}</td>
                <td>
                    <span class="transaction-type ${typeClass}">
                        <i class="fas fa-${typeIcon}"></i>
                        ${typeLabel}
                    </span>
                </td>
                <td>
                    <strong>${transaction.title}</strong>
                    ${transaction.description ? `<br><small class="text-muted">${transaction.description}</small>` : ''}
                </td>
                <td>${transaction.category ? transaction.category.name : '-'}</td>
                <td>
                    ${transaction.account ? transaction.account.name : '-'}
                    ${transaction.to_account ? ` → ${transaction.to_account.name}` : ''}
                </td>
                <td class="${amountClass}">
                    ${transaction.type === 'expense' ? '-' : ''}Rp ${formatNumber(transaction.amount)}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-action btn-view" onclick="viewTransaction(${transaction.id})" title="Lihat Detail">
                            <i class="fas fa-eye"></i> Lihat
                        </button>
                        <button class="btn-action btn-edit" onclick="editTransaction(${transaction.id})" title="Edit Transaksi">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn-action btn-delete" onclick="deleteTransaction(${transaction.id})" title="Hapus Transaksi">
                            <i class="fas fa-trash"></i> Hapus
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tableHTML += `
            </tbody>
        </table>
    `;
    
    document.getElementById('tableContainer').innerHTML = tableHTML;
}

// Load statistics
function loadStatistics() {
    const formData = new FormData(document.getElementById('filterForm'));
    
    fetch('/transactions/api/statistics?' + new URLSearchParams(formData))
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalIncome').textContent = `Rp ${formatNumber(data.total_income || 0)}`;
            document.getElementById('totalExpense').textContent = `Rp ${formatNumber(data.total_expense || 0)}`;
            document.getElementById('totalTransfer').textContent = `Rp ${formatNumber(data.total_transfer || 0)}`;
            document.getElementById('netIncome').textContent = `Rp ${formatNumber((data.total_income || 0) - (data.total_expense || 0))}`;
        })
        .catch(error => console.error('Error loading statistics:', error));
}

// Update pagination
function updatePagination(data) {
    const paginationInfo = document.getElementById('paginationInfo');
    const paginationControls = document.getElementById('paginationControls');
    
    if (data.last_page <= 1) {
        paginationInfo.innerHTML = `Menampilkan ${data.data.length} dari ${data.total} transaksi`;
        paginationControls.innerHTML = '';
        return;
    }
    
    paginationInfo.innerHTML = `
        Halaman ${data.current_page} dari ${data.last_page} 
        (${data.from}-${data.to} dari ${data.total} transaksi)
    `;
    
    let paginationHTML = '<div class="pagination">';
    
    // Previous button
    if (data.current_page > 1) {
        paginationHTML += `<button class="btn btn-sm btn-outline-primary" onclick="loadTransactions(${data.current_page - 1})">‹ Sebelumnya</button>`;
    }
    
    // Page numbers
    const startPage = Math.max(1, data.current_page - 2);
    const endPage = Math.min(data.last_page, data.current_page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === data.current_page ? 'btn-primary' : 'btn-outline-primary';
        paginationHTML += `<button class="btn btn-sm ${activeClass}" onclick="loadTransactions(${i})">${i}</button>`;
    }
    
    // Next button
    if (data.current_page < data.last_page) {
        paginationHTML += `<button class="btn btn-sm btn-outline-primary" onclick="loadTransactions(${data.current_page + 1})">Selanjutnya ›</button>`;
    }
    
    paginationHTML += '</div>';
    paginationControls.innerHTML = paginationHTML;
}

// Apply filters
function applyFilters() {
    loadTransactions(1);
    loadStatistics();
}

// Reset filters
function resetFilters() {
    document.getElementById('filterForm').reset();
    applyFilters();
}

// Export data
function exportData(format) {
    const formData = new FormData(document.getElementById('filterForm'));
    formData.append('format', format);
    
    const params = new URLSearchParams(formData);
    window.open(`/transactions/export?${params}`, '_blank');
}

// Transaction actions
function viewTransaction(id) {
    window.location.href = `/transactions/${id}`;
}

function editTransaction(id) {
    window.location.href = `/transactions/${id}/edit`;
}

function deleteTransaction(id) {
    Swal.fire({
        title: 'Hapus Transaksi?',
        text: 'Apakah Anda yakin ingin menghapus transaksi ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/transactions/${id}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('Delete response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return response.json();
            })
            .then(data => {
                console.log('Delete response data:', data);
                
                if (data.success) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: data.message || 'Transaksi telah dihapus.',
                        icon: 'success',
                        timer: 2000,
                        timerProgressBar: true
                    });
                    loadTransactions();
                    loadStatistics();
                } else {
                    Swal.fire({
                        title: 'Gagal!',
                        text: data.message || 'Gagal menghapus transaksi.',
                        icon: 'error'
                    });
                }
            })
            .catch(error => {
                console.error('Delete error:', error);
                Swal.fire({
                    title: 'Gagal!',
                    text: 'Terjadi kesalahan saat menghapus transaksi.',
                    icon: 'error'
                });
            });
        }
    });
}

// Utility functions
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
    });
}
</script>
@endpush
@endsection
