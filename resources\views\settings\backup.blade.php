@extends('layouts.dashboard')
<style>
      /* Theme Variations for Sidebar */
        /* Solarized Dark Theme */
        body[data-theme="solarized-dark"] {
            background: linear-gradient(135deg, #002b36 0%, #073642 100%);
        }
        
        body[data-theme="solarized-dark"] .sidebar {
            background: rgba(253, 246, 227, 0.95);
        }
        
        body[data-theme="solarized-dark"] .sidebar-logo {
            color: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .menu-link {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .menu-link:hover {
            background: rgba(38, 139, 210, 0.1);
            color: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .menu-link.active {
            background: #268bd2;
            color: white;
        }
        
        body[data-theme="solarized-dark"] .sidebar-collapse-btn {
            background: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .sidebar-collapse-btn:hover {
            background: #2aa198;
        }
        
        body[data-theme="solarized-dark"] .dashboard-card {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .quick-actions {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .card-title {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .card-subtitle {
            color: #657b83;
        }
        
        body[data-theme="solarized-dark"] .card-content {
            color: #839496;
        }
        
        body[data-theme="solarized-dark"] .action-btn {
            background: linear-gradient(45deg, #268bd2, #2aa198);
        }
        
        /* Synth Wave Theme */
        body[data-theme="synth-wave"] {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a0033 50%, #000000 100%);
        }
        
        body[data-theme="synth-wave"] .sidebar {
            background: rgba(26, 26, 26, 0.95);
            border-right: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .sidebar-logo {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .sidebar-subtitle {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .menu-link {
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .menu-link:hover {
            background: rgba(255, 0, 255, 0.1);
            color: #ff00ff;
        }
        
        body[data-theme="synth-wave"] .menu-link.active {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            color: white;
        }
        
        body[data-theme="synth-wave"] .sidebar-collapse-btn {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
        }
        
        body[data-theme="synth-wave"] .dashboard-card {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .quick-actions {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .card-title {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .card-subtitle {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .card-content {
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .action-btn {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.4);
        }
          body[data-theme="synth-wave"] .quick-actions h3 {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        /* Theme-specific styles for user dropdown */
        body[data-theme="solarized-dark"] .user-dropdown {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .user-dropdown-item {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .user-dropdown-item:hover {
            background: rgba(38, 139, 210, 0.1);
        }
        
        body[data-theme="synth-wave"] .user-dropdown {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .user-dropdown-item {
            color: #ffffff;
        }
          body[data-theme="synth-wave"] .user-dropdown-item:hover {
            background: rgba(255, 0, 255, 0.1);
        }
          /* Tooltip styles for different themes */
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link::after {
            background: linear-gradient(135deg, #268bd2, #2aa198);
            color: #fdf6e3;
            box-shadow: 0 4px 12px rgba(38, 139, 210, 0.3);
        }
        
        body[data-theme="synth-wave"] .sidebar.collapsed .menu-link::after {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            color: #ffffff;
            border: 1px solid rgba(255, 0, 255, 0.5);
            box-shadow: 0 4px 12px rgba(255, 0, 255, 0.4);
        }
          /* Theme-specific styling for collapsed sidebar */
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link {
            background: rgba(253, 246, 227, 0.9);
            border-color: rgba(38, 139, 210, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }          body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link:hover {
            background: rgba(38, 139, 210, 0.1);
            border-color: rgba(38, 139, 210, 0.25);
            box-shadow: 0 3px 10px rgba(38, 139, 210, 0.12);
        }
        
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, #268bd2, #2aa198);
        }
          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(255, 0, 255, 0.2);
            box-shadow: 0 2px 8px rgba(255, 0, 255, 0.08);
        }          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link:hover {
            background: rgba(255, 0, 255, 0.1);
            border-color: rgba(255, 0, 255, 0.3);
            box-shadow: 0 3px 10px rgba(255, 0, 255, 0.15);
        }
          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            box-shadow: 0 4px 15px rgba(255, 0, 255, 0.4);
        }
    </style>