// Global Currency Formatting Functions for Indonesian Rupiah

/**
 * Format number to Indonesian Rupiah
 * @param {number} amount - The amount to format
 * @param {boolean} showSymbol - Whether to show Rp symbol (default: true)
 * @returns {string} Formatted currency string
 */
function formatRupiah(amount, showSymbol = true) {
    const formatted = new Intl.NumberFormat('id-ID', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
    
    return showSymbol ? 'Rp ' + formatted : formatted;
}

/**
 * Parse Indonesian Rupiah string to number
 * @param {string} rupiahString - The Rupiah string to parse
 * @returns {number} Parsed number
 */
function parseRupiah(rupiahString) {
    // Remove "Rp", spaces, and dots, then convert to number
    const cleaned = rupiahString.replace(/Rp\s?|\.|\s/g, '');
    return parseInt(cleaned) || 0;
}

/**
 * Format input field for Rupiah display
 * @param {HTMLInputElement} input - The input element
 */
function formatRupiahInput(input) {
    let value = input.value.replace(/[^0-9]/g, '');
    if (value) {
        input.value = formatRupiah(parseInt(value), false);
    }
}

/**
 * Auto format Rupiah input on keyup
 * @param {Event} event - The keyup event
 */
function autoFormatRupiah(event) {
    formatRupiahInput(event.target);
}

// Apply auto formatting to all rupiah inputs
document.addEventListener('DOMContentLoaded', function() {
    const rupiahInputs = document.querySelectorAll('.rupiah-input, input[name*="amount"], input[name*="balance"], input[name*="limit"]');
    rupiahInputs.forEach(input => {
        input.addEventListener('keyup', autoFormatRupiah);
        input.addEventListener('blur', autoFormatRupiah);
        
        // Format initial value if exists
        if (input.value) {
            formatRupiahInput(input);
        }
    });
});
