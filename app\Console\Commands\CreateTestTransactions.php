<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Transaction;
use App\Models\Category;
use App\Models\Account;

class CreateTestTransactions extends Command
{
    protected $signature = 'test:create-transactions';
    protected $description = 'Create test transactions for July 2025';

    public function handle()
    {
        $user_id = 2;

        // Get expense category
        $expenseCategory = Category::where('user_id', $user_id)->where('type', 'expense')->first();
        $incomeCategory = Category::where('user_id', $user_id)->where('type', 'income')->first();

        // Get account
        $account = Account::where('user_id', $user_id)->first();

        if (!$account) {
            $this->error('No account found for user');
            return;
        }

        if ($expenseCategory) {
            // Create expense transaction for July 2025
            Transaction::create([
                'user_id' => $user_id,
                'account_id' => $account->id,
                'category_id' => $expenseCategory->id,
                'type' => 'expense',
                'amount' => 500000,
                'title' => '<PERSON><PERSON><PERSON>',
                'transaction_date' => '2025-07-15',
                'status' => 'completed',
                'processed_at' => now()
            ]);
            
            $this->info('Expense transaction created successfully');
        }
        
        if ($incomeCategory) {
            // Create income transaction for July 2025
            Transaction::create([
                'user_id' => $user_id,
                'account_id' => $account->id,
                'category_id' => $incomeCategory->id,
                'type' => 'income',
                'amount' => 7000000,
                'title' => 'Gaji Juli',
                'transaction_date' => '2025-07-01',
                'status' => 'completed',
                'processed_at' => now()
            ]);
            $this->info('Income transaction created successfully');
        }
        
        // Show total transactions
        $total = Transaction::where('user_id', $user_id)->count();
        $this->info("Total transactions for user $user_id: $total");
    }
}
