@extends('layouts.dashboard')

@section('title', isset($account) ? 'Edit <PERSON>ku<PERSON> & Dompet' : 'Tambah Akun & Dompet')

@section('page-title', isset($account) ? '✏️ Edit Akun & Dompet' : '➕ <PERSON>bah Akun & Dompet')

@push('styles')
<style>
    /* Dashboard container styling sesuai referensi */
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    /* Theme support untuk breadcrumb */
    body[data-theme="solarized-dark"] .breadcrumb-item a {
        color: #b58900 !important;
    }
    
    body[data-theme="solarized-dark"] .breadcrumb-item.active {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item a {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item.active {
        color: #ffffff !important;
    }
    
    /* Theme support untuk h2 */
    body[data-theme="solarized-dark"] h2 {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] h2 {
        color: #ffffff !important;
    }
    
    /* Theme support untuk small text */
    body[data-theme="solarized-dark"] .text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="synth-wave"] .text-muted {
        color: #cccccc !important;
    }

    /* Form card styling */
    .form-card {
        background: #f8fffe;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    body[data-theme="solarized-dark"] .form-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
    }
    
    body[data-theme="synth-wave"] .form-card {
        background: rgba(0, 128, 255, 0.1);
        border: 1px solid #0080ff;
        color: #ffffff;
    }
    
    /* Icon selector */
    .icon-selector {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 10px;
        margin-top: 10px;
    }
    
    .icon-option {
        width: 60px;
        height: 60px;
        border: 2px solid #ddd;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #fff;
    }
    
    .icon-option:hover {
        border-color: #007bff;
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }
    
    .icon-option.selected {
        border-color: #007bff;
        background: #007bff;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
    }
    
    .icon-option i {
        font-size: 24px;
    }
    
    .icon-option.selected i {
        color: white !important;
    }
    
    /* Color selector */
    .color-selector {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
        gap: 10px;
        margin-top: 10px;
    }
    
    .color-option {
        width: 50px;
        height: 50px;
        border: 3px solid transparent;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .color-option:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    
    .color-option.selected {
        border-color: #333;
        transform: scale(1.2);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    }
    
    .color-option.selected::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-weight: bold;
        text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
    }
    
    /* Form styling */
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 8px;
        color: #495057;
    }
    
    body[data-theme="solarized-dark"] .form-label {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] .form-label {
        color: #ffffff !important;
    }
    
    /* Button styling */
    .btn-save {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        border-radius: 15px;
        padding: 12px 30px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 30px rgba(0, 123, 255, 0.4);
        color: white;
    }
    
    .btn-cancel {
        background: transparent;
        border: 2px solid #6c757d;
        border-radius: 15px;
        padding: 10px 30px;
        color: #6c757d;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn-cancel:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
    }
    
    /* Preview card */
    .preview-card {
        background: #f8fffe;
        border-radius: 20px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 2px dashed #007bff;
        transition: all 0.3s ease;
    }
    
    .preview-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }
    
    .preview-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        margin: 0 auto 15px;
        color: white;
        background: #007bff;
    }
    
    /* Account type specific styling */
    .type-description {
        background: rgba(0, 123, 255, 0.1);
        border-left: 4px solid #007bff;
        padding: 15px;
        border-radius: 0 10px 10px 0;
        margin-top: 10px;
    }
    
    body[data-theme="solarized-dark"] .type-description {
        background: rgba(38, 139, 210, 0.1);
        border-left-color: #268bd2;
    }
    
    body[data-theme="synth-wave"] .type-description {
        background: rgba(0, 128, 255, 0.2);
        border-left-color: #0080ff;
        color: #ffffff;
    }
    
    /* Theme support for form controls */
    body[data-theme="synth-wave"] .form-control,
    body[data-theme="synth-wave"] .form-select {
        background: rgba(0, 20, 40, 0.8) !important;
        border-color: #0080ff !important;
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .form-control:focus,
    body[data-theme="synth-wave"] .form-select:focus {
        background: rgba(0, 20, 40, 0.9) !important;
        border-color: #00ffff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .form-control::placeholder {
        color: #cccccc !important;
    }
    
    /* Input group styling */
    .input-group-text {
        border-radius: 10px 0 0 10px;
        border: 2px solid #e9ecef;
        border-right: none;
        background: #f8f9fa;
        font-weight: 600;
    }
    
    .input-group .form-control {
        border-radius: 0 10px 10px 0;
        border-left: none;
    }
    
    body[data-theme="synth-wave"] .input-group-text {
        background: rgba(0, 128, 255, 0.2) !important;
        border-color: #0080ff !important;
        color: #ffffff !important;
    }
    
    /* Currency input styling */
    .currency-input {
        text-align: right;
        font-family: 'Courier New', monospace;
        font-weight: 600;
    }
    
    body[data-theme="synth-wave"] .currency-input {
        background: rgba(0, 20, 40, 0.8) !important;
        border-color: #0080ff !important;
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .currency-input:focus {
        background: rgba(0, 20, 40, 0.9) !important;
        border-color: #00ffff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
        color: #ffffff !important;
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('dashboard') }}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="#" class="text-decoration-none">
                    <i class="fas fa-wallet me-1"></i>Akun & Dompet
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-{{ isset($account) ? 'edit' : 'plus' }} me-1"></i>{{ isset($account) ? 'Edit' : 'Tambah' }} Akun
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-{{ isset($account) ? 'edit' : 'plus' }} text-primary me-2"></i>
            {{ isset($account) ? 'Edit Akun & Dompet' : 'Tambah Akun & Dompet Baru' }}
        </h2>
        <a href="{{ route('accounts.list.index') }}" class="btn btn-cancel">
            <i class="fas fa-arrow-left me-2"></i>Kembali ke Daftar
        </a>
    </div>

    <div class="row">
        <!-- Form Column -->
        <div class="col-lg-8">
            <div class="form-card">
                <div class="card-body p-4">
                    <form action="{{ isset($account) ? route('accounts.update', $account->id) : route('accounts.store') }}" method="POST" id="accountForm">
                        @csrf
                        @if(isset($account))
                            @method('PUT')
                        @endif
                        
                        <!-- Informasi Dasar -->
                        <div class="mb-4">
                            <h5 class="fw-bold mb-3">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                Informasi Dasar
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Nama Akun/Dompet <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $account->name ?? '') }}" 
                                           placeholder="Contoh: BCA - Tabungan Utama" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="type" class="form-label">Jenis Akun <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" 
                                            id="type" name="type" required onchange="updateTypeDescription()">
                                        <option value="">Pilih jenis akun...</option>
                                        <option value="bank" {{ old('type', $account->type ?? '') === 'bank' ? 'selected' : '' }}>Bank</option>
                                        <option value="cash" {{ old('type', $account->type ?? '') === 'cash' ? 'selected' : '' }}>Tunai</option>
                                        <option value="e_wallet" {{ old('type', $account->type ?? '') === 'e_wallet' ? 'selected' : '' }}>E-Wallet</option>
                                        <option value="investment" {{ old('type', $account->type ?? '') === 'investment' ? 'selected' : '' }}>Investasi</option>
                                        <option value="credit_card" {{ old('type', $account->type ?? '') === 'credit_card' ? 'selected' : '' }}>Kartu Kredit</option>
                                        <option value="savings" {{ old('type', $account->type ?? '') === 'savings' ? 'selected' : '' }}>Tabungan</option>
                                        <option value="other" {{ old('type', $account->type ?? '') === 'other' ? 'selected' : '' }}>Lainnya</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    
                                    <!-- Dynamic type description -->
                                    <div id="typeDescription" class="type-description" style="display: none;">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            <span id="typeDescriptionText"></span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Deskripsi</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="3"
                                          placeholder="Deskripsi singkat tentang akun ini...">{{ old('description', $account->description ?? '') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Detail Akun -->
                        <div class="mb-4">
                            <h5 class="fw-bold mb-3">
                                <i class="fas fa-building text-primary me-2"></i>
                                Detail Akun
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="bank_name" class="form-label">Nama Bank/Lembaga</label>
                                    <input type="text" class="form-control @error('bank_name') is-invalid @enderror" 
                                           id="bank_name" name="bank_name" value="{{ old('bank_name', $account->bank_name ?? '') }}" 
                                           placeholder="Contoh: Bank Central Asia">
                                    @error('bank_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="account_number" class="form-label">Nomor Akun/Kartu</label>
                                    <input type="text" class="form-control @error('account_number') is-invalid @enderror" 
                                           id="account_number" name="account_number" value="{{ old('account_number', $account->account_number ?? '') }}" 
                                           placeholder="Contoh: **********">
                                    @error('account_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Saldo dan Pengaturan -->
                        <div class="mb-4">
                            <h5 class="fw-bold mb-3">
                                <i class="fas fa-money-bill-wave text-primary me-2"></i>
                                Saldo dan Pengaturan
                            </h5>
                            
                            <div class="row">
                                <!-- Hidden input untuk saldo awal, selalu 0 untuk akun baru -->
                                @if(!isset($account))
                                <input type="hidden" name="initial_balance" value="0">
                                @endif
                                
                                @if(isset($account))
                                <!-- Untuk edit mode, tampilkan saldo saat ini -->
                                <div class="col-md-6 mb-3">
                                    <label for="current_balance" class="form-label">Saldo Saat Ini <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">Rp</span>
                                        <input type="text" class="form-control currency-input @error('current_balance') is-invalid @enderror" 
                                               id="current_balance" name="current_balance" value="{{ old('current_balance', number_format($account->current_balance, 0, ',', '.')) }}" 
                                               placeholder="0" required>
                                    </div>
                                    @error('current_balance')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Saldo yang ada di akun saat ini</small>
                                </div>
                                @else
                                <!-- Untuk akun baru, tampilkan informasi saldo otomatis -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Saldo Awal</label>
                                    <div class="input-group">
                                        <span class="input-group-text">Rp</span>
                                        <input type="text" class="form-control" value="0" disabled>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Akun baru dimulai dengan saldo Rp 0. Saldo akan bertambah melalui transaksi pemasukan.
                                    </small>
                                </div>
                                @endif
                                
                                <div class="col-md-6 mb-3" id="creditLimitField" style="display: none;">
                                    <label for="credit_limit" class="form-label">Limit Kredit</label>
                                    <div class="input-group">
                                        <span class="input-group-text">Rp</span>
                                        <input type="text" class="form-control currency-input @error('credit_limit') is-invalid @enderror" 
                                               id="credit_limit" name="credit_limit" value="{{ old('credit_limit', isset($account->credit_limit) ? number_format($account->credit_limit, 0, ',', '.') : '') }}" 
                                               placeholder="0">
                                    </div>
                                    @error('credit_limit')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Limit maksimal untuk kartu kredit</small>
                                </div>
                            </div>
                        </div>

                        <!-- Tampilan dan Pengaturan -->
                        <div class="mb-4">
                            <h5 class="fw-bold mb-3">
                                <i class="fas fa-palette text-primary me-2"></i>
                                Tampilan dan Pengaturan
                            </h5>
                            
                            <!-- Icon Selection -->
                            <div class="mb-4">
                                <label class="form-label">Pilih Icon</label>
                                <input type="hidden" name="icon" id="selectedIcon" value="{{ old('icon', $account->icon ?? 'fas fa-wallet') }}">
                                
                                <div class="icon-selector">
                                    <div class="icon-option" data-icon="fas fa-wallet">
                                        <i class="fas fa-wallet"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fas fa-university">
                                        <i class="fas fa-university"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fas fa-credit-card">
                                        <i class="fas fa-credit-card"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fas fa-money-bill-wave">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fas fa-piggy-bank">
                                        <i class="fas fa-piggy-bank"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fas fa-mobile-alt">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fas fa-chart-line">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fas fa-coins">
                                        <i class="fas fa-coins"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Color Selection -->
                            <div class="mb-4">
                                <label class="form-label">Pilih Warna</label>
                                <input type="hidden" name="color" id="selectedColor" value="{{ old('color', $account->color ?? '#007bff') }}">
                                
                                <div class="color-selector">
                                    <div class="color-option" data-color="#007bff" style="background: #007bff;"></div>
                                    <div class="color-option" data-color="#28a745" style="background: #28a745;"></div>
                                    <div class="color-option" data-color="#dc3545" style="background: #dc3545;"></div>
                                    <div class="color-option" data-color="#ffc107" style="background: #ffc107;"></div>
                                    <div class="color-option" data-color="#17a2b8" style="background: #17a2b8;"></div>
                                    <div class="color-option" data-color="#6f42c1" style="background: #6f42c1;"></div>
                                    <div class="color-option" data-color="#fd7e14" style="background: #fd7e14;"></div>
                                    <div class="color-option" data-color="#6c757d" style="background: #6c757d;"></div>
                                    <div class="color-option" data-color="#e83e8c" style="background: #e83e8c;"></div>
                                    <div class="color-option" data-color="#20c997" style="background: #20c997;"></div>
                                </div>
                            </div>
                            
                            <!-- Settings -->
                            <div class="row">
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex gap-3 justify-content-end">
                            <a href="{{ route('accounts.list.index') }}" class="btn btn-cancel">
                                <i class="fas fa-times me-2"></i>Batal
                            </a>
                            <button type="submit" class="btn btn-save">
                                <i class="fas fa-save me-2"></i>{{ isset($account) ? 'Update Akun' : 'Simpan Akun' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Column -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 20px;">
                <div class="preview-card">
                    <h6 class="fw-bold mb-3">
                        <i class="fas fa-eye text-primary me-2"></i>
                        Preview Akun
                    </h6>
                    
                    <div class="preview-icon" id="previewIcon" style="background: #007bff;">
                        <i class="fas fa-wallet"></i>
                    </div>
                    
                    <h5 class="fw-bold mb-2" id="previewName">Nama Akun</h5>
                    <span class="badge bg-primary mb-3" id="previewType">Pilih Jenis</span>
                    
                    <div class="text-muted mb-3" id="previewDescription">
                        Deskripsi akan muncul di sini...
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">{{ isset($account) ? 'Saldo Saat Ini:' : 'Saldo Awal:' }}</small>
                        <strong id="previewBalance">{{ isset($account) ? 'Rp '.number_format($account->current_balance, 0, ',', '.') : 'Rp 0' }}</strong>
                    </div>
                </div>

                <!-- Quick Tips -->
                <div class="mt-4 p-3 rounded" style="background: rgba(0, 123, 255, 0.1); border-left: 4px solid #007bff;">
                    <h6 class="fw-bold text-primary">
                        <i class="fas fa-lightbulb me-2"></i>Tips
                    </h6>
                    <ul class="small mb-0 ps-3">
                        <li class="mb-2">Berikan nama yang mudah dikenali untuk setiap akun</li>
                        <li class="mb-2">Pilih icon dan warna yang berbeda untuk membedakan akun</li>
                        <li class="mb-2">Isi saldo awal sesuai dengan saldo aktual akun Anda</li>
                        <li>Akun yang dinonaktifkan tidak akan muncul di form transaksi</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Currency formatting functions
function formatRupiah(amount) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
}

function formatCurrencyInput(input) {
    // Remove all non-digit characters
    let value = input.value.replace(/[^0-9]/g, '');
    
    // Format as Indonesian currency (without Rp prefix)
    if (value) {
        const formatted = new Intl.NumberFormat('id-ID').format(parseInt(value));
        input.value = formatted;
    } else {
        input.value = '';
    }
}

function parseCurrencyInput(value) {
    // Remove all non-digit characters and convert to number
    return parseInt(value.replace(/[^0-9]/g, '')) || 0;
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize currency inputs
    initializeCurrencyInputs();
    
    // Initialize form interactions
    initializeIconSelector();
    initializeColorSelector();
    initializeFormPreview();
    initializeTypeSpecificFields();
    
    // Set initial states
    updatePreview();
    updateTypeDescription();
});

function initializeCurrencyInputs() {
    const currencyInputs = document.querySelectorAll('.currency-input');
    
    currencyInputs.forEach(input => {
        // Format on input
        input.addEventListener('input', function() {
            formatCurrencyInput(this);
            updatePreview();
        });
        
        // Format on blur
        input.addEventListener('blur', function() {
            formatCurrencyInput(this);
        });
        
        // Initial format
        if (input.value) {
            formatCurrencyInput(input);
        }
    });
}

function initializeIconSelector() {
    const iconOptions = document.querySelectorAll('.icon-option');
    const selectedIconInput = document.getElementById('selectedIcon');
    
    // Set initial selection
    const initialIcon = selectedIconInput.value;
    iconOptions.forEach(option => {
        if (option.dataset.icon === initialIcon) {
            option.classList.add('selected');
        }
        
        option.addEventListener('click', function() {
            iconOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            selectedIconInput.value = this.dataset.icon;
            updatePreview();
        });
    });
}

function initializeColorSelector() {
    const colorOptions = document.querySelectorAll('.color-option');
    const selectedColorInput = document.getElementById('selectedColor');
    
    // Set initial selection
    const initialColor = selectedColorInput.value;
    colorOptions.forEach(option => {
        if (option.dataset.color === initialColor) {
            option.classList.add('selected');
        }
        
        option.addEventListener('click', function() {
            colorOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            selectedColorInput.value = this.dataset.color;
            updatePreview();
        });
    });
}

function initializeFormPreview() {
    // Listen to form changes
    const form = document.getElementById('accountForm');
    const inputs = form.querySelectorAll('input[type="text"], input[type="number"], textarea, select');
    
    inputs.forEach(input => {
        input.addEventListener('input', updatePreview);
        input.addEventListener('change', updatePreview);
    });
}

function initializeTypeSpecificFields() {
    const typeSelect = document.getElementById('type');
    typeSelect.addEventListener('change', function() {
        const creditLimitField = document.getElementById('creditLimitField');
        
        if (this.value === 'credit_card') {
            creditLimitField.style.display = 'block';
        } else {
            creditLimitField.style.display = 'none';
        }
        
        updateTypeDescription();
        updatePreview();
    });
}

function updatePreview() {
    const name = document.getElementById('name').value || 'Nama Akun';
    const type = document.getElementById('type').value;
    const description = document.getElementById('description').value || 'Deskripsi akan muncul di sini...';
    
    // Get balance based on mode (create/edit)
    let balance = 0;
    const currentBalanceField = document.getElementById('current_balance');
    if (currentBalanceField) {
        // Edit mode - get from current_balance field
        const balanceStr = currentBalanceField.value.replace(/[^0-9]/g, '');
        balance = parseFloat(balanceStr) || 0;
    }
    // For create mode, balance is always 0
    
    const icon = document.getElementById('selectedIcon').value;
    const color = document.getElementById('selectedColor').value;
    
    // Update preview elements
    document.getElementById('previewName').textContent = name;
    document.getElementById('previewDescription').textContent = description;
    document.getElementById('previewBalance').textContent = formatRupiah(balance);
    
    // Update icon
    const previewIcon = document.getElementById('previewIcon');
    previewIcon.style.background = color;
    previewIcon.querySelector('i').className = icon;
    
    // Update type badge
    const typeLabels = {
        'bank': 'Bank',
        'cash': 'Tunai',
        'e_wallet': 'E-Wallet',
        'investment': 'Investasi',
        'credit_card': 'Kartu Kredit',
        'savings': 'Tabungan',
        'other': 'Lainnya'
    };
    
    document.getElementById('previewType').textContent = typeLabels[type] || 'Pilih Jenis';
}

function updateTypeDescription() {
    const type = document.getElementById('type').value;
    const descriptionDiv = document.getElementById('typeDescription');
    const descriptionText = document.getElementById('typeDescriptionText');
    
    const descriptions = {
        'bank': 'Akun bank untuk transaksi sehari-hari, transfer, dan penyimpanan dana.',
        'cash': 'Uang tunai yang Anda miliki di dompet, kas kecil, atau tempat penyimpanan fisik.',
        'e_wallet': 'Dompet digital seperti GoPay, OVO, DANA, atau aplikasi pembayaran lainnya.',
        'investment': 'Akun investasi seperti saham, reksadana, emas, atau instrumen investasi lainnya.',
        'credit_card': 'Kartu kredit dengan limit tertentu untuk pembelian dengan sistem hutang.',
        'savings': 'Tabungan khusus dengan tujuan tertentu atau deposito berjangka.',
        'other': 'Jenis akun lainnya yang tidak termasuk dalam kategori di atas.'
    };
    
    if (type && descriptions[type]) {
        descriptionText.textContent = descriptions[type];
        descriptionDiv.style.display = 'block';
    } else {
        descriptionDiv.style.display = 'none';
    }
}

// Form validation dan submit dengan AJAX
document.getElementById('accountForm').addEventListener('submit', function(e) {
    e.preventDefault(); // Prevent default form submission
    
    const name = document.getElementById('name').value.trim();
    const type = document.getElementById('type').value;
    
    // Check if we're in edit mode
    const isEditMode = document.getElementById('current_balance') !== null;
    
    // Validation
    if (!name) {
        Swal.fire({
            title: 'Nama Akun Diperlukan',
            text: 'Silakan masukkan nama untuk akun ini.',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        document.getElementById('name').focus();
        return;
    }
    
    if (!type) {
        Swal.fire({
            title: 'Jenis Akun Diperlukan',
            text: 'Silakan pilih jenis akun.',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        document.getElementById('type').focus();
        return;
    }
    
    // Additional validation for edit mode
    if (isEditMode) {
        const currentBalance = document.getElementById('current_balance').value.trim();
        if (!currentBalance) {
            Swal.fire({
                title: 'Saldo Saat Ini Diperlukan',
                text: 'Silakan masukkan saldo saat ini untuk akun.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            document.getElementById('current_balance').focus();
            return;
        }
    }
    
    // Show loading
    Swal.fire({
        title: 'Menyimpan Akun...',
        text: 'Sedang memproses data akun baru',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    // Preprocess currency fields before submit (hanya untuk edit mode)
    const currencyInputs = this.querySelectorAll('.currency-input');
    currencyInputs.forEach(input => {
        if (input.value && input.name === 'current_balance') {
            // Only process current_balance for edit mode
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = input.name + '_numeric';
            hiddenInput.value = parseCurrencyInput(input.value);
            this.appendChild(hiddenInput);
        }
    });
    
    // Prepare form data
    const formData = new FormData(this);
    const formAction = this.getAttribute('action');
    const formMethod = this.getAttribute('method');
    
    // Add AJAX header
    formData.append('_ajax', '1');
    
    // Submit form via AJAX
    fetch(formAction, {
        method: formMethod,
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        Swal.close();
        
        if (data.success) {
            // Success notification
            Swal.fire({
                title: 'Berhasil!',
                text: data.message,
                icon: 'success',
                confirmButtonText: 'OK',
                allowOutsideClick: false
            }).then((result) => {
                if (result.isConfirmed) {
                    // Redirect to accounts list
                    window.location.href = data.redirect || "{{ route('accounts.list.index') }}";
                }
            });
        } else {
            // Error notification
            let errorMessage = data.message || 'Terjadi kesalahan saat menyimpan akun!';
            
            // Handle validation errors
            if (data.errors) {
                const errorList = Object.values(data.errors).flat();
                errorMessage = errorList.join('\\n');
            }
            
            Swal.fire({
                title: 'Gagal!',
                text: errorMessage,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    })
    .catch(error => {
        Swal.close();
        console.error('Error:', error);
        
        Swal.fire({
            title: 'Terjadi Kesalahan!',
            text: 'Gagal menghubungi server. Silakan coba lagi.',
            icon: 'error',
            confirmButtonText: 'OK'
        });
    });
});
</script>
@endpush
