@extends('layouts.dashboard')

@section('title', '2FA Berhasil Diaktifkan')

@section('page-title', '✅ Two-Factor Authentication Aktif')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            
            <!-- Success Alert from Session -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Selamat!</strong> {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @else
                <!-- Default Success Alert -->
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Selamat!</strong> Two-Factor Authentication berhasil diaktifkan untuk akun <PERSON>.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Backup Codes Card -->
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-key me-2"></i>
                        Backup Codes - PENTING!
                    </h5>
                </div>
                <div class="card-body">
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Simpan backup codes ini dengan aman!</strong><br>
                        Gunakan backup codes jika Anda kehilangan akses ke aplikasi authenticator.
                        Setiap code hanya bisa digunakan sekali.
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="fw-bold mb-3">Backup Codes Anda:</h6>
                            <div class="row">
                                @foreach($backupCodes as $index => $code)
                                <div class="col-md-6 mb-2">
                                    <div class="p-2 bg-light border rounded font-monospace">
                                        {{ $loop->iteration }}. {{ $code }}
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid gap-2">
                                <button onclick="downloadBackupCodes()" class="btn btn-primary">
                                    <i class="fas fa-download me-2"></i>
                                    Download as TXT
                                </button>
                                <button onclick="printBackupCodes()" class="btn btn-outline-primary">
                                    <i class="fas fa-print me-2"></i>
                                    Print Codes
                                </button>
                                <button onclick="copyAllCodes()" class="btn btn-outline-secondary">
                                    <i class="fas fa-copy me-2"></i>
                                    Copy All
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Next Steps Card -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-success text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-list-check me-2"></i>
                        Langkah Selanjutnya
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">✅ Yang Sudah Aktif:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    2FA TOTP di aplikasi authenticator
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    10 backup codes untuk recovery
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Login verification setiap sesi baru
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">📝 Yang Perlu Diingat:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-mobile-alt text-info me-2"></i>
                                    Pastikan aplikasi authenticator selalu tersedia
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-save text-info me-2"></i>
                                    Simpan backup codes di tempat aman
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-sync text-info me-2"></i>
                                    Regenerate backup codes jika sudah habis
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center mt-4">
                <a href="{{ route('settings.security') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>
                    Kembali ke Pengaturan Keamanan
                </a>
                <a href="{{ route('dashboard') }}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-home me-2"></i>
                    Ke Dashboard
                </a>
            </div>

        </div>
    </div>
</div>

<!-- Hidden content for download/print -->
<div id="backup-codes-content" style="display: none;">
    <h3>MyMoney - Two-Factor Authentication Backup Codes</h3>
    <p>User: {{ Auth::user()->email }}</p>
    <p>Generated: {{ now()->format('d/m/Y H:i:s') }}</p>
    <p>IMPORTANT: Keep these codes safe and secret. Each code can only be used once.</p>
    <hr>
    @foreach($backupCodes as $index => $code)
    {{ $loop->iteration }}. {{ $code }}
    @endforeach
    <hr>
    <p>Instructions:</p>
    <p>- Use these codes if you lose access to your authenticator app</p>
    <p>- Each code works only once</p>
    <p>- Generate new codes when you have used most of them</p>
    <p>- Store these codes in a secure location</p>
</div>

@push('scripts')
<script>
function downloadBackupCodes() {
    const codes = @json($backupCodes);
    const content = `MyMoney - Two-Factor Authentication Backup Codes
User: {{ Auth::user()->email }}
Generated: {{ now()->format('d/m/Y H:i:s') }}

IMPORTANT: Keep these codes safe and secret. Each code can only be used once.

${codes.map((code, index) => `${index + 1}. ${code}`).join('\n')}

Instructions:
- Use these codes if you lose access to your authenticator app
- Each code works only once
- Generate new codes when you have used most of them
- Store these codes in a secure location`;

    const element = document.createElement('a');
    element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(content));
    element.setAttribute('download', 'mymoney-2fa-backup-codes.txt');
    element.style.display = 'none';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
}

function printBackupCodes() {
    const content = document.getElementById('backup-codes-content').innerHTML;
    const printWindow = window.open('', '', 'height=600,width=800');
    printWindow.document.write('<html><head><title>Backup Codes</title>');
    printWindow.document.write('<style>body{font-family:Arial,sans-serif;padding:20px;}</style>');
    printWindow.document.write('</head><body>');
    printWindow.document.write(content.replace(/<hr>/g, '<hr style="margin:20px 0;">'));
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    printWindow.print();
}

function copyAllCodes() {
    const codes = @json($backupCodes);
    const text = codes.map((code, index) => `${index + 1}. ${code}`).join('\n');
    
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        Swal.fire({
            icon: 'success',
            title: 'Copied!',
            text: 'Backup codes berhasil disalin ke clipboard',
            timer: 2000,
            showConfirmButton: false
        });
    });
}
</script>
@endpush
@endsection
