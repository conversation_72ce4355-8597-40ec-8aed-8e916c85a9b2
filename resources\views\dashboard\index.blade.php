<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://js.pusher.com https://cdnjs.cloudflare.com https:;
        style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.bunny.net https://cdnjs.cloudflare.com https:;
        font-src 'self' https://fonts.googleapis.com https://fonts.bunny.net https://cdnjs.cloudflare.com https:;
        img-src 'self' data: blob: https:;
        connect-src 'self' ws: wss: https://sockjs-ap1.pusher.com https://ws-ap1.pusher.com wss://ws-ap1.pusher.com https:;
    ">
    <title>{{ config('app.name', 'My Money') }} - Dashboard</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Bootstrap & Custom CSS -->
    @vite(['resources/sass/app.scss'])
      <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
      <!-- Page Transitions CSS -->
    <link rel="stylesheet" href="{{ asset('css/page-transitions.css') }}">
      <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Pusher -->
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    
</head>
      <style>        /* Ensure FontAwesome icons are loaded properly */
        .fas, .far, .fab {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro" !important;
            font-weight: 900;
        }
        
        .far {
            font-weight: 400;
        }
        
        /* Force icon visibility with fallback */
        .menu-icon.fas::before,
        .submenu-icon.fas::before,
        .menu-arrow.fas::before {
            display: inline-block !important;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
        }
        
        /* Fallback untuk icon yang tidak ter-load */
        .menu-icon {
            position: relative;
        }
        
        .menu-icon.fa-home::before { content: "🏠"; }
        .menu-icon.fa-exchange-alt::before { content: "💱"; }
        .menu-icon.fa-tags::before { content: "🏷️"; }
        .menu-icon.fa-wallet::before { content: "👛"; }
        .menu-icon.fa-clipboard-list::before { content: "📋"; }
        .menu-icon.fa-chart-bar::before { content: "📊"; }
        .menu-icon.fa-cog::before { content: "⚙️"; }
        
        :root {
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --primary-color: #667eea;
            --secondary-color: #764ba2;
        }
        
        /* Animasi untuk rotasi aktivitas */
        @keyframes slideInFade {
            0% {
                opacity: 0;
                transform: translateY(10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Figtree', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .app-container {
            display: flex;
            min-height: 100vh;
        }
          /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
            overflow-x: hidden;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
          .sidebar-subtitle {
            font-size: 0.8rem;
            color: #666;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .sidebar-menu {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        
        /* Styling khusus untuk sidebar collapsed */
        .sidebar.collapsed .sidebar-menu {
            padding: 10px 0;
        }
        
        .sidebar.collapsed .menu-item {
            margin: 6px 0;
        }
        
        .sidebar.collapsed .menu-item:first-child {
            margin-top: 20px;
        }
        
        .menu-item {
            position: relative;
        }
          .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border-radius: 8px;
            margin: 2px 16px;
        }
          .menu-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            text-decoration: none;
            transform: translateX(2px);
        }
        
        .menu-link.active {
            background: var(--primary-color);
            color: white;
        }        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
            font-size: 1rem;
            color: inherit;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: margin-right 0.3s ease, font-size 0.3s ease;
        }
          .menu-text {
            flex: 1;
            font-weight: 500;
            transition: opacity 0.3s ease, visibility 0.3s ease, width 0.3s ease;
        }
        
        .menu-arrow {
            font-size: 0.8rem;
            transition: transform 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .menu-arrow.rotated {
            transform: rotate(90deg);
        }
        
        .submenu {
            list-style: none;
            margin: 0;
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.05);
        }
        
        .submenu.open {
            max-height: 300px;
        }
        
        .submenu-item {
            padding: 8px 20px 8px 52px;
        }
        
        .submenu-link {
            display: flex;
            align-items: center;
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .submenu-link:hover {
            color: var(--primary-color);
            text-decoration: none;
        }
          .submenu-icon {
            width: 16px;
            margin-right: 8px;
            text-align: center;
            font-size: 0.9rem;
            color: inherit;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
          /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            flex: 1;
            min-height: 100vh;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .topbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            height: var(--topbar-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .topbar-left h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }
        
        .topbar-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            overflow: hidden;
            position: relative;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }
        
        .user-avatar-text {
            font-size: 1rem;
            color: white;
            font-weight: 600;
        }
        
        .user-details h4 {
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .user-details p {
            margin: 0;
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .dashboard-container {
            padding: 30px;
            min-height: calc(100vh - var(--topbar-height));
        }
          
        .dashboard-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            color: white;
        }
        
        .welcome-section h2 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .welcome-section p {
            opacity: 0.9;
            margin: 0;
        }
        
        .btn-logout {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .dashboard-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .card-subtitle {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .card-content {
            color: #555;
            line-height: 1.6;
        }
        
        .finance-icon {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .security-icon {
            background: linear-gradient(45deg, #11998e, #38ef7d);
            color: white;
        }
        
        .stats-icon {
            background: linear-gradient(45deg, #fa709a, #fee140);
            color: white;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0 0 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .quick-actions h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
            display: block;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }        /* Theme Switcher in Topbar */
        .theme-switcher {
            position: relative;
        }
        
        .theme-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
            z-index: 99999; /* Fix z-index issue */
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
        
        /* User Info Dropdown */
        .user-info-dropdown {
            position: relative;
        }
        
        .user-info-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .user-info-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
            z-index: 9999;
        }
        
        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .user-dropdown-item:hover {
            background: #f0f0f0;
        }
        
        .user-dropdown-icon {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }          /* Sidebar Collapse Button */
        .sidebar-collapse-btn {
            position: fixed;
            top: 20px;
            left: calc(var(--sidebar-width) - 15px);
            width: 30px;
            height: 30px;
            background: var(--primary-color);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
        }
        
        .sidebar-collapse-btn:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        /* Tombol saat sidebar collapsed - posisi berubah ke kanan */
        .sidebar.collapsed + .sidebar-collapse-btn {
            left: calc(80px - 15px);
        }.sidebar.collapsed {
            width: 80px;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
          .sidebar.collapsed .sidebar-header {
            padding: 12px 3px;
            text-align: center;
        }
        
        .sidebar.collapsed .sidebar-logo {
            font-size: 1.4rem;
            margin-bottom: 0;
        }
        
        .sidebar.collapsed .sidebar-subtitle {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        
        .sidebar.collapsed .menu-text {
            opacity: 0;
            visibility: hidden;
            width: 0;
            transition: opacity 0.2s ease, visibility 0.2s ease, width 0.2s ease;
        }
        
        .sidebar.collapsed .menu-arrow {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        
        .sidebar.collapsed .submenu {
            display: none;
        }        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 8px 4px;
            margin: 4px 6px;
            border-radius: 8px;
            position: relative;
            min-height: 36px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }          .sidebar.collapsed .menu-link:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.12);
            border-color: rgba(102, 126, 234, 0.15);
        }
          .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            border-color: transparent;        }.sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 1.1rem;
            color: inherit;
            width: auto;
            min-width: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
          /* Tooltip for collapsed sidebar */
        .sidebar.collapsed .menu-link::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 15px;
            z-index: 10000;
            pointer-events: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .sidebar.collapsed .menu-link:hover::after {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) translateX(5px);
        }        .main-content.sidebar-collapsed {
            margin-left: 70px;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
          /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-collapse-btn {
                left: 15px;
                top: 15px;
            }
            
            .sidebar.collapsed + .sidebar-collapse-btn {
                left: 15px;
            }
            
            .topbar {
                padding: 0 15px;
            }
            
            .dashboard-container {
                padding: 15px;
            }
            
            .dashboard-content {
                grid-template-columns: 1fr;
            }
              .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .user-info-toggle .user-details h4 {
                display: none;
            }
            
            .user-info-toggle .user-details p {
                display: none;
            }
            
            .user-dropdown {
                min-width: 150px;
            }
        }
        
        .theme-icon {
            font-size: 1rem;
        }
        
        .dropdown-arrow {
            font-size: 0.7rem;
            transition: transform 0.3s ease;
        }
        
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
          /* Responsive */
        @media (max-width: 768px) {
            .welcome-section h2 {
                font-size: 1.3rem;
            }
        }
          /* Theme Variations for Sidebar */
        /* Solarized Dark Theme */
        body[data-theme="solarized-dark"] {
            background: linear-gradient(135deg, #002b36 0%, #073642 100%);
        }
        
        body[data-theme="solarized-dark"] .sidebar {
            background: rgba(253, 246, 227, 0.95);
        }
        
        body[data-theme="solarized-dark"] .sidebar-logo {
            color: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .menu-link {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .menu-link:hover {
            background: rgba(38, 139, 210, 0.1);
            color: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .menu-link.active {
            background: #268bd2;
            color: white;
        }
        
        body[data-theme="solarized-dark"] .sidebar-collapse-btn {
            background: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .sidebar-collapse-btn:hover {
            background: #2aa198;
        }
        
        body[data-theme="solarized-dark"] .dashboard-card {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .quick-actions {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .card-title {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .card-subtitle {
            color: #657b83;
        }
        
        body[data-theme="solarized-dark"] .card-content {
            color: #839496;
        }
        
        body[data-theme="solarized-dark"] .action-btn {
            background: linear-gradient(45deg, #268bd2, #2aa198);
        }
        
        /* Synth Wave Theme */
        body[data-theme="synth-wave"] {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a0033 50%, #000000 100%);
        }
        
        body[data-theme="synth-wave"] .sidebar {
            background: rgba(26, 26, 26, 0.95);
            border-right: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .sidebar-logo {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .sidebar-subtitle {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .menu-link {
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .menu-link:hover {
            background: rgba(255, 0, 255, 0.1);
            color: #ff00ff;
        }
        
        body[data-theme="synth-wave"] .menu-link.active {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            color: white;
        }
        
        body[data-theme="synth-wave"] .sidebar-collapse-btn {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
        }
        
        body[data-theme="synth-wave"] .dashboard-card {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .quick-actions {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .card-title {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .card-subtitle {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .card-content {
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .action-btn {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.4);
        }
          body[data-theme="synth-wave"] .quick-actions h3 {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        /* Theme-specific styles for user dropdown */
        body[data-theme="solarized-dark"] .user-dropdown {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .user-dropdown-item {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .user-dropdown-item:hover {
            background: rgba(38, 139, 210, 0.1);
        }
        
        body[data-theme="synth-wave"] .user-dropdown {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .user-dropdown-item {
            color: #ffffff;
        }
          body[data-theme="synth-wave"] .user-dropdown-item:hover {
            background: rgba(255, 0, 255, 0.1);
        }
          /* Tooltip styles for different themes */
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link::after {
            background: linear-gradient(135deg, #268bd2, #2aa198);
            color: #fdf6e3;
            box-shadow: 0 4px 12px rgba(38, 139, 210, 0.3);
        }
        
        body[data-theme="synth-wave"] .sidebar.collapsed .menu-link::after {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            color: #ffffff;
            border: 1px solid rgba(255, 0, 255, 0.5);
            box-shadow: 0 4px 12px rgba(255, 0, 255, 0.4);
        }
          /* Theme-specific styling for collapsed sidebar */
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link {
            background: rgba(253, 246, 227, 0.9);
            border-color: rgba(38, 139, 210, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }          body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link:hover {
            background: rgba(38, 139, 210, 0.1);
            border-color: rgba(38, 139, 210, 0.25);
            box-shadow: 0 3px 10px rgba(38, 139, 210, 0.12);
        }
        
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, #268bd2, #2aa198);
        }
          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(255, 0, 255, 0.2);
            box-shadow: 0 2px 8px rgba(255, 0, 255, 0.08);
        }          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link:hover {
            background: rgba(255, 0, 255, 0.1);
            border-color: rgba(255, 0, 255, 0.3);
            box-shadow: 0 3px 10px rgba(255, 0, 255, 0.15);
        }
          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            box-shadow: 0 4px 15px rgba(255, 0, 255, 0.4);
        }
        
        /* Theme-specific styles for user avatar */
        body[data-theme="solarized-dark"] .user-avatar {
            background: rgba(38, 139, 210, 0.2);
            border: 2px solid rgba(38, 139, 210, 0.3);
        }
        
        body[data-theme="solarized-dark"] .user-avatar-text {
            color: #268bd2;
        }
        
        body[data-theme="synth-wave"] .user-avatar {
            background: rgba(255, 0, 255, 0.2);
            border: 2px solid rgba(255, 0, 255, 0.5);
            box-shadow: 0 0 10px rgba(255, 0, 255, 0.3);
        }
        
        body[data-theme="synth-wave"] .user-avatar-text {
            color: #ff00ff;
            text-shadow: 0 0 5px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="dark"] .user-avatar {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        body[data-theme="dark"] .user-avatar-text {
            color: #ffffff;
        }
        
        /* Pulse animation for real-time indicator */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }
        
        /* Animasi slide dihapus untuk update instant tanpa efek */
        
        .activity-item {
            /* Hapus animasi slide - update langsung tanpa efek */
            flex-shrink: 0; /* Prevent item from shrinking */
            min-height: 60px; /* Minimum height untuk consistency */
        }
        
        .activity-item.new {
            background: linear-gradient(90deg, rgba(40, 167, 69, 0.1), rgba(255, 255, 255, 0.1));
            border-left: 3px solid #28a745;
            /* Hapus transform scale - update langsung tanpa efek */
        }
        
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }
        
        /* Real-time card specific styling */
        #realtimeActivities {
            min-height: 200px;
            max-height: 300px; /* Batas tinggi maksimal card */
            position: relative;
            overflow-y: auto; /* Enable vertical scroll */
            overflow-x: hidden; /* Hide horizontal scroll */
            /* Hide scrollbar untuk Chrome, Safari dan Opera */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer 10+ */
        }
        
        /* Hide scrollbar untuk webkit browsers */
        #realtimeActivities::-webkit-scrollbar {
            display: none;
        }
        
        /* Smooth scroll behavior */
        #realtimeActivities {
            scroll-behavior: smooth;
        }
        
        /* Card khusus untuk Aktivitas Terbaru - Fixed height */
        .realtime-activities-card {
            min-height: 400px;
            max-height: 450px;
            display: flex;
            flex-direction: column;
        }
        
        /* Ensure card content can flex */
        .realtime-activities-card .card-content {
            flex: 1;
            overflow: hidden;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
        
        .empty-state .empty-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
            display: block;
        }
        
        // ...existing code...
    </style>
    
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">My Money 💰</div>
            </div>
            
            <nav>
                <ul class="sidebar-menu">
                    @php
                        $menuItems = config('menu', []);
                        $currentRoute = request()->route()->getName();
                    @endphp
                    
                    @foreach($menuItems as $key => $menu)
                        <li class="menu-item">
                            <a href="{{ $menu['route'] === '#' ? 'javascript:void(0)' : route($menu['route']) }}" 
                               class="menu-link {{ $currentRoute === $menu['route'] ? 'active' : '' }}"
                               onclick="{{ !empty($menu['submenu']) ? 'toggleSubmenu(this)' : '' }}">
                                <i class="menu-icon {{ $menu['icon'] }}"></i>
                                <span class="menu-text">{{ $menu['name'] }}</span>
                                @if(!empty($menu['submenu']))
                                    <i class="menu-arrow fas fa-chevron-right"></i>
                                @endif
                            </a>
                            
                            @if(!empty($menu['submenu']))
                                <ul class="submenu">
                                    @foreach($menu['submenu'] as $subKey => $submenu)
                                        <li class="submenu-item">
                                            <a href="{{ route($submenu['route']) }}" class="submenu-link">
                                                <i class="submenu-icon {{ $submenu['icon'] }}"></i>
                                                <span>{{ $submenu['name'] }}</span>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                        </li>
                    @endforeach
                </ul>
            </nav>
        </aside>
        
        <!-- Collapse Button -->
        <button class="sidebar-collapse-btn" id="sidebarCollapseBtn" onclick="toggleSidebarCollapse()">
            <i class="fas fa-chevron-left" id="collapseIcon"></i>
        </button>
        
        <!-- Main Content -->
        <main class="main-content">            <!-- Topbar -->
            <header class="topbar">
                <div class="topbar-left">
                    <h1>Dashboard</h1>
                </div>
                  <div class="topbar-right">
                    <!-- Theme Switcher -->
                    <div class="theme-switcher">
                        <button class="theme-toggle" id="themeToggle" onclick="toggleThemeDropdown()">
                            <i class="theme-icon" id="themeIcon">�</i>
                            <span class="theme-text" id="themeText">Light</span>
                            <i class="dropdown-arrow">▼</i>
                        </button>
                        <div class="theme-dropdown" id="themeDropdown">
                            <div class="theme-option" onclick="setTheme('light')">
                                <i class="theme-option-icon">🌞</i>
                                <span>Light</span>
                            </div>
                            <div class="theme-option" onclick="setTheme('solarized-dark')">
                                <i class="theme-option-icon">🌙</i>
                                <span>Solarized Dark</span>
                            </div>
                            <!--<div class="theme-option" onclick="setTheme('synth-wave')">
                                <i class="theme-option-icon">🌆</i>
                                <span>Synth Wave</span>
                            </div>-->
                        </div>
                    </div>
                    <!-- User Info Dropdown -->
                    <div class="user-info-dropdown">
                        <button class="user-info-toggle" id="userInfoToggle" onclick="toggleUserDropdown()">
                            <div class="user-avatar">
                                @if(Auth::user()->avatar)
                                    <img src="{{ Storage::url(Auth::user()->avatar) }}" 
                                         alt="{{ Auth::user()->name }}" 
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="user-avatar-text" style="display: none;">
                                        {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                    </div>
                                @else
                                    <div class="user-avatar-text">
                                        {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                    </div>
                                @endif
                            </div>
                            <div class="user-details">
                                <h4>{{ Auth::user()->name }}</h4>
                            </div>
                            <i class="dropdown-arrow">▼</i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <div class="user-dropdown-item" onclick="goToProfile()">
                                <i class="user-dropdown-icon fas fa-user"></i>
                                <span>Profile</span>
                            </div>
                            <div class="user-dropdown-item" onclick="confirmLogout()">
                                <i class="user-dropdown-icon fas fa-sign-out-alt"></i>
                                <span>Logout</span>
                            </div>
                        </div>
                    </div>
                    
                    
                    <!-- Hidden Logout Form -->
                    <form id="logoutForm" method="POST" action="{{ route('logout') }}" style="display: none;">
                        @csrf
                    </form>
                </div></header>
            
            <!-- Dashboard Content -->
            <div class="dashboard-container">              <!-- Dashboard Cards -->
                <div class="dashboard-content">
                    <!-- Financial Overview Card -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon finance-icon">💰</div>
                            <div>
                                <h3 class="card-title">Saldo Total</h3>
                                <p class="card-subtitle">Semua akun keuangan Anda</p>
                            </div>
                        </div>
                        <div class="card-content">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <div style="font-size: 2rem; font-weight: 600; color: var(--primary-color);" id="totalBalance">
                                    {{ formatRupiah($stats['totalBalance'] ?? 0) }}
                                </div>
                                <div style="background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;" id="balanceGrowth">
                                    <i class="fas fa-arrow-up"></i> +{{ $stats['balanceGrowth'] ?? 0 }}%
                                </div>
                            </div>
                            
                            <!-- Current Month -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9rem; margin-bottom: 15px;">
                                <div>
                                    <span style="color: #28a745;">↗ Pemasukan Bulan Ini</span><br>
                                    <strong id="monthlyIncome">{{ formatRupiah($stats['monthlyIncome'] ?? 0) }}</strong>
                                </div>
                                <div>
                                    <span style="color: #dc3545;">↘ Pengeluaran Bulan Ini</span><br>
                                    <strong id="monthlyExpense">{{ formatRupiah($stats['monthlyExpense'] ?? 0) }}</strong>
                                </div>
                            </div>
                            
                            <!-- Monthly History -->
                            <div style="border-top: 1px solid #f0f0f0; padding-top: 15px;">
                                <div style="font-size: 0.9rem; font-weight: 600; margin-bottom: 10px; color: #666;">
                                    📊 Histori 6 Bulan Terakhir
                                </div>
                                <div id="monthlyHistoryContainer" style="max-height: 180px; overflow-y: auto;">
                                    <div style="text-align: center; padding: 20px; color: #999;">
                                        <i class="fas fa-spinner fa-spin"></i> Memuat histori...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats Card -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon stats-icon">�</div>
                            <div>
                                <h3 class="card-title">Statistik Cepat</h3>
                                <p class="card-subtitle">Ringkasan aktivitas hari ini</p>
                            </div>
                        </div>
                        <div class="card-content">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div style="text-align: center; padding: 15px; background: rgba(102, 126, 234, 0.1); border-radius: 10px;">
                                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--primary-color);" id="todayTransactions">{{ $stats['todayTransactions'] ?? 0 }}</div>
                                    <div style="font-size: 0.8rem; color: #666;">Transaksi Hari Ini</div>
                                </div>
                                <div style="text-align: center; padding: 15px; background: rgba(40, 167, 69, 0.1); border-radius: 10px;">
                                    <div style="font-size: 1.5rem; font-weight: 600; color: #28a745;" id="activeAccounts">{{ $stats['activeAccounts'] ?? 0 }}</div>
                                    <div style="font-size: 0.8rem; color: #666;">Rekening Aktif</div>
                                </div>
                            </div>
                            
                            <!-- Monthly Transaction Stats -->
                            <div style="border-top: 1px solid #f0f0f0; padding-top: 15px; margin-bottom: 15px;">
                                <div style="font-size: 0.9rem; font-weight: 600; margin-bottom: 10px; color: #666;">
                                    📈 Transaksi per Bulan
                                </div>
                                <div id="monthlyTransactionStatsContainer" style="max-height: 120px; overflow-y: auto;">
                                    <div style="text-align: center; padding: 15px; color: #999;">
                                        <i class="fas fa-spinner fa-spin"></i> Memuat statistik...
                                    </div>
                                </div>
                            </div>
                            
                            <div style="padding: 10px; background: rgba(255, 193, 7, 0.1); border-radius: 8px;">
                                <div style="font-size: 0.9rem; color: #856404;">
                                    <i class="fas fa-lightbulb"></i> <strong>Tips:</strong> Catat setiap transaksi untuk kontrol keuangan yang lebih baik!
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- NEW Real-time Activity Card -->
                    <div class="dashboard-card realtime-activities-card">
                        <div class="card-header">
                            <div class="card-icon activity-icon">🔔</div>
                            <div>
                                <h3 class="card-title">Aktivitas Terbaru</h3>
                                <p class="card-subtitle">Update real-time transaksi</p>
                            </div>
                            <div style="margin-left: auto;">
                                <div id="realTimeIndicator" style="display: flex; align-items: center; font-size: 0.8rem;">
                                    <div style="width: 8px; height: 8px; border-radius: 50%; background: #28a745; margin-right: 5px; animation: pulse 2s infinite;"></div>
                                    <span style="color: #28a745;">LIVE</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div id="realtimeActivities">
                                <div style="text-align: center; padding: 30px; color: #666;">
                                    <div style="font-size: 2rem; margin-bottom: 10px;">⏳</div>
                                    <p>Memuat aktivitas terbaru...</p>
                                    <small>Menghubungkan ke server real-time</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>🚀 Aksi Cepat</h3>
                    <div class="action-buttons">
                        <a href="{{ route('transactions.create') }}?type=income" class="action-btn">💰 Tambah Pemasukan</a>
                        <a href="{{ route('transactions.create') }}?type=expense" class="action-btn">💸 Catat Pengeluaran</a>
                        <a href="{{ route('reports.index') }}" class="action-btn">📊 Lihat Laporan</a>
                        <a href="{{ route('settings.index') }}" class="action-btn">⚙️ Pengaturan</a>
                        <a href="{{ route('profile.show') }}" class="action-btn">👤 Profil</a>
                        <a href="{{ route('settings.index') }}" class="action-btn">❓ Bantuan</a>
                    </div>
                </div>
            </div>
        </main>
    </div>
    </body>
    @vite(['resources/js/app.js'])
    <!-- Currency Helper -->
    <script src="{{ asset('js/currency.js') }}"></script>
    <script>
        // Dashboard Real-time Updates
        let dashboardUpdateInterval;
        
        function initializeDashboard() {
            // Initialize empty realtime activities data
            window.realtimeActivitiesData = [];
            
            // Load initial data and start real-time updates
            loadDashboardData();
            startRealTimeUpdates();
            
            console.log('✅ NEW Realtime Dashboard initialized');
        }
        
        function loadDashboardData() {
            console.log('Loading dashboard data...');
            fetch('{{ route("dashboard.stats") }}', {
                method: 'GET',
                credentials: 'same-origin', // Include cookies for session authentication
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    console.log('Dashboard response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('✅ Dashboard data received:', data);
                    console.log('🔍 Checking data structure:');
                    console.log('- monthlyHistory:', data.monthlyHistory);
                    console.log('- monthlyTransactionStats:', data.monthlyTransactionStats);
                    console.log('- recentActivities:', data.recentActivities);
                    updateDashboardUI(data);
                })
                .catch(error => {
                    console.error('Error loading dashboard data:', error);
                    // Retry after 5 seconds if error
                    setTimeout(loadDashboardData, 5000);
                });
        }
        
        function updateDashboardUI(stats) {
            console.log('updateDashboardUI called with stats:', stats);
            
            // Update balance
            const totalBalanceElement = document.getElementById('totalBalance');
            if (totalBalanceElement && stats.totalBalance !== undefined) {
                console.log('Updating total balance:', stats.totalBalance);
                totalBalanceElement.textContent = formatRupiah(stats.totalBalance);
            }
            
            // Update growth percentage
            const balanceGrowthElement = document.getElementById('balanceGrowth');
            if (balanceGrowthElement && stats.balanceGrowth !== undefined) {
                const growth = parseFloat(stats.balanceGrowth);
                const isPositive = growth >= 0;
                console.log('Updating balance growth:', growth);
                balanceGrowthElement.innerHTML = `
                    <i class="fas fa-arrow-${isPositive ? 'up' : 'down'}"></i> 
                    ${isPositive ? '+' : ''}${growth}%
                `;
                balanceGrowthElement.style.background = isPositive ? '#28a745' : '#dc3545';
            }
            
            // Update monthly income/expense
            const monthlyIncomeElement = document.getElementById('monthlyIncome');
            if (monthlyIncomeElement && stats.monthlyIncome !== undefined) {
                console.log('Updating monthly income:', stats.monthlyIncome);
                monthlyIncomeElement.textContent = formatRupiah(stats.monthlyIncome);
            }
            
            const monthlyExpenseElement = document.getElementById('monthlyExpense');
            if (monthlyExpenseElement && stats.monthlyExpense !== undefined) {
                console.log('Updating monthly expense:', stats.monthlyExpense);
                monthlyExpenseElement.textContent = formatRupiah(stats.monthlyExpense);
            }
            
            // Update quick stats
            const todayTransactionsElement = document.getElementById('todayTransactions');
            if (todayTransactionsElement && stats.todayTransactions !== undefined) {
                console.log('Updating today transactions:', stats.todayTransactions);
                // Handle both array and number formats
                const todayCount = Array.isArray(stats.todayTransactions) ? stats.todayTransactions.length : stats.todayTransactions;
                todayTransactionsElement.textContent = todayCount;
            }
            
            const activeAccountsElement = document.getElementById('activeAccounts');
            if (activeAccountsElement && stats.activeAccounts !== undefined) {
                console.log('Updating active accounts:', stats.activeAccounts);
                activeAccountsElement.textContent = stats.activeAccounts;
            }
            
            // Update recent activities - NEW REALTIME CARD IMPLEMENTATION
            console.log('Updating NEW realtime activities with data:', stats.recentActivities);
            if (stats.recentActivities !== undefined) {
                updateRealtimeActivities(stats.recentActivities || []);
            } else {
                console.warn('recentActivities not found in stats data');
            }
            
            // Update monthly history
            console.log('Checking monthlyHistory data:', stats.monthlyHistory);
            if (stats.monthlyHistory !== undefined) {
                updateMonthlyHistory(stats.monthlyHistory || []);
            } else {
                console.warn('monthlyHistory not found in stats data');
            }
            
            // Update monthly transaction stats
            console.log('Checking monthlyTransactionStats data:', stats.monthlyTransactionStats);
            if (stats.monthlyTransactionStats !== undefined) {
                updateMonthlyTransactionStats(stats.monthlyTransactionStats || []);
            } else {
                console.warn('monthlyTransactionStats not found in stats data');
            }
        }
        
        // NEW REALTIME ACTIVITIES IMPLEMENTATION
        function updateRealtimeActivities(activities) {
            const container = document.getElementById('realtimeActivities');
            if (!container) {
                console.error('Realtime activities container not found');
                return;
            }
            
            console.log('🔄 Updating realtime activities with:', activities);
            
            // Store globally for real-time updates
            window.realtimeActivitiesData = activities || [];
            
            // Clear and rebuild the content
            renderRealtimeActivities(container, activities);
            
            // Update real-time indicator
            updateRealTimeIndicator();
        }
        
        function renderRealtimeActivities(container, activities) {
            if (!activities || activities.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <span class="empty-icon">📭</span>
                        <p style="margin: 0; font-weight: 500;">Belum ada aktivitas</p>
                        <small style="color: #999;">Transaksi terbaru akan muncul di sini secara real-time</small>
                    </div>
                `;
                return;
            }
            
            // Show latest activities (lebih banyak untuk testing scroll)
            const latestActivities = activities.slice(0, 15); // Increased from 5 to 15
            
            const activitiesHTML = latestActivities.map((activity, index) => {
                const amount = typeof activity.amount === 'number' ? 
                    Math.abs(activity.amount) : 
                    Math.abs(parseFloat(activity.amount) || 0);
                    
                const isNew = activity.time && (
                    activity.time.includes('Baru saja') || 
                    activity.time.includes('menit yang lalu') ||
                    activity.time.includes('detik yang lalu')
                );
                
                const activityIcon = activity.icon || (activity.type === 'expense' ? '💸' : '💰');
                
                return `
                    <div class="activity-item ${isNew ? 'new' : ''}" 
                         style="display: flex; justify-content: space-between; align-items: center; 
                                padding: 12px; margin-bottom: 8px; border-radius: 8px; 
                                border: 1px solid #f0f0f0; background: #fff;
                                ${index === 0 && isNew ? 'border-color: #28a745; background: linear-gradient(90deg, rgba(40, 167, 69, 0.05), #fff);' : ''}">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; font-weight: 500; margin-bottom: 4px;">
                                <span style="margin-right: 8px; font-size: 1.2rem;">${activityIcon}</span>
                                <span>${activity.title}</span>
                                ${isNew ? '<span style="background: #28a745; color: white; font-size: 0.6rem; padding: 2px 6px; border-radius: 10px; margin-left: 8px; font-weight: bold;">BARU</span>' : ''}
                            </div>
                            <div style="font-size: 0.8rem; color: #666; display: flex; align-items: center;">
                                <i class="fas fa-clock" style="margin-right: 4px;"></i>
                                <span>${activity.time}</span>
                                ${activity.category ? `<span style="margin-left: 8px; color: #999;">• ${activity.category}</span>` : ''}
                            </div>
                        </div>
                        <div style="text-align: right; margin-left: 12px;">
                            <div style="color: ${activity.type === 'expense' ? '#dc3545' : '#28a745'}; 
                                        font-weight: 600; font-size: 1rem;">
                                ${activity.type === 'expense' ? '-' : '+'}${formatRupiah(amount)}
                            </div>
                            ${activity.account ? `<div style="font-size: 0.7rem; color: #888; margin-top: 2px;">${activity.account}</div>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = activitiesHTML + `
                <div style="text-align: center; margin-top: 15px; padding: 10px;">
                    <div style="font-size: 0.8rem; color: #28a745; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-wifi" style="margin-right: 5px; animation: pulse 2s infinite;"></i>
                        <span>Real-time • Terakhir update: ${new Date().toLocaleTimeString('id-ID')}</span>
                    </div>
                </div>
            `;
        }
        
        function updateRealTimeIndicator() {
            const indicator = document.getElementById('realTimeIndicator');
            if (indicator) {
                // Flash the indicator to show activity
                indicator.style.opacity = '0.5';
                setTimeout(() => {
                    indicator.style.opacity = '1';
                }, 200);
            }
        }
        
        // INSTANT UPDATE FUNCTION FOR NEW TRANSACTIONS - TANPA EFEK SLIDE
        function addNewTransactionToRealtimeCard(transaction) {
            console.log('🚀 Adding new transaction instantly:', transaction);
            
            const container = document.getElementById('realtimeActivities');
            if (!container) {
                console.error('Realtime activities container not found');
                return;
            }
            
            // Create new activity object matching server format
            const newActivity = {
                id: transaction.id || Date.now(),
                icon: transaction.type === 'expense' ? '💸' : '💰',
                title: transaction.title || 'Transaksi Baru',
                time: 'Baru saja',
                amount: transaction.amount || 0,
                type: transaction.type || 'expense',
                category: transaction.category || 'Lainnya',
                account: transaction.account || 'Akun'
            };
            
            // Get current activities and add new one at the beginning
            const currentActivities = window.realtimeActivitiesData || [];
            const updatedActivities = [newActivity, ...currentActivities].slice(0, 10); // Keep max 10
            
            // Update global data
            window.realtimeActivitiesData = updatedActivities;
            
            // TANPA EFEK ANIMASI - Langsung render
            renderRealtimeActivities(container, updatedActivities);
            
            // Update indicator saja
            updateRealTimeIndicator();
            
            console.log('✅ New transaction added instantly without animations');
        }
        
        // Remove old functions
        function updateRecentActivities() { /* REMOVED - using updateRealtimeActivities now */ }
        
        function formatRupiah(amount) {
            if (typeof amount !== 'number') {
                amount = parseFloat(amount) || 0;
            }
            return 'Rp ' + amount.toLocaleString('id-ID');
        }
        
        // Function to update monthly history in Saldo Total card
        function updateMonthlyHistory(monthlyHistory) {
            const container = document.getElementById('monthlyHistoryContainer');
            if (!container) {
                console.error('Monthly history container not found');
                return;
            }
            
            console.log('🔄 Updating monthly history with:', monthlyHistory);
            
            if (!monthlyHistory || monthlyHistory.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #999;">
                        📈 Belum ada data histori
                    </div>
                `;
                return;
            }
            
            const historyHTML = monthlyHistory.map((month, index) => {
                const netAmount = month.net || 0;
                const isPositive = netAmount >= 0;
                const isCurrent = month.is_current || false;
                
                return `
                    <div style="display: flex; justify-content: space-between; align-items: center; 
                                padding: 8px 12px; margin-bottom: 6px; border-radius: 6px; 
                                background: ${isCurrent ? 'rgba(102, 126, 234, 0.1)' : '#f8f9fa'}; 
                                border-left: 3px solid ${isCurrent ? 'var(--primary-color)' : '#dee2e6'};">
                        <div>
                            <div style="font-weight: ${isCurrent ? '600' : '500'}; font-size: 0.85rem;">
                                ${month.month} ${isCurrent ? '(Saat ini)' : ''}
                            </div>
                            <div style="font-size: 0.7rem; color: #666; margin-top: 2px;">
                                Masuk: ${formatRupiah(month.income)} • Keluar: ${formatRupiah(month.expense)}
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="color: ${isPositive ? '#28a745' : '#dc3545'}; 
                                        font-weight: 600; font-size: 0.8rem;">
                                ${isPositive ? '+' : ''}${formatRupiah(netAmount)}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = historyHTML;
        }
        
        // Function to update monthly transaction stats in Statistik Cepat card
        function updateMonthlyTransactionStats(monthlyStats) {
            const container = document.getElementById('monthlyTransactionStatsContainer');
            if (!container) {
                console.error('Monthly transaction stats container not found');
                return;
            }
            
            console.log('🔄 Updating monthly transaction stats with:', monthlyStats);
            
            if (!monthlyStats || monthlyStats.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 15px; color: #999;">
                        📊 Belum ada data transaksi
                    </div>
                `;
                return;
            }
            
            const statsHTML = monthlyStats.map((month, index) => {
                const isCurrent = month.is_current || false;
                
                return `
                    <div style="display: flex; justify-content: space-between; align-items: center; 
                                padding: 8px 12px; margin-bottom: 6px; border-radius: 6px; 
                                background: ${isCurrent ? 'rgba(40, 167, 69, 0.1)' : '#f8f9fa'}; 
                                border-left: 3px solid ${isCurrent ? '#28a745' : '#dee2e6'};">
                        <div>
                            <div style="font-weight: ${isCurrent ? '600' : '500'}; font-size: 0.85rem;">
                                ${month.month} ${isCurrent ? '(Saat ini)' : ''}
                            </div>
                            <div style="font-size: 0.7rem; color: #666; margin-top: 2px;">
                                Masuk: ${month.income_transactions} • Keluar: ${month.expense_transactions}
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="color: var(--primary-color); font-weight: 600; font-size: 1rem;">
                                ${month.total_transactions}
                            </div>
                            <div style="font-size: 0.7rem; color: #888;">
                                total
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = statsHTML;
        }
        
        function startRealTimeUpdates() {
            // Clear existing interval if any
            if (dashboardUpdateInterval) {
                clearInterval(dashboardUpdateInterval);
            }
            
            // Update dashboard every 30 seconds for full sync
            dashboardUpdateInterval = setInterval(() => {
                console.log('🕐 Scheduled dashboard sync...');
                loadDashboardData();
            }, 30000); // 30 seconds for less frequent but complete updates
        }
        
        function stopRealTimeUpdates() {
            if (dashboardUpdateInterval) {
                clearInterval(dashboardUpdateInterval);
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });
        
        // Stop updates when page is unloaded
        window.addEventListener('beforeunload', function() {
            stopRealTimeUpdates();
        });
        
        // Pusher real-time updates (if available)
        @if(config('broadcasting.connections.pusher.key'))
            try {
                console.log('Initializing Pusher with key:', '{{ config("broadcasting.connections.pusher.key") }}');
                console.log('Pusher cluster:', '{{ config("broadcasting.connections.pusher.options.cluster") }}');
                const pusher = new Pusher('{{ config("broadcasting.connections.pusher.key") }}', {
                    cluster: '{{ config("broadcasting.connections.pusher.options.cluster") }}',
                    forceTLS: true,
                    enabledTransports: ['ws', 'wss']
                });
                
                console.log('Subscribing to financial-updates channel...');
                const channel = pusher.subscribe('financial-updates');
                
                channel.bind('pusher:subscription_succeeded', function() {
                    console.log('Successfully subscribed to financial-updates channel');
                });
                
                channel.bind('pusher:subscription_error', function(error) {
                    console.error('Failed to subscribe to financial-updates channel:', error);
                });
                
                // Listen for new transactions
                channel.bind('new-transaction', function(data) {
                    console.log('🔥 NEW TRANSACTION EVENT RECEIVED:', data);
                    console.log('Current user ID:', {{ auth()->id() }});
                    console.log('Event user ID:', data.user_id);
                    
                    if (data.user_id === {{ auth()->id() }}) {
                        console.log('✅ Event is for current user - updating dashboard...');
                        
                        // Show notification immediately
                        if (data.transaction) {
                            showTransactionNotification(data.transaction);
                        }
                        
                        // INSTANT UPDATE for realtime activities card
                        console.log('� INSTANT UPDATE: Adding new transaction to realtime card');
                        addNewTransactionToRealtimeCard(data.transaction);
                        
                        // Also refresh full dashboard data
                        setTimeout(() => {
                            console.log('🔄 Refreshing full dashboard data...');
                            loadDashboardData();
                        }, 1000);
                        
                    } else {
                        console.log('❌ Event not for current user, ignoring');
                    }
                });
                
                // Listen for balance updates
                channel.bind('balance-updated', function(data) {
                    console.log('Balance updated event received:', data);
                    if (data.user_id === {{ auth()->id() }}) {
                        loadDashboardData();
                    }
                });
                
                // Listen for stats updates
                channel.bind('stats-updated', function(data) {
                    console.log('Stats updated via Pusher:', data);
                    if (data.user_id === {{ auth()->id() }}) {
                        console.log('Updating dashboard UI with Pusher data...');
                        updateDashboardUI(data);
                        
                        // Also refresh dashboard data to ensure consistency
                        setTimeout(() => {
                            loadDashboardData();
                        }, 500);
                    }
                });
                
                // Listen for account updates (specifically for account count changes)
                channel.bind('account-updated', function(data) {
                    console.log('Account updated via Pusher, refreshing dashboard...');
                    if (data.user_id === {{ auth()->id() }}) {
                        loadDashboardData();
                    }
                });
                
                console.log('Real-time updates connected via Pusher');
            } catch (error) {
                console.log('Pusher not available, using periodic updates only');
            }
        @endif
        
        function showTransactionNotification(transaction) {
            console.log('🔔 Showing notification for transaction:', transaction);
            
            const toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true
            });
            
            const isIncome = transaction.type === 'income';
            const amount = typeof transaction.amount === 'number' ? 
                Math.abs(transaction.amount) : 
                Math.abs(parseFloat(transaction.amount) || 0);
            
            toast.fire({
                icon: isIncome ? 'success' : 'info',
                title: `${isIncome ? 'Pemasukan' : 'Pengeluaran'} Baru`,
                text: `${transaction.title}: ${isIncome ? '+' : '-'}${formatRupiah(amount)}`,
                background: '#fff',
                color: '#333'
            });
        }
        
        // Sidebar functionality
        function toggleSidebarCollapse() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('.main-content');
            const collapseIcon = document.getElementById('collapseIcon');
            const menuLinks = document.querySelectorAll('.menu-link');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('sidebar-collapsed');
            
            // Toggle icon with smooth transition
            if (sidebar.classList.contains('collapsed')) {
                collapseIcon.className = 'fas fa-chevron-right';
                
                // Add tooltips to menu items
                menuLinks.forEach(link => {
                    const menuText = link.querySelector('.menu-text');
                    if (menuText) {
                        link.setAttribute('data-tooltip', menuText.textContent.trim());
                    }
                });
            } else {
                collapseIcon.className = 'fas fa-chevron-left';
                
                // Remove tooltips
                menuLinks.forEach(link => {
                    link.removeAttribute('data-tooltip');
                });
            }
            
            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }
        
        function toggleSubmenu(element) {
            const submenu = element.nextElementSibling;
            const arrow = element.querySelector('.menu-arrow');
            
            if (submenu) {
                submenu.classList.toggle('open');
                arrow.classList.toggle('rotated');
            }
        }        // Theme functionality
        function toggleThemeDropdown() {
            const dropdown = document.getElementById('themeDropdown');
            dropdown.classList.toggle('show');
        }
        
        // User dropdown functionality
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }
        
        function goToProfile() {
            window.location.href = '{{ route("profile.show") }}';
        }
        
        // Function to apply theme without saving to database (used on page load)
        function applyThemeOnly(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');
            
            switch(theme) {
                case 'light':
                    themeIcon.textContent = '🌞';
                    themeText.textContent = 'Light';
                    break;
                case 'solarized-dark':
                    themeIcon.textContent = '🌙';
                    themeText.textContent = 'Solarized Dark';
                    break;
                case 'synth-wave':
                    themeIcon.textContent = '🌆';
                    themeText.textContent = 'Synth Wave';
                    break;
            }
        }
        
        // Function to set theme and save to database (used when user clicks theme option)
          function setTheme(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');
            
            // Update UI immediately
            switch(theme) {
                case 'light':
                    themeIcon.textContent = '🌞';
                    themeText.textContent = 'Light';
                    break;
                case 'solarized-dark':
                    themeIcon.textContent = '🌙';
                    themeText.textContent = 'Solarized Dark';
                    break;
                case 'synth-wave':
                    themeIcon.textContent = '🌆';
                    themeText.textContent = 'Synth Wave';
                    break;
            }
            
            document.getElementById('themeDropdown').classList.remove('show');
            
            // Save to database
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            // Show loading notification
            const loadingToast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true
            });
            
            loadingToast.fire({
                icon: 'info',
                title: 'Menyimpan tema...'
            });
            
            fetch('/settings/theme', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    theme: theme
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Get theme display name
                    let themeDisplayName = '';
                    switch(theme) {
                        case 'light':
                            themeDisplayName = 'Light Mode';
                            break;
                        case 'solarized-dark':
                            themeDisplayName = 'Solarized Dark';
                            break;
                        case 'synth-wave':
                                                       themeDisplayName = 'Synth Wave';
                            break;
                    }
                    
                    // Show success notification
                    const successToast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true
                    });
                    
                    successToast.fire({
                        icon: 'success',
                        title: `✅ Tema berhasil diterapkan!`,
                        text: `Tema ${themeDisplayName} telah disimpan`
                    });
                } else {
                    throw new Error(data.message || 'Gagal menyimpan tema');
                }
            })
            .catch(error => {
                console.error('Error saving theme:', error);
                
                // Show error notification
                const errorToast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true
                });
                
                errorToast.fire({
                    icon: 'error',
                    title: '❌ Gagal menyimpan tema',
                    text: 'Tema telah diterapkan secara lokal, namun gagal disimpan ke database'
                });
            });
        }          // Load saved theme and sidebar state
        document.addEventListener('DOMContentLoaded', function() {
            // Load theme from database (user preference)
            const userTheme = '{{ Auth::user()->theme ?? "light" }}';
            const savedLocalTheme = localStorage.getItem('theme') || userTheme;
            
            // If database theme differs from localStorage, use database theme
            const themeToUse = userTheme !== 'light' ? userTheme : savedLocalTheme;
            
            // Apply theme without saving (to avoid showing notification on page load)
            applyThemeOnly(themeToUse);              // Load sidebar collapsed state
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                // Set collapsed state immediately for smooth transition
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.querySelector('.main-content');
                const collapseIcon = document.getElementById('collapseIcon');
                const menuLinks = document.querySelectorAll('.menu-link');
                
                sidebar.classList.add('collapsed');
                mainContent.classList.add('sidebar-collapsed');
                collapseIcon.className = 'fas fa-chevron-right';
                
                // Add tooltips to menu items
                menuLinks.forEach(link => {
                    const menuText = link.querySelector('.menu-text');
                    if (menuText) {
                        link.setAttribute('data-tooltip', menuText.textContent.trim());
                    }
                });
            }
            
            // Initialize real-time dashboard
            initializeRealtimeDashboard();
            
            // Show welcome greeting only once per session
            const hasShownGreeting = sessionStorage.getItem('hasShownGreeting');
            if (!hasShownGreeting) {
                setTimeout(() => {
                    showWelcomeGreeting();
                    sessionStorage.setItem('hasShownGreeting', 'true');
                }, 1000);
            }
        });
        
        // Real-time Dashboard Functions
        function initializeRealtimeDashboard() {
            // Load real data instead of dummy data
            loadDashboardData();
            
            // Update data every 30 seconds
            setInterval(loadDashboardData, 30000);
            
            // Initialize Pusher for real-time updates
            if (typeof Pusher !== 'undefined') {
                initializePusher();
            }
        }
        
        function updateDashboardData() {
            // This function is replaced by loadDashboardData() 
            // which fetches real data from the server
            loadDashboardData();
        }
        
        function animateValue(elementId, start, end, duration, format = 'number') {
            const element = document.getElementById(elementId);
            const startTime = performance.now();
            
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Easing function
                const easedProgress = 1 - Math.pow(1 - progress, 3);
                const current = Math.round(start + (end - start) * easedProgress);
                
                if (format === 'currency') {
                    element.textContent = 'Rp ' + current.toLocaleString('id-ID');
                } else {
                    element.textContent = current.toLocaleString('id-ID');
                }
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            
            requestAnimationFrame(update);
        }
        
        function updateRecentActivities() {
            // This function is now handled by updateRecentActivities(activities) 
            // called from updateDashboardUI() with real data
            console.log('updateRecentActivities() called - using real data from server');
        }
        
        function initializePusher() {
            try {
                // Replace with your actual Pusher key
                const pusher = new Pusher('{{ env("PUSHER_APP_KEY") }}', {
                    cluster: '{{ env("PUSHER_APP_CLUSTER") }}',
                    encrypted: true
                });
                
                const channel = pusher.subscribe('financial-updates');
                
                // Listen for new transactions
                channel.bind('new-transaction', function(data) {
                    showNewTransactionNotification(data);
                    updateDashboardData();
                });
                
                // Listen for balance updates
                channel.bind('balance-updated', function(data) {
                    animateValue('totalBalance', 0, data.newBalance, 1500, 'currency');
                });
                
                console.log('Pusher initialized successfully');
            } catch (error) {
                console.log('Pusher not configured or error:', error);
            }
        }
        
        function showNewTransactionNotification(data) {
            // Show toast notification for new transaction
            Swal.fire({
                toast: true,
                position: 'top-end',
                icon: data.type === 'income' ? 'success' : 'info',
                title: `Transaksi Baru: ${data.title}`,
                text: `${data.type === 'income' ? '+' : '-'}Rp ${Math.abs(data.amount).toLocaleString('id-ID')}`,
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        }        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const themeSwitcher = document.querySelector('.theme-switcher');
            const userInfoDropdown = document.querySelector('.user-info-dropdown');
            
            if (!themeSwitcher.contains(event.target)) {
                document.getElementById('themeDropdown').classList.remove('show');
            }
            
            if (!userInfoDropdown.contains(event.target)) {
                document.getElementById('userDropdown').classList.remove('show');
            }
        });
          // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const sidebarCollapseBtn = document.querySelector('.sidebar-collapse-btn');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !sidebarCollapseBtn.contains(event.target)) {
                sidebar.classList.remove('open');
            }
        });
        
        // Welcome greeting function
        function showWelcomeGreeting() {
            const userName = '{{ Auth::user()->name }}';
            const currentHour = new Date().getHours();
            let greeting = '';
            let icon = '';
            
            if (currentHour < 12) {
                greeting = 'Selamat Pagi';
                icon = '🌅';
            } else if (currentHour < 17) {
                greeting = 'Selamat Siang';
                icon = '☀️';
            } else if (currentHour < 21) {
                greeting = 'Selamat Sore';
                icon = '🌇';
            } else {
                greeting = 'Selamat Malam';
                icon = '🌙';
            }
            
            Swal.fire({
                title: `${icon} ${greeting}, ${userName}!`,
                text: 'Selamat datang kembali di My Money. Siap mengelola keuangan Anda hari ini?',
                icon: 'success',
                confirmButtonText: 'Mulai Sekarang!',
                confirmButtonColor: '#667eea',
                timer: 5000,
                timerProgressBar: true,
                allowOutsideClick: false,
                customClass: {
                    popup: 'animated bounceIn'
                }
            });
        }
          // Logout confirmation
        function confirmLogout() {
            Swal.fire({
                title: '🚪 Logout Konfirmasi',
                text: 'Apakah Anda yakin ingin keluar dari akun?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#dc3545',
                confirmButtonText: 'Ya, Logout',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show logout success message
                    Swal.fire({
                        title: '👋 Sampai Jumpa!',
                        text: 'Anda telah berhasil logout. Terima kasih telah menggunakan My Money!',
                        icon: 'success',
                        timer: 2000,
                        timerProgressBar: true,
                        showConfirmButton: false
                    }).then(() => {
                        // Clear session storage
                        sessionStorage.clear();
                        // Submit logout form
                        document.getElementById('logoutForm').submit();
                    });
                }
            });
        }
        
        // Flash Messages with SweetAlert2
        @if(session('status'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('status') }}',
                confirmButtonColor: '#667eea',
                timer: 3000,
                timerProgressBar: true
            });
        @endif

        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                confirmButtonColor: '#667eea',
                timer: 3000,
                timerProgressBar: true
            });
        @endif

        @if(session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Terjadi Kesalahan!',
                text: '{{ session('error') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        @if(session('warning'))
            Swal.fire({
                icon: 'warning',
                title: 'Peringatan!',
                text: '{{ session('warning') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        @if(session('info'))
            Swal.fire({
                icon: 'info',
                title: 'Informasi',
                text: '{{ session('info') }}',
                confirmButtonColor: '#667eea'
            });
        @endif
    </script>
    
    <!-- Page Transitions JavaScript -->
    <script src="{{ asset('js/page-transitions.js') }}"></script>
</html>
