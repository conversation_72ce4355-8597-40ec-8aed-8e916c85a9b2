@extends('layouts.dashboard')

@section('title', 'Export Laporan')

@section('page-title', 'Export Menu')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Export Laporan</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('reports.index') }}">Laporan</a></li>
                            <li class="breadcrumb-item active">Export</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Pengaturan Export</h6>
                </div>
                <div class="card-body">
                    <form id="exportForm">
                        <!-- Export Type -->
                        <div class="form-group">
                            <label class="form-label">Jenis Laporan <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="exportTransactions" name="export_type" value="transactions" class="custom-control-input" checked>
                                        <label class="custom-control-label" for="exportTransactions">
                                            <strong>Data Transaksi</strong>
                                            <br><small class="text-muted">Export semua atau transaksi terfilter</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="exportSummary" name="export_type" value="summary" class="custom-control-input">
                                        <label class="custom-control-label" for="exportSummary">
                                            <strong>Laporan Ringkasan</strong>
                                            <br><small class="text-muted">Export ringkasan per kategori/bulan</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Date Range -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="startDate" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="startDate" name="start_date" 
                                           value="{{ now()->startOfMonth()->format('Y-m-d') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="endDate" class="form-label">Tanggal Selesai <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="endDate" name="end_date" 
                                           value="{{ now()->format('Y-m-d') }}" required>
                                </div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="categoryFilter" class="form-label">Kategori</label>
                                    <select class="form-control" id="categoryFilter" name="category_id">
                                        <option value="">Semua Kategori</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="accountFilter" class="form-label">Akun</label>
                                    <select class="form-control" id="accountFilter" name="account_id">
                                        <option value="">Semua Akun</option>
                                        @foreach($accounts as $account)
                                            <option value="{{ $account->id }}">{{ $account->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Transaction Type -->
                        <div class="form-group">
                            <label class="form-label">Tipe Transaksi</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="typeAll" name="transaction_type" value="all" class="custom-control-input" checked>
                                        <label class="custom-control-label" for="typeAll">Semua</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="typeIncome" name="transaction_type" value="income" class="custom-control-input">
                                        <label class="custom-control-label" for="typeIncome">Pemasukan</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="typeExpense" name="transaction_type" value="expense" class="custom-control-input">
                                        <label class="custom-control-label" for="typeExpense">Pengeluaran</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Export Format -->
                        <div class="form-group">
                            <label class="form-label">Format Export <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="formatExcel" name="export_format" value="excel" class="custom-control-input" checked>
                                        <label class="custom-control-label" for="formatExcel">
                                            <i class="fas fa-file-excel text-success"></i> Excel (.xlsx)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="formatCsv" name="export_format" value="csv" class="custom-control-input">
                                        <label class="custom-control-label" for="formatCsv">
                                            <i class="fas fa-file-csv text-info"></i> CSV (.csv)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="formatPdf" name="export_format" value="pdf" class="custom-control-input">
                                        <label class="custom-control-label" for="formatPdf">
                                            <i class="fas fa-file-pdf text-danger"></i> PDF (.pdf)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Options -->
                        <div class="form-group">
                            <label class="form-label">Opsi Tambahan</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="includeCharts" name="include_charts">
                                        <label class="custom-control-label" for="includeCharts">
                                            Sertakan Grafik (PDF only)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="includeSummary" name="include_summary" checked>
                                        <label class="custom-control-label" for="includeSummary">
                                            Sertakan Ringkasan
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-primary" id="exportBtn">
                                <i class="fas fa-download"></i> Export Laporan
                            </button>
                            <button type="button" class="btn btn-secondary ml-2" id="previewBtn">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Quick Export Options -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Export Cepat</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <button class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" 
                                onclick="quickExport('current_month')">
                            <div>
                                <i class="fas fa-calendar text-primary"></i>
                                <strong class="ml-2">Bulan Ini</strong>
                                <br><small class="text-muted ml-4">Semua transaksi bulan berjalan</small>
                            </div>
                            <i class="fas fa-download"></i>
                        </button>

                        <button class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" 
                                onclick="quickExport('last_month')">
                            <div>
                                <i class="fas fa-calendar-minus text-info"></i>
                                <strong class="ml-2">Bulan Lalu</strong>
                                <br><small class="text-muted ml-4">Semua transaksi bulan sebelumnya</small>
                            </div>
                            <i class="fas fa-download"></i>
                        </button>

                        <button class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" 
                                onclick="quickExport('current_year')">
                            <div>
                                <i class="fas fa-calendar-alt text-warning"></i>
                                <strong class="ml-2">Tahun Ini</strong>
                                <br><small class="text-muted ml-4">Semua transaksi tahun berjalan</small>
                            </div>
                            <i class="fas fa-download"></i>
                        </button>

                        <button class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" 
                                onclick="quickExport('last_7_days')">
                            <div>
                                <i class="fas fa-clock text-success"></i>
                                <strong class="ml-2">7 Hari Terakhir</strong>
                                <br><small class="text-muted ml-4">Transaksi minggu ini</small>
                            </div>
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Export History -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Riwayat Export</h6>
                </div>
                <div class="card-body">
                    <div id="exportHistory">
                        <div class="text-center text-muted">
                            <i class="fas fa-history fa-2x mb-2"></i>
                            <p>Belum ada riwayat export</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Preview Laporan</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="previewContent">
                    <!-- Preview content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="button" class="btn btn-primary" onclick="proceedExport()">
                        <i class="fas fa-download"></i> Lanjutkan Export
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.custom-control-label {
    cursor: pointer;
}

.list-group-item-action:hover {
    background-color: #f8f9fc;
    transform: translateX(5px);
    transition: all 0.3s ease;
}

.export-history-item {
    padding: 10px;
    border-bottom: 1px solid #eaecf4;
    font-size: 0.9em;
}

.export-history-item:last-child {
    border-bottom: none;
}

.export-status {
    font-size: 0.8em;
    padding: 2px 6px;
    border-radius: 3px;
}

.export-status.success {
    background-color: #d4edda;
    color: #155724;
}

.export-status.processing {
    background-color: #fff3cd;
    color: #856404;
}

.export-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

#previewContent {
    max-height: 500px;
    overflow-y: auto;
}

.loading-spinner {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Form validation
    $('#exportForm').on('submit', function(e) {
        e.preventDefault();
        performExport();
    });

    // Preview button
    $('#previewBtn').on('click', function() {
        showPreview();
    });

    // Date validation
    $('#startDate, #endDate').on('change', function() {
        validateDates();
    });

    // Format dependent options
    $('input[name="export_format"]').on('change', function() {
        const format = $(this).val();
        if (format === 'pdf') {
            $('#includeCharts').prop('disabled', false);
        } else {
            $('#includeCharts').prop('disabled', true).prop('checked', false);
        }
    });

    function validateDates() {
        const startDate = new Date($('#startDate').val());
        const endDate = new Date($('#endDate').val());

        if (startDate && endDate && startDate > endDate) {
            alert('Tanggal mulai tidak boleh lebih besar dari tanggal selesai');
            $('#startDate').focus();
            return false;
        }
        return true;
    }

    function performExport() {
        if (!validateDates()) return;

        const formData = new FormData(document.getElementById('exportForm'));
        const exportBtn = $('#exportBtn');
        const originalText = exportBtn.html();

        exportBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

        // Show loading spinner
        showLoadingSpinner();

        $.ajax({
            url: '{{ route("reports.api.export") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhrFields: {
                responseType: 'blob'
            },
            success: function(data, status, xhr) {
                hideLoadingSpinner();
                exportBtn.prop('disabled', false).html(originalText);

                // Get filename from response header
                const disposition = xhr.getResponseHeader('Content-Disposition');
                let filename = 'export.' + $('input[name="export_format"]:checked').val();
                
                if (disposition && disposition.indexOf('attachment') !== -1) {
                    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    const matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    }
                }

                // Create download link
                const url = window.URL.createObjectURL(new Blob([data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', filename);
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);

                // Add to export history
                addToExportHistory(filename, 'success');

                alert('File berhasil didownload!');
            },
            error: function(xhr) {
                hideLoadingSpinner();
                exportBtn.prop('disabled', false).html(originalText);
                
                console.error('Export error:', xhr);
                alert('Terjadi kesalahan saat export file');
                
                // Add to export history
                addToExportHistory('Export gagal', 'failed');
            }
        });
    }

    function showPreview() {
        if (!validateDates()) return;

        const formData = new FormData(document.getElementById('exportForm'));
        formData.append('preview', '1');

        $('#previewContent').html(`
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Memuat preview...</p>
            </div>
        `);

        $('#previewModal').modal('show');

        $.ajax({
            url: '{{ route("reports.api.export") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#previewContent').html(response.preview_html);
            },
            error: function(xhr) {
                $('#previewContent').html(`
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>Gagal memuat preview</p>
                    </div>
                `);
            }
        });
    }

    function showLoadingSpinner() {
        $('body').append(`
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Memproses export...</p>
            </div>
        `);
    }

    function hideLoadingSpinner() {
        $('.loading-spinner').remove();
    }

    function addToExportHistory(filename, status) {
        const now = new Date();
        const historyItem = `
            <div class="export-history-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${filename}</strong>
                        <br><small class="text-muted">${now.toLocaleString('id-ID')}</small>
                    </div>
                    <span class="export-status ${status}">${status === 'success' ? 'Berhasil' : 'Gagal'}</span>
                </div>
            </div>
        `;

        if ($('#exportHistory').find('.export-history-item').length === 0) {
            $('#exportHistory').html(historyItem);
        } else {
            $('#exportHistory').prepend(historyItem);
        }

        // Keep only last 5 items
        $('#exportHistory .export-history-item').slice(5).remove();
    }

    // Global functions
    window.quickExport = function(period) {
        const now = new Date();
        let startDate, endDate;

        switch(period) {
            case 'current_month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                endDate = now;
                break;
            case 'last_month':
                startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                endDate = new Date(now.getFullYear(), now.getMonth(), 0);
                break;
            case 'current_year':
                startDate = new Date(now.getFullYear(), 0, 1);
                endDate = now;
                break;
            case 'last_7_days':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                endDate = now;
                break;
        }

        $('#startDate').val(startDate.toISOString().split('T')[0]);
        $('#endDate').val(endDate.toISOString().split('T')[0]);
        
        // Reset filters
        $('#categoryFilter').val('');
        $('#accountFilter').val('');
        $('input[name="transaction_type"][value="all"]').prop('checked', true);

        performExport();
    };

    window.proceedExport = function() {
        $('#previewModal').modal('hide');
        performExport();
    };

    // Load export history on page load
    loadExportHistory();

    function loadExportHistory() {
        // This would typically load from an API endpoint
        // For now, we'll show a placeholder
        setTimeout(function() {
            $('#exportHistory').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-history fa-2x mb-2"></i>
                    <p>Belum ada riwayat export</p>
                    <small>Export pertama Anda akan muncul di sini</small>
                </div>
            `);
        }, 1000);
    }
});
</script>
@endpush
@endsection
