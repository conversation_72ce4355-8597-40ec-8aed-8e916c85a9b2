@extends('layouts.dashboard')

@section('title', 'Edit Transaksi')

@section('page-title', '✏️ Edit Transaksi')

@push('styles')
<style>
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    .edit-header {
        background: rgba(255, 193, 7, 0.1);
        backdrop-filter: blur(10px);
        color: #856404;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        border: 2px solid rgba(255, 193, 7, 0.3);
    }
    
    /* Synth-wave edit header */
    body[data-theme="synth-wave"] .edit-header {
        background: rgba(26, 26, 26, 0.9) !important;
        border: 1px solid #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.3) !important;
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .edit-header h2 {
        color: #ff00ff !important;
        text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
    }
    
    body[data-theme="synth-wave"] .edit-header p {
        color: #00ffff !important;
    }
    
    .edit-form-card {
        background: #fdf6e3;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    /* Synth-wave form card */
    body[data-theme="synth-wave"] .edit-form-card {
        background: #1a1a1a !important;
        border: 1px solid #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.2) !important;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    /* Synth-wave form labels */
    body[data-theme="synth-wave"] .form-label {
        color: #ff00ff !important;
    }
    
    .form-label i {
        color: #ffc107;
    }
    
    body[data-theme="synth-wave"] .form-label i {
        color: #00ffff !important;
    }
    
    .form-control {
        padding: 15px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        transition: all 0.3s ease;
        background: #faf8f5;
        font-size: 16px;
    }
    
    /* Synth-wave form controls */
    body[data-theme="synth-wave"] .form-control {
        background-color: #1a1a1a !important;
        color: #ffffff !important;
        border-color: #ff00ff !important;
    }
    
    body[data-theme="synth-wave"] .form-control::placeholder {
        color: #00ffff !important;
    }
    
    .form-control:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
        outline: none;
        background: white;
    }
    
    body[data-theme="synth-wave"] .form-control:focus {
        border-color: #00ffff !important;
        box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.25) !important;
        background: #1a1a1a !important;
    }
    
    .btn-group {
        display: flex;
        gap: 15px;
        margin-top: 30px;
    }
    
    .btn {
        padding: 12px 30px;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        color: white;
    }
    
    /* Synth-wave primary button */
    body[data-theme="synth-wave"] .btn-primary {
        background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
        border-color: #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.4) !important;
        color: white !important;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    body[data-theme="synth-wave"] .btn-secondary {
        background: linear-gradient(45deg, #333333, #666666) !important;
        border-color: #ff00ff !important;
        box-shadow: 0 0 15px rgba(255, 0, 255, 0.2) !important;
    }
    
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }
    
    body[data-theme="synth-wave"] .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(255, 0, 255, 0.5) !important;
    }
    
    .type-selector {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin-bottom: 25px;
    }
    
    .type-option {
        position: relative;
    }
    
    .type-option input[type="radio"] {
        position: absolute;
        opacity: 0;
    }
    
    .type-option label {
        display: block;
        padding: 20px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #faf8f5;
    }
    
    /* Synth-wave type options */
    body[data-theme="synth-wave"] .type-option label {
        background: #1a1a1a !important;
        border-color: #ff00ff !important;
        color: #ffffff !important;
    }
    
    .type-option input:checked + label {
        border-color: #ffc107;
        background: rgba(255, 193, 7, 0.1);
        color: #856404;
    }
    
    body[data-theme="synth-wave"] .type-option input:checked + label {
        border-color: #00ffff !important;
        background: rgba(0, 255, 255, 0.1) !important;
        color: #00ffff !important;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
    }
    
    .alert {
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .alert-danger {
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.3);
        color: #721c24;
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <div class="edit-header">
        <h2><i class="fas fa-edit"></i> Edit Transaksi</h2>
        <p>Ubah detail transaksi yang telah ada</p>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle"></i> Terjadi kesalahan validasi:</h6>
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            @if(config('app.debug'))
                <hr>
                <small class="text-muted">
                    <strong>Debug info:</strong><br>
                    Form data: {{ json_encode(old()) }}
                </small>
            @endif
        </div>
    @endif

    <div class="edit-form-card">
        <form action="{{ route('transactions.update', $transaction) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <!-- Transaction Type -->
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-tags"></i> Jenis Transaksi
                </label>
                <div class="type-selector">
                    <div class="type-option">
                        <input type="radio" name="type" value="income" id="income" 
                               {{ old('type', $transaction->type) == 'income' ? 'checked' : '' }}>
                        <label for="income">
                            <i class="fas fa-arrow-up text-success"></i><br>
                            Pemasukan
                        </label>
                    </div>
                    <div class="type-option">
                        <input type="radio" name="type" value="expense" id="expense" 
                               {{ old('type', $transaction->type) == 'expense' ? 'checked' : '' }}>
                        <label for="expense">
                            <i class="fas fa-arrow-down text-danger"></i><br>
                            Pengeluaran
                        </label>
                    </div>
                    <div class="type-option">
                        <input type="radio" name="type" value="transfer" id="transfer" 
                               {{ old('type', $transaction->type) == 'transfer' ? 'checked' : '' }}>
                        <label for="transfer">
                            <i class="fas fa-exchange-alt text-info"></i><br>
                            Transfer
                        </label>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Account -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="account_id" class="form-label">
                            <i class="fas fa-university"></i> Akun
                        </label>
                        <select name="account_id" id="account_id" class="form-control" required>
                            <option value="">Pilih Akun</option>
                            @foreach($accounts as $account)
                                <option value="{{ $account->id }}" 
                                        {{ old('account_id', $transaction->account_id) == $account->id ? 'selected' : '' }}>
                                    {{ $account->name }} - Rp {{ number_format($account->current_balance, 0, ',', '.') }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- To Account (for transfer) -->
                <div class="col-md-6" id="toAccountField" style="display: {{ old('type', $transaction->type) == 'transfer' ? 'block' : 'none' }};">
                    <div class="form-group">
                        <label for="to_account_id" class="form-label">
                            <i class="fas fa-arrow-right"></i> Ke Akun
                        </label>
                        <select name="to_account_id" id="to_account_id" class="form-control">
                            <option value="">Pilih Akun Tujuan</option>
                            @foreach($accounts as $account)
                                <option value="{{ $account->id }}" 
                                        {{ old('to_account_id', $transaction->to_account_id) == $account->id ? 'selected' : '' }}>
                                    {{ $account->name }} - Rp {{ number_format($account->current_balance, 0, ',', '.') }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Category (for income/expense) -->
                <div class="col-md-6" id="categoryField" style="display: {{ old('type', $transaction->type) != 'transfer' ? 'block' : 'none' }};">
                    <div class="form-group">
                        <label for="category_id" class="form-label">
                            <i class="fas fa-folder"></i> Kategori
                        </label>
                        <select name="category_id" id="category_id" class="form-control">
                            <option value="">Pilih Kategori</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" 
                                        data-type="{{ $category->type }}"
                                        {{ old('category_id', $transaction->category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                    @if($category->budget_limit)
                                        (Budget: Rp {{ number_format($category->budget_limit, 0, ',', '.') }})
                                    @endif
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Amount -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="amount" class="form-label">
                            <i class="fas fa-money-bill"></i> Jumlah
                        </label>
                        <div class="amount-input-group" style="position: relative;">
                            <span class="currency-symbol" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: #666; z-index: 2;">Rp</span>
                            <input type="text" name="amount" id="amount" class="form-control" 
                                   value="{{ old('amount', number_format($transaction->amount, 0, ',', '.')) }}" 
                                   placeholder="0" required style="padding-left: 45px;">
                        </div>
                    </div>
                </div>

                <!-- Transaction Date -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="transaction_date" class="form-label">
                            <i class="fas fa-calendar"></i> Tanggal Transaksi
                        </label>
                        <input type="date" name="transaction_date" id="transaction_date" class="form-control" 
                               value="{{ old('transaction_date', $transaction->transaction_date) }}" required>
                    </div>
                </div>
            </div>

            <!-- Title -->
            <div class="form-group">
                <label for="title" class="form-label">
                    <i class="fas fa-file-alt"></i> Judul Transaksi
                </label>
                <input type="text" name="title" id="title" class="form-control" 
                       value="{{ old('title', $transaction->title) }}" placeholder="Masukkan judul transaksi" required>
            </div>

            <!-- Description -->
            <div class="form-group">
                <label for="description" class="form-label">
                    <i class="fas fa-comment"></i> Deskripsi (Opsional)
                </label>
                <textarea name="description" id="description" class="form-control" rows="3" 
                          placeholder="Masukkan deskripsi transaksi">{{ old('description', $transaction->description) }}</textarea>
            </div>

            <div class="row">
                <!-- Payment Method -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="payment_method" class="form-label">
                            <i class="fas fa-credit-card"></i> Metode Pembayaran (Opsional)
                        </label>
                        <select name="payment_method" id="payment_method" class="form-control">
                            <option value="">Pilih Metode</option>
                            <option value="cash" {{ old('payment_method', $transaction->payment_method) == 'cash' ? 'selected' : '' }}>Tunai</option>
                            <option value="debit_card" {{ old('payment_method', $transaction->payment_method) == 'debit_card' ? 'selected' : '' }}>Kartu Debit</option>
                            <option value="credit_card" {{ old('payment_method', $transaction->payment_method) == 'credit_card' ? 'selected' : '' }}>Kartu Kredit</option>
                            <option value="bank_transfer" {{ old('payment_method', $transaction->payment_method) == 'bank_transfer' ? 'selected' : '' }}>Transfer Bank</option>
                            <option value="e_wallet" {{ old('payment_method', $transaction->payment_method) == 'e_wallet' ? 'selected' : '' }}>E-Wallet</option>
                        </select>
                    </div>
                </div>

                <!-- Reference Number -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="reference_number" class="form-label">
                            <i class="fas fa-hashtag"></i> Nomor Referensi (Opsional)
                        </label>
                        <input type="text" name="reference_number" id="reference_number" class="form-control" 
                               value="{{ old('reference_number', $transaction->reference_number) }}" placeholder="No. invoice, receipt, dll">
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="btn-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Transaksi
                </button>
                <a href="{{ route('transactions.index') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Batal
                </a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeRadios = document.querySelectorAll('input[name="type"]');
    const categoryField = document.getElementById('categoryField');
    const toAccountField = document.getElementById('toAccountField');
    
    typeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'transfer') {
                categoryField.style.display = 'none';
                toAccountField.style.display = 'block';
                document.getElementById('category_id').required = false;
                document.getElementById('to_account_id').required = true;
            } else {
                categoryField.style.display = 'block';
                toAccountField.style.display = 'none';
                document.getElementById('category_id').required = true;
                document.getElementById('to_account_id').required = false;
                
                // Filter categories by type
                filterCategoriesByType(this.value);
            }
        });
    });

    // Filter categories based on transaction type
    function filterCategoriesByType(type) {
        const categorySelect = document.getElementById('category_id');
        const options = categorySelect.querySelectorAll('option');
        
        options.forEach(option => {
            if (option.value === '') return; // Keep the default option
            
            const categoryType = option.dataset.type;
            if (categoryType === type) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        });
        
        // Reset selection if current selection is not valid for new type
        const currentSelection = categorySelect.value;
        if (currentSelection) {
            const currentOption = categorySelect.querySelector(`option[value="${currentSelection}"]`);
            if (currentOption && currentOption.dataset.type !== type) {
                categorySelect.value = '';
            }
        }
    }

    // Initialize filter with current transaction type
    const currentType = document.querySelector('input[name="type"]:checked')?.value;
    if (currentType && currentType !== 'transfer') {
        filterCategoriesByType(currentType);
    }

    // Format amount input
    const amountInput = document.getElementById('amount');
    
    // Format existing value on page load
    if (amountInput.value) {
        let cleanValue = amountInput.value.replace(/[^\d]/g, '');
        if (cleanValue) {
            amountInput.value = formatRupiahDisplay(cleanValue);
        }
    }
    
    amountInput.addEventListener('input', function() {
        let value = this.value.replace(/[^\d]/g, '');
        if (value) {
            this.value = formatRupiahDisplay(value);
        } else {
            this.value = '';
        }
    });
    
    // Function to format Rupiah display
    function formatRupiahDisplay(number) {
        return new Intl.NumberFormat('id-ID').format(number);
    }
    
    // Before form submission, convert formatted value back to number
    document.querySelector('form').addEventListener('submit', function(e) {
        console.log('Form submit - Before cleanup:', amountInput.value);
        
        // Remove all non-digit characters from amount
        let cleanAmount = amountInput.value.replace(/[^\d]/g, '');
        amountInput.value = cleanAmount;
        
        console.log('Form submit - After cleanup:', amountInput.value);
        
        // Validate that we have a valid amount
        if (!amountInput.value || amountInput.value === '0') {
            e.preventDefault();
            alert('Jumlah harus diisi dan tidak boleh 0');
            return false;
        }
    });
});
</script>
@endsection