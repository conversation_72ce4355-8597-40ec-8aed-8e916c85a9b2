<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Category;
use App\Models\User;

class CreateSampleCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'categories:sample';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create sample income categories for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = User::first();
        
        if (!$user) {
            $this->error('No user found. Please create a user first.');
            return;
        }

        // Create sample income categories
        $categories = [
            [
                'name' => 'Gaji <PERSON>',
                'icon' => 'fas fa-dollar-sign',
                'color' => '#28a745',
                'description' => 'Pendapatan dari gaji pekerjaan utama',
                'budget_limit' => 5000000,
                'budget_period' => 'monthly',
                'sort_order' => 1
            ],
            [
                'name' => 'Bonus & Tunjangan',
                'icon' => 'fas fa-gift',
                'color' => '#ffc107',
                'description' => 'Bonus kinerja, tunjangan, insentif',
                'sort_order' => 2
            ],
            [
                'name' => 'Freelance',
                'icon' => 'fas fa-laptop',
                'color' => '#17a2b8',
                'description' => 'Pendapatan dari pekerjaan freelance/sampingan',
                'sort_order' => 3
            ],
            [
                'name' => 'Investasi',
                'icon' => 'fas fa-chart-line',
                'color' => '#6f42c1',
                'description' => 'Keuntungan dari investasi saham, reksadana, dll',
                'sort_order' => 4
            ]
        ];

        foreach ($categories as $categoryData) {
            Category::create([
                'user_id' => $user->id,
                'name' => $categoryData['name'],
                'type' => 'income',
                'icon' => $categoryData['icon'],
                'color' => $categoryData['color'],
                'description' => $categoryData['description'],
                'budget_limit' => $categoryData['budget_limit'] ?? null,
                'budget_period' => $categoryData['budget_period'] ?? 'monthly',
                'is_active' => true,
                'sort_order' => $categoryData['sort_order']
            ]);
        }

        $this->info('Sample income categories created successfully!');
    }
}
