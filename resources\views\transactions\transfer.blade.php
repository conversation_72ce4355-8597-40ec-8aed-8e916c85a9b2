@extends('layouts.dashboard')

@section('title', 'Transfer Antar Akun')

@section('page-title', '🔄 Transfer Antar Akun - Pindah Dana')

@push('styles')
<style>
    /* Dashboard container consistency */
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    .transfer-card {
        background: #fdf6e3;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        transition: all 0.3s ease;
        margin: 0 auto;
        max-width: none;
        width: 100%;
    }
    
    .transfer-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
    }
    
    .transfer-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px 20px 0 0;
    }
    
    .transfer-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        animation: grain 20s linear infinite;
        pointer-events: none;
    }
    
    @keyframes grain {
        0%, 100% { transform: translate(0, 0); }
        25% { transform: translate(-10px, -10px); }
        50% { transform: translate(-20px, 0); }
        75% { transform: translate(-10px, 10px); }
    }
    
    .transfer-header h2 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }
    
    .transfer-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .transfer-body {
        padding: 40px;
    }
    
    .account-selector {
        margin-bottom: 40px;
    }
    
    .accounts-container {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 30px;
        align-items: center;
        margin-bottom: 30px;
    }
    
    .account-card {
        background: #f4f0e8;
        border-radius: 15px;
        padding: 25px;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .account-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s ease;
    }
    
    .account-card:hover::before {
        left: 100%;
    }
    
    .account-card.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
    }
    
    .account-card.insufficient {
        border-color: #dc3545;
        background: rgba(220, 53, 69, 0.1);
    }
    
    .account-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
    }
    
    .account-name {
        font-weight: 600;
        font-size: 1.1rem;
        color: #333;
        margin: 0;
    }
    
    .account-type {
        background: #667eea;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .account-balance {
        font-size: 1.5rem;
        font-weight: 700;
        color: #28a745;
        margin: 10px 0;
    }
    
    .account-details {
        font-size: 0.9rem;
        color: #666;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .transfer-arrow {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        animation: pulse 2s infinite;
        position: relative;
        overflow: hidden;
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
        100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
    }
    
    .amount-section {
        background: #f4f0e8;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .amount-label {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }
    
    .amount-input-group {
        position: relative;
        max-width: 400px;
        margin: 0 auto;
    }
    
    .currency-symbol {
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        font-weight: 700;
        font-size: 1.5rem;
        z-index: 2;
    }
    
    .amount-input {
        width: 100%;
        padding: 20px 60px 20px 60px;
        border: 3px solid #e9ecef;
        border-radius: 15px;
        font-size: 2rem;
        font-weight: 700;
        color: #007bff;
        text-align: center;
        background: white;
        transition: all 0.3s ease;
    }
    
    .amount-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 5px rgba(102, 126, 234, 0.1);
        outline: none;
    }
    
    .calc-button {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: #667eea;
        color: white;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 2;
    }
    
    .calc-button:hover {
        background: #5a6fd8;
        transform: translateY(-50%) scale(1.1);
    }
    
    .quick-amounts {
        display: flex;
        gap: 10px;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 20px;
    }
    
    .quick-amount-btn {
        padding: 10px 20px;
        background: #f4f0e8;
        border: 1px solid #d4c5b0;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .quick-amount-btn:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
        transform: translateY(-2px);
    }
    
    .transfer-details {
        background: #f4f0e8;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .form-label i {
        color: #667eea;
    }
    
    .form-control {
        padding: 15px 20px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        font-size: 16px;
        transition: all 0.3s ease;
        background: #faf8f5;
        width: 100%;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
        outline: none;
    }
    
    .transfer-fees {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .fees-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .fees-breakdown {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
    
    .fee-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .fee-item:last-child {
        border-bottom: none;
        font-weight: 700;
        font-size: 1.1rem;
    }
    
    .transfer-summary {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .summary-amount {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }
    
    .summary-details {
        opacity: 0.9;
        margin-bottom: 20px;
    }
    
    .form-buttons {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 15px;
        margin-top: 30px;
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        color: white;
        padding: 18px 40px;
        border-radius: 15px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(108, 117, 125, 0.3);
    }
    
    .btn-transfer {
        background: linear-gradient(135deg, #007bff 0%, #17a2b8 100%);
        border: none;
        color: white;
        padding: 18px 40px;
        border-radius: 15px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        width: 100%;
    }
    
    .btn-transfer::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s ease;
    }
    
    .btn-transfer:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
    }
    
    .btn-transfer:hover::before {
        left: 100%;
    }
    
    .btn-transfer:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
    
    .validation-error {
        color: #dc3545;
        font-size: 14px;
        margin-top: 5px;
        display: none;
    }
    
    .account-search {
        margin-bottom: 20px;
    }
    
    .search-input {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 16px;
        background: white;
    }
    
    .search-input:focus {
        border-color: #667eea;
        outline: none;
    }
    
    /* Theme support */
    body[data-theme="solarized-dark"] .transfer-card {
        background: #fdf6e3;
    }
    
    body[data-theme="solarized-dark"] .account-card {
        background: #f4f0e8;
    }
    
    body[data-theme="solarized-dark"] .amount-section,
    body[data-theme="solarized-dark"] .transfer-details {
        background: #f4f0e8;
    }
    
    body[data-theme="solarized-dark"] .form-control {
        background: #faf8f5;
    }
    
    body[data-theme="solarized-dark"] .form-label {
        color: #586e75;
    }
    
    body[data-theme="solarized-dark"] .form-label i {
        color: #268bd2;
    }
    
    body[data-theme="solarized-dark"] .account-name {
        color: #586e75;
    }
    
    body[data-theme="synth-wave"] .transfer-card {
        background: rgba(0, 0, 0, 0.8);
        border: 1px solid #ff00ff;
    }
    
    body[data-theme="synth-wave"] .account-card {
        background: rgba(255, 0, 255, 0.1);
        border-color: rgba(255, 0, 255, 0.3);
    }
    
    body[data-theme="synth-wave"] .account-card.selected {
        background: rgba(0, 255, 255, 0.2);
        border-color: #00ffff;
    }
    
    body[data-theme="synth-wave"] .amount-section,
    body[data-theme="synth-wave"] .transfer-details {
        background: rgba(255, 0, 255, 0.05);
        border: 1px solid rgba(255, 0, 255, 0.2);
    }
    
    body[data-theme="synth-wave"] .form-control {
        background: rgba(0, 0, 0, 0.6);
        color: #ffffff;
        border-color: #ff00ff;
    }
    
    body[data-theme="synth-wave"] .form-label {
        color: #ffffff;
    }
    
    body[data-theme="synth-wave"] .form-label i {
        color: #ff00ff;
    }
    
    body[data-theme="synth-wave"] .account-name {
        color: #ffffff;
    }
    
    body[data-theme="synth-wave"] .amount-label {
        color: #ffffff;
    }
    
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 15px !important;
        }
        
        .transfer-body {
            padding: 20px;
        }
        
        .accounts-container {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .transfer-arrow {
            width: 40px;
            height: 40px;
            font-size: 1rem;
            transform: rotate(90deg);
        }
        
        .form-buttons {
            grid-template-columns: 1fr;
        }
        
        .quick-amounts {
            justify-content: center;
        }
        
        .fees-breakdown {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <div class="transfer-card">
        <div class="transfer-header">
            <h2><i class="fas fa-exchange-alt"></i> Transfer Antar Akun</h2>
            <p>Pindahkan dana antar akun dengan aman dan mudah</p>
        </div>
        
        <div class="transfer-body">
            <!-- Alert Messages -->
            @if ($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Ada kesalahan:</strong>
                    <ul class="mb-0 mt-2">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <form id="transferForm" action="{{ route('transactions.transfer.process') }}" method="POST">
                @csrf
                <input type="hidden" name="type" value="transfer">
                
                <!-- Account Selection -->
                <div class="account-selector">
                    <h5><i class="fas fa-wallet"></i> Pilih Akun</h5>
                    
                    <!-- Search Accounts -->
                    <div class="account-search">
                        <input type="text" class="search-input" id="accountSearch" 
                               placeholder="Cari akun berdasarkan nama...">
                    </div>
                    
                    <div class="accounts-container">
                        <!-- From Account -->
                        <div>
                            <h6>Dari Akun</h6>
                            <div id="fromAccountsList">
                                @foreach($accounts as $account)
                                    <div class="account-card" data-account-id="{{ $account->id }}" 
                                         data-balance="{{ $account->current_balance }}" 
                                         data-name="{{ $account->name }}"
                                         onclick="selectFromAccount({{ $account->id }})">
                                        <div class="account-header">
                                            <h5 class="account-name">{{ $account->name }}</h5>
                                            <span class="account-type">{{ ucfirst($account->type) }}</span>
                                        </div>
                                        <div class="account-balance">Rp {{ number_format($account->current_balance, 0, ',', '.') }}</div>
                                        <div class="account-details">
                                            <i class="fas fa-university"></i>
                                            <span>{{ $account->bank_name ?? 'Cash' }}</span>
                                            @if($account->account_number)
                                                <span>•••• {{ substr($account->account_number, -4) }}</span>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <input type="hidden" name="account_id" id="fromAccount" required>
                            <div class="validation-error" id="fromAccountError">Pilih akun asal</div>
                        </div>
                        
                        <!-- Transfer Arrow -->
                        <div class="transfer-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        
                        <!-- To Account -->
                        <div>
                            <h6>Ke Akun</h6>
                            <div id="toAccountsList">
                                @foreach($accounts as $account)
                                    <div class="account-card" data-account-id="{{ $account->id }}" 
                                         data-balance="{{ $account->current_balance }}" 
                                         data-name="{{ $account->name }}"
                                         onclick="selectToAccount({{ $account->id }})">
                                        <div class="account-header">
                                            <h5 class="account-name">{{ $account->name }}</h5>
                                            <span class="account-type">{{ ucfirst($account->type) }}</span>
                                        </div>
                                        <div class="account-balance">Rp {{ number_format($account->current_balance, 0, ',', '.') }}</div>
                                        <div class="account-details">
                                            <i class="fas fa-university"></i>
                                            <span>{{ $account->bank_name ?? 'Cash' }}</span>
                                            @if($account->account_number)
                                                <span>•••• {{ substr($account->account_number, -4) }}</span>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <input type="hidden" name="to_account_id" id="toAccount" required>
                            <div class="validation-error" id="toAccountError">Pilih akun tujuan</div>
                        </div>
                    </div>
                </div>
                
                <!-- Amount Section -->
                <div class="amount-section">
                    <div class="amount-label">
                        <i class="fas fa-money-bill-wave"></i>
                        Jumlah Transfer
                    </div>
                    <div class="amount-input-group">
                        <span class="currency-symbol">Rp</span>
                        <input type="text" name="amount" id="transferAmount" class="amount-input" 
                               placeholder="0" required>
                        <button type="button" class="calc-button" onclick="openCalculator()">
                            <i class="fas fa-calculator"></i>
                        </button>
                    </div>
                    <div class="quick-amounts">
                        <button type="button" class="quick-amount-btn" data-amount="100000">100K</button>
                        <button type="button" class="quick-amount-btn" data-amount="500000">500K</button>
                        <button type="button" class="quick-amount-btn" data-amount="1000000">1JT</button>
                        <button type="button" class="quick-amount-btn" data-amount="5000000">5JT</button>
                        <button type="button" class="quick-amount-btn" data-amount="10000000">10JT</button>
                        <button type="button" class="quick-amount-btn" onclick="transferAll()">Semua</button>
                    </div>
                    <div class="validation-error" id="amountError"></div>
                </div>
                
                <!-- Transfer Details -->
                <div class="transfer-details">
                    <h5><i class="fas fa-info-circle"></i> Detail Transfer</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-pen"></i> Keterangan Transfer
                                </label>
                                <input type="text" name="title" id="transferTitle" class="form-control" 
                                       placeholder="Contoh: Transfer ke tabungan" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-calendar"></i> Tanggal Transfer
                                </label>
                                <input type="date" name="transaction_date" id="transferDate" class="form-control" 
                                       value="{{ date('Y-m-d') }}" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-comment"></i> Catatan (Opsional)
                        </label>
                        <textarea name="description" id="transferDescription" class="form-control" rows="3" 
                                  placeholder="Tambahkan catatan atau alasan transfer..."></textarea>
                    </div>
                </div>
                
                <!-- Transfer Fees -->
                <div class="transfer-fees">
                    <div class="fees-header">
                        <i class="fas fa-receipt"></i>
                        Biaya Transfer
                    </div>
                    <div class="fees-breakdown">
                        <div class="fee-item">
                            <span>Biaya Admin:</span>
                            <span id="adminFee">Rp 0</span>
                        </div>
                        <div class="fee-item">
                            <span>Pajak (0%):</span>
                            <span id="taxFee">Rp 0</span>
                        </div>
                        <div class="fee-item">
                            <span>Total Biaya:</span>
                            <span id="totalFees">Rp 0</span>
                        </div>
                        <div class="fee-item">
                            <span>Total Debet:</span>
                            <span id="totalDebit">Rp 0</span>
                        </div>
                    </div>
                </div>
                
                <!-- Transfer Summary -->
                <div class="transfer-summary" id="transferSummary" style="display: none;">
                    <div class="summary-amount" id="summaryAmount">Rp 0</div>
                    <div class="summary-details" id="summaryDetails">
                        Transfer dari <strong id="summaryFromAccount">-</strong> 
                        ke <strong id="summaryToAccount">-</strong>
                    </div>
                    <div class="summary-details">
                        Sisa saldo setelah transfer: <strong id="remainingBalance">Rp 0</strong>
                    </div>
                </div>
                
                <!-- Form Buttons -->
                <div class="form-buttons">
                    <button type="button" class="btn-cancel" onclick="window.history.back()">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn-transfer" id="submitButton" disabled>
                        <i class="fas fa-paper-plane"></i> Proses Transfer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Calculator Popup (reuse from create page) -->
<div class="calculator-popup" id="calculatorPopup" style="display: none;">
    <div class="p-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">Kalkulator</h6>
            <button type="button" class="btn-close" onclick="closeCalculator()"></button>
        </div>
        <input type="text" id="calcDisplay" class="form-control mb-3" readonly>
        <div class="calculator-grid">
            <button class="calc-btn" onclick="clearCalc()">C</button>
            <button class="calc-btn operator" onclick="calcOperation('/')">÷</button>
            <button class="calc-btn operator" onclick="calcOperation('*')">×</button>
            <button class="calc-btn" onclick="backspace()">⌫</button>
            
            <button class="calc-btn" onclick="calcNumber('7')">7</button>
            <button class="calc-btn" onclick="calcNumber('8')">8</button>
            <button class="calc-btn" onclick="calcNumber('9')">9</button>
            <button class="calc-btn operator" onclick="calcOperation('-')">-</button>
            
            <button class="calc-btn" onclick="calcNumber('4')">4</button>
            <button class="calc-btn" onclick="calcNumber('5')">5</button>
            <button class="calc-btn" onclick="calcNumber('6')">6</button>
            <button class="calc-btn operator" onclick="calcOperation('+')">+</button>
            
            <button class="calc-btn" onclick="calcNumber('1')">1</button>
            <button class="calc-btn" onclick="calcNumber('2')">2</button>
            <button class="calc-btn" onclick="calcNumber('3')">3</button>
            <button class="calc-btn operator" onclick="calculateResult()" style="grid-row: span 2">=</button>
            
            <button class="calc-btn" onclick="calcNumber('0')" style="grid-column: span 2">0</button>
            <button class="calc-btn" onclick="calcNumber('.')">.</button>
        </div>
    </div>
</div>

@push('scripts')
<script>
let selectedFromAccount = null;
let selectedToAccount = null;
let accounts = @json($accounts);

document.addEventListener('DOMContentLoaded', function() {
    // Amount formatting
    const amountInput = document.getElementById('transferAmount');
    amountInput.addEventListener('input', function() {
        let value = this.value.replace(/[^\d]/g, '');
        if (value) {
            this.value = new Intl.NumberFormat('id-ID').format(value);
        }
        calculateFees();
        updateSummary();
        validateForm();
    });
    
    // Quick amount buttons
    const quickAmountBtns = document.querySelectorAll('.quick-amount-btn');
    quickAmountBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.textContent === 'Semua') {
                transferAll();
            } else {
                const amount = this.dataset.amount;
                amountInput.value = new Intl.NumberFormat('id-ID').format(amount);
                calculateFees();
                updateSummary();
                validateForm();
            }
        });
    });
    
    // Account search
    const searchInput = document.getElementById('accountSearch');
    searchInput.addEventListener('input', function() {
        filterAccounts(this.value);
    });
    
    // Form validation
    const form = document.getElementById('transferForm');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        if (validateForm()) {
            confirmTransfer();
        }
    });
});

// Account selection functions
function selectFromAccount(accountId) {
    // Clear previous selection
    document.querySelectorAll('#fromAccountsList .account-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Select new account
    const selectedCard = document.querySelector(`#fromAccountsList .account-card[data-account-id="${accountId}"]`);
    selectedCard.classList.add('selected');
    
    selectedFromAccount = accounts.find(acc => acc.id == accountId);
    document.getElementById('fromAccount').value = accountId;
    
    // Update to account list (remove selected from account)
    updateToAccountsList();
    
    calculateFees();
    updateSummary();
    validateForm();
}

function selectToAccount(accountId) {
    // Don't allow selecting same account
    if (selectedFromAccount && selectedFromAccount.id == accountId) {
        Swal.fire({
            icon: 'warning',
            title: 'Akun Sama',
            text: 'Tidak dapat transfer ke akun yang sama!'
        });
        return;
    }
    
    // Clear previous selection
    document.querySelectorAll('#toAccountsList .account-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Select new account
    const selectedCard = document.querySelector(`#toAccountsList .account-card[data-account-id="${accountId}"]`);
    selectedCard.classList.add('selected');
    
    selectedToAccount = accounts.find(acc => acc.id == accountId);
    document.getElementById('toAccount').value = accountId;
    
    calculateFees();
    updateSummary();
    validateForm();
}

function updateToAccountsList() {
    const toAccountsList = document.getElementById('toAccountsList');
    toAccountsList.innerHTML = '';
    
    accounts.forEach(account => {
        if (!selectedFromAccount || account.id != selectedFromAccount.id) {
            const accountCard = createAccountCard(account);
            accountCard.onclick = () => selectToAccount(account.id);
            toAccountsList.appendChild(accountCard);
        }
    });
}

function createAccountCard(account) {
    const div = document.createElement('div');
    div.className = 'account-card';
    div.setAttribute('data-account-id', account.id);
    div.setAttribute('data-balance', account.current_balance);
    div.setAttribute('data-name', account.name);
    
    div.innerHTML = `
        <div class="account-header">
            <h5 class="account-name">${account.name}</h5>
            <span class="account-type">${account.type.charAt(0).toUpperCase() + account.type.slice(1)}</span>
        </div>
        <div class="account-balance">Rp ${new Intl.NumberFormat('id-ID').format(account.current_balance)}</div>
        <div class="account-details">
            <i class="fas fa-university"></i>
            <span>${account.bank_name || 'Cash'}</span>
            ${account.account_number ? `<span>•••• ${account.account_number.slice(-4)}</span>` : ''}
        </div>
    `;
    
    return div;
}

function filterAccounts(searchTerm) {
    const fromCards = document.querySelectorAll('#fromAccountsList .account-card');
    const toCards = document.querySelectorAll('#toAccountsList .account-card');
    
    [...fromCards, ...toCards].forEach(card => {
        const accountName = card.getAttribute('data-name').toLowerCase();
        if (accountName.includes(searchTerm.toLowerCase())) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function transferAll() {
    if (!selectedFromAccount) {
        Swal.fire({
            icon: 'warning',
            title: 'Pilih Akun',
            text: 'Pilih akun asal terlebih dahulu!'
        });
        return;
    }
    
    const amountInput = document.getElementById('transferAmount');
    amountInput.value = new Intl.NumberFormat('id-ID').format(selectedFromAccount.current_balance);
    calculateFees();
    updateSummary();
    validateForm();
}

function calculateFees() {
    const amountStr = document.getElementById('transferAmount').value.replace(/[^\d]/g, '');
    const amount = parseInt(amountStr) || 0;
    
    // Calculate fees (you can customize these rates)
    const adminFee = 0; // Free internal transfer
    const taxFee = 0;   // No tax for internal transfer
    const totalFees = adminFee + taxFee;
    const totalDebit = amount + totalFees;
    
    document.getElementById('adminFee').textContent = `Rp ${new Intl.NumberFormat('id-ID').format(adminFee)}`;
    document.getElementById('taxFee').textContent = `Rp ${new Intl.NumberFormat('id-ID').format(taxFee)}`;
    document.getElementById('totalFees').textContent = `Rp ${new Intl.NumberFormat('id-ID').format(totalFees)}`;
    document.getElementById('totalDebit').textContent = `Rp ${new Intl.NumberFormat('id-ID').format(totalDebit)}`;
    
    return { amount, adminFee, taxFee, totalFees, totalDebit };
}

function updateSummary() {
    const { amount, totalDebit } = calculateFees();
    
    if (amount > 0 && selectedFromAccount && selectedToAccount) {
        document.getElementById('transferSummary').style.display = 'block';
        document.getElementById('summaryAmount').textContent = `Rp ${new Intl.NumberFormat('id-ID').format(amount)}`;
        document.getElementById('summaryFromAccount').textContent = selectedFromAccount.name;
        document.getElementById('summaryToAccount').textContent = selectedToAccount.name;
        
        const remainingBalance = selectedFromAccount.current_balance - totalDebit;
        document.getElementById('remainingBalance').textContent = `Rp ${new Intl.NumberFormat('id-ID').format(remainingBalance)}`;
        
        // Check if sufficient balance
        const fromAccountCard = document.querySelector(`#fromAccountsList .account-card[data-account-id="${selectedFromAccount.id}"]`);
        if (remainingBalance < 0) {
            fromAccountCard.classList.add('insufficient');
        } else {
            fromAccountCard.classList.remove('insufficient');
        }
    } else {
        document.getElementById('transferSummary').style.display = 'none';
    }
}

function validateForm() {
    let isValid = true;
    
    // Clear previous errors
    document.querySelectorAll('.validation-error').forEach(error => {
        error.style.display = 'none';
    });
    
    // Validate from account
    if (!selectedFromAccount) {
        document.getElementById('fromAccountError').style.display = 'block';
        isValid = false;
    }
    
    // Validate to account
    if (!selectedToAccount) {
        document.getElementById('toAccountError').style.display = 'block';
        isValid = false;
    }
    
    // Validate amount
    const amountStr = document.getElementById('transferAmount').value.replace(/[^\d]/g, '');
    const amount = parseInt(amountStr) || 0;
    
    if (amount <= 0) {
        document.getElementById('amountError').textContent = 'Jumlah transfer harus lebih dari 0';
        document.getElementById('amountError').style.display = 'block';
        isValid = false;
    } else if (selectedFromAccount && amount > selectedFromAccount.current_balance) {
        document.getElementById('amountError').textContent = 'Saldo tidak mencukupi';
        document.getElementById('amountError').style.display = 'block';
        isValid = false;
    }
    
    // Validate title
    const title = document.getElementById('transferTitle').value.trim();
    if (!title) {
        isValid = false;
    }
    
    // Enable/disable submit button
    document.getElementById('submitButton').disabled = !isValid;
    
    return isValid;
}

function confirmTransfer() {
    const { amount, totalDebit } = calculateFees();
    
    Swal.fire({
        title: 'Konfirmasi Transfer',
        html: `
            <div style="text-align: left;">
                <p><strong>Dari:</strong> ${selectedFromAccount.name}</p>
                <p><strong>Ke:</strong> ${selectedToAccount.name}</p>
                <p><strong>Jumlah:</strong> Rp ${new Intl.NumberFormat('id-ID').format(amount)}</p>
                <p><strong>Total Debet:</strong> Rp ${new Intl.NumberFormat('id-ID').format(totalDebit)}</p>
                <p><strong>Keterangan:</strong> ${document.getElementById('transferTitle').value}</p>
            </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Transfer!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Convert amount back to number format for submission
            const cleanAmount = document.getElementById('transferAmount').value.replace(/[^\d]/g, '');
            document.getElementById('transferAmount').value = cleanAmount;
            
            // Submit form
            document.getElementById('transferForm').submit();
        }
    });
}

// Calculator functions (reuse from create page)
let calcDisplay = '';
let operator = '';
let firstOperand = '';
let waitingForNewOperand = false;

function openCalculator() {
    document.getElementById('calculatorPopup').style.display = 'block';
    document.getElementById('calcDisplay').value = '0';
}

function closeCalculator() {
    document.getElementById('calculatorPopup').style.display = 'none';
    
    const result = document.getElementById('calcDisplay').value;
    if (result && result !== '0') {
        document.getElementById('transferAmount').value = new Intl.NumberFormat('id-ID').format(result.replace(/[^\d]/g, ''));
        calculateFees();
        updateSummary();
        validateForm();
    }
}

function calcNumber(num) {
    const display = document.getElementById('calcDisplay');
    
    if (waitingForNewOperand) {
        display.value = num;
        waitingForNewOperand = false;
    } else {
        display.value = display.value === '0' ? num : display.value + num;
    }
}

function calcOperation(nextOperator) {
    const display = document.getElementById('calcDisplay');
    const inputValue = parseFloat(display.value);

    if (firstOperand === '') {
        firstOperand = inputValue;
    } else if (operator) {
        const currentValue = firstOperand || 0;
        const newValue = calculate(currentValue, inputValue, operator);

        display.value = String(newValue);
        firstOperand = newValue;
    }

    waitingForNewOperand = true;
    operator = nextOperator;
}

function calculateResult() {
    const display = document.getElementById('calcDisplay');
    const inputValue = parseFloat(display.value);

    if (firstOperand !== '' && operator) {
        const newValue = calculate(firstOperand, inputValue, operator);
        display.value = String(newValue);
        firstOperand = '';
        operator = '';
        waitingForNewOperand = true;
    }
}

function calculate(firstOperand, secondOperand, operator) {
    switch (operator) {
        case '+':
            return firstOperand + secondOperand;
        case '-':
            return firstOperand - secondOperand;
        case '*':
            return firstOperand * secondOperand;
        case '/':
            return firstOperand / secondOperand;
        default:
            return secondOperand;
    }
}

function clearCalc() {
    document.getElementById('calcDisplay').value = '0';
    firstOperand = '';
    operator = '';
    waitingForNewOperand = false;
}

function backspace() {
    const display = document.getElementById('calcDisplay');
    display.value = display.value.slice(0, -1) || '0';
}
</script>
@endpush
@endsection
