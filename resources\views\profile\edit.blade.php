@extends('layouts.dashboard')

@section('title', 'Edit Profil')

@section('page-title', '✏️ Edit Profil - Perbarui Informasi Anda')

@push('styles')
<style>
    /* Base Variables */
    :root {
        --primary-color: #667eea;
        --secondary-color: #764ba2;
        --text-color: #333;
        --text-secondary: #6c757d;
        --bg-color: #ffffff;
        --border-color: #e1e8ed;
        --shadow-color: rgba(0, 0, 0, 0.1);
    }

    /* Theme Variables */
    body[data-theme="light"] {
        --text-color: #333333;
        --text-secondary: #6c757d;
        --bg-color: #ffffff;
        --border-color: #e1e8ed;
        --shadow-color: rgba(0, 0, 0, 0.1);
        --card-bg: #ffffff;
        --input-bg: #ffffff;
        --input-text: #333333;
        --form-text-color: #6c757d;
    }

    body[data-theme="solarized-dark"] {
        --text-color: #586e75;
        --text-secondary: #657b83;
        --bg-color: #fdf6e3;
        --border-color: #eee8d5;
        --shadow-color: rgba(88, 110, 117, 0.1);
        --card-bg: #fdf6e3;
        --input-bg: #fdf6e3;
        --input-text: #586e75;
        --form-text-color: #657b83;
        --primary-color: #268bd2;
        --secondary-color: #2aa198;
    }

    body[data-theme="synth-wave"] {
        --text-color: #ffffff;
        --text-secondary: #00ffff;
        --bg-color: #1a1a1a;
        --border-color: #ff00ff;
        --shadow-color: rgba(255, 0, 255, 0.2);
        --card-bg: #1a1a1a;
        --input-bg: #1a1a1a;
        --input-text: #ffffff;
        --form-text-color: #00ffff;
        --primary-color: #ff00ff;
        --secondary-color: #00ffff;
    }

    body[data-theme="dark"] {
        --text-color: #e9ecef;
        --text-secondary: #adb5bd;
        --bg-color: #2d3748;
        --border-color: #4a5568;
        --shadow-color: rgba(0, 0, 0, 0.3);
        --card-bg: #2d3748;
        --input-bg: #1a202c;
        --input-text: #e9ecef;
        --form-text-color: #adb5bd;
        --primary-color: #667eea;
        --secondary-color: #764ba2;
    }

    .profile-edit-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .profile-card {
        background: var(--card-bg);
        border-radius: 15px;
        box-shadow: 0 5px 20px var(--shadow-color);
        overflow: hidden;
        margin-bottom: 30px;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
    }

    body[data-theme="synth-wave"] .profile-card {
        border: 1px solid var(--primary-color);
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.2);
    }

    .profile-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px var(--shadow-color);
    }

    body[data-theme="synth-wave"] .profile-card:hover {
        box-shadow: 0 0 30px rgba(255, 0, 255, 0.4);
    }

    .profile-card-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 20px 30px;
        border-bottom: none;
    }

    body[data-theme="synth-wave"] .profile-card-header {
        text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
    }

    .profile-card-header h3 {
        margin: 0;
        font-size: 1.4rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .profile-card-body {
        padding: 30px;
        background: var(--card-bg);
        color: var(--text-color);
    }

    .avatar-upload-section {
        text-align: center;
        margin-bottom: 30px;
    }

    .avatar-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 4px solid var(--primary-color);
        object-fit: cover;
        margin: 0 auto 20px;
        display: block;
        transition: all 0.3s ease;
    }

    body[data-theme="synth-wave"] .avatar-preview {
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
    }

    .avatar-preview:hover {
        transform: scale(1.05);
        box-shadow: 0 10px 30px var(--shadow-color);
    }

    body[data-theme="synth-wave"] .avatar-preview:hover {
        box-shadow: 0 0 30px rgba(255, 0, 255, 0.5);
    }

    .avatar-upload-btn {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    body[data-theme="synth-wave"] .avatar-upload-btn {
        box-shadow: 0 0 15px rgba(255, 0, 255, 0.3);
    }

    .avatar-upload-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px var(--shadow-color);
    }

    body[data-theme="synth-wave"] .avatar-upload-btn:hover {
        box-shadow: 0 0 25px rgba(255, 0, 255, 0.5);
    }

    .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        flex: 1;
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: var(--text-color);
        font-size: 0.95rem;
    }

    body[data-theme="synth-wave"] .form-group label {
        text-shadow: 0 0 5px rgba(255, 0, 255, 0.3);
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid var(--border-color);
        border-radius: 10px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: var(--input-bg);
        color: var(--input-text);
    }

    body[data-theme="synth-wave"] .form-control {
        border-color: var(--primary-color);
        box-shadow: 0 0 10px rgba(255, 0, 255, 0.1);
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-color), 0.1);
        transform: translateY(-1px);
    }

    body[data-theme="synth-wave"] .form-control:focus {
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
    }

    .form-control:hover {
        border-color: var(--primary-color);
        transform: translateY(-1px);
    }

    body[data-theme="synth-wave"] .form-control:hover {
        box-shadow: 0 0 15px rgba(255, 0, 255, 0.2);
    }

    .form-control::placeholder {
        color: var(--text-secondary);
        opacity: 0.7;
    }

    .form-select {
        appearance: none;
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px 12px;
        padding-right: 40px;
    }

    body[data-theme="solarized-dark"] .form-select {
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23586e75' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
    }

    body[data-theme="synth-wave"] .form-select {
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23ff00ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
    }

    body[data-theme="dark"] .form-select {
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23e9ecef' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
    }

    .form-text {
        font-size: 0.85rem;
        color: var(--form-text-color);
        margin-top: 5px;
    }

    body[data-theme="synth-wave"] .form-text {
        text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
    }

    .text-success {
        color: #28a745 !important;
    }

    body[data-theme="solarized-dark"] .text-success {
        color: #859900 !important;
    }

    body[data-theme="synth-wave"] .text-success {
        color: #00ff00 !important;
        text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
    }

    .text-warning {
        color: #ffc107 !important;
    }

    body[data-theme="solarized-dark"] .text-warning {
        color: #b58900 !important;
    }

    body[data-theme="synth-wave"] .text-warning {
        color: #ffff00 !important;
        text-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
    }

    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 5px;
        font-size: 0.875rem;
        color: #dc3545;
    }

    body[data-theme="solarized-dark"] .invalid-feedback {
        color: #dc322f;
    }

    body[data-theme="synth-wave"] .invalid-feedback {
        color: #ff0040;
        text-shadow: 0 0 10px rgba(255, 0, 64, 0.5);
    }

    .is-invalid {
        border-color: #dc3545;
    }

    body[data-theme="solarized-dark"] .is-invalid {
        border-color: #dc322f;
    }

    body[data-theme="synth-wave"] .is-invalid {
        border-color: #ff0040;
        box-shadow: 0 0 15px rgba(255, 0, 64, 0.3);
    }

    .security-info {
        background: var(--bg-color);
        border: 1px solid var(--border-color);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }

    body[data-theme="synth-wave"] .security-info {
        border-color: var(--primary-color);
        box-shadow: 0 0 15px rgba(255, 0, 255, 0.1);
    }

    .security-info h5 {
        margin-bottom: 10px;
        color: var(--text-color);
        font-size: 1rem;
    }

    .security-status {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
        color: var(--text-color);
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    body[data-theme="solarized-dark"] .status-active {
        background: rgba(133, 153, 0, 0.2);
        color: #859900;
    }

    body[data-theme="synth-wave"] .status-active {
        background: rgba(0, 255, 0, 0.2);
        color: #00ff00;
        text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    body[data-theme="solarized-dark"] .status-inactive {
        background: rgba(220, 50, 47, 0.2);
        color: #dc322f;
    }

    body[data-theme="synth-wave"] .status-inactive {
        background: rgba(255, 0, 64, 0.2);
        color: #ff0040;
        text-shadow: 0 0 5px rgba(255, 0, 64, 0.5);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: white;
    }

    body[data-theme="synth-wave"] .btn-primary {
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px var(--shadow-color);
        color: white;
    }

    body[data-theme="synth-wave"] .btn-primary:hover {
        box-shadow: 0 0 30px rgba(255, 0, 255, 0.5);
    }

    .btn-primary:active {
        transform: translateY(0);
    }

    .btn-primary.loading {
        position: relative;
        color: transparent;
    }

    .btn-primary.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #fff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .btn-secondary {
        background: var(--text-secondary);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        color: white;
    }

    body[data-theme="synth-wave"] .btn-secondary {
        background: #666666;
        border: 1px solid var(--secondary-color);
        color: var(--secondary-color);
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
    }

    .btn-secondary:hover {
        background: var(--text-color);
        transform: translateY(-2px);
        color: white;
    }

    body[data-theme="synth-wave"] .btn-secondary:hover {
        background: var(--secondary-color);
        color: #000000;
        box-shadow: 0 0 25px rgba(0, 255, 255, 0.4);
    }

    .form-actions {
        text-align: center;
        padding-top: 20px;
        border-top: 1px solid var(--border-color);
        margin-top: 30px;
    }

    .form-actions .btn {
        margin: 0 10px;
        min-width: 120px;
    }

    .verification-badge {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 10px;
    }

    .verification-badge.verified {
        background: #d4edda;
        color: #155724;
    }

    body[data-theme="solarized-dark"] .verification-badge.verified {
        background: rgba(133, 153, 0, 0.2);
        color: #859900;
    }

    body[data-theme="synth-wave"] .verification-badge.verified {
        background: rgba(0, 255, 0, 0.2);
        color: #00ff00;
        text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
    }

    .verification-badge.unverified {
        background: #fff3cd;
        color: #856404;
    }

    body[data-theme="solarized-dark"] .verification-badge.unverified {
        background: rgba(181, 137, 0, 0.2);
        color: #b58900;
    }

    body[data-theme="synth-wave"] .verification-badge.unverified {
        background: rgba(255, 255, 0, 0.2);
        color: #ffff00;
        text-shadow: 0 0 5px rgba(255, 255, 0, 0.5);
    }

    .full-width {
        width: 100%;
    }

    .form-group textarea {
        resize: vertical;
        min-height: 80px;
        background: var(--input-bg);
        color: var(--input-text);
    }

    .form-group input[type="checkbox"] {
        width: auto;
        margin-right: 8px;
        transform: scale(1.2);
        accent-color: var(--primary-color);
    }

    .form-group label:has(input[type="checkbox"]) {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-weight: 500;
        color: var(--text-color);
    }

    .text-right {
        text-align: right;
    }

    .btn-group {
        display: flex;
        gap: 10px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .profile-card-body .alert {
        margin-bottom: 20px;
        border: none;
        border-radius: 10px;
        padding: 15px 20px;
        font-weight: 500;
    }

    .alert-success {
        background: #d4edda;
        color: #155724;
        border-left: 4px solid #28a745;
    }

    .alert-danger {
        background: #f8d7da;
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .alert-info {
        background: #d1ecf1;
        color: #0c5460;
        border-left: 4px solid #17a2b8;
    }

    .alert ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .alert ul li {
        margin-bottom: 5px;
    }

    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
            gap: 0;
        }
        
        .profile-card-body {
            padding: 20px;
        }
        
        .profile-edit-container {
            padding: 10px;
        }

        .form-actions {
            text-align: center;
        }

        .btn-group {
            flex-direction: column;
        }

        .btn-group .btn {
            width: 100%;
            margin: 5px 0;
        }
    }
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
      <style>        /* Ensure FontAwesome icons are loaded properly */
        .fas, .far, .fab {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro" !important;
            font-weight: 900;
        }
        
        .far {
            font-weight: 400;
        }
        
        /* Force icon visibility with fallback */
        .menu-icon.fas::before,
        .submenu-icon.fas::before,
        .menu-arrow.fas::before {
            display: inline-block !important;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
        }
        
        /* Fallback untuk icon yang tidak ter-load */
        .menu-icon {
            position: relative;
        }
        
        .menu-icon.fa-home::before { content: "🏠"; }
        .menu-icon.fa-exchange-alt::before { content: "💱"; }
        .menu-icon.fa-tags::before { content: "🏷️"; }
        .menu-icon.fa-wallet::before { content: "👛"; }
        .menu-icon.fa-clipboard-list::before { content: "📋"; }
        .menu-icon.fa-chart-bar::before { content: "📊"; }
        .menu-icon.fa-cog::before { content: "⚙️"; }
        
        :root {
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --primary-color: #667eea;
            --secondary-color: #764ba2;
        }body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Figtree', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .app-container {
            display: flex;
            min-height: 100vh;
        }
          /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
            overflow-x: hidden;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
          .sidebar-subtitle {
            font-size: 0.8rem;
            color: #666;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .sidebar-menu {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        
        /* Styling khusus untuk sidebar collapsed */
        .sidebar.collapsed .sidebar-menu {
            padding: 10px 0;
        }
        
        .sidebar.collapsed .menu-item {
            margin: 6px 0;
        }
        
        .sidebar.collapsed .menu-item:first-child {
            margin-top: 20px;
        }
        
        .menu-item {
            position: relative;
        }
          .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border-radius: 8px;
            margin: 2px 16px;
        }
          .menu-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            text-decoration: none;
            transform: translateX(2px);
        }
        
        .menu-link.active {
            background: var(--primary-color);
            color: white;
        }        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
            font-size: 1rem;
            color: inherit;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: margin-right 0.3s ease, font-size 0.3s ease;
        }
          .menu-text {
            flex: 1;
            font-weight: 500;
            transition: opacity 0.3s ease, visibility 0.3s ease, width 0.3s ease;
        }
        
        .menu-arrow {
            font-size: 0.8rem;
            transition: transform 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .menu-arrow.rotated {
            transform: rotate(90deg);
        }
        
        .submenu {
            list-style: none;
            margin: 0;
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.05);
        }
        
        .submenu.open {
            max-height: 300px;
        }
        
        .submenu-item {
            padding: 8px 20px 8px 52px;
        }
        
        .submenu-link {
            display: flex;
            align-items: center;
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .submenu-link:hover {
            color: var(--primary-color);
            text-decoration: none;
        }
          .submenu-icon {
            width: 16px;
            margin-right: 8px;
            text-align: center;
            font-size: 0.9rem;
            color: inherit;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
          /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            flex: 1;
            min-height: 100vh;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .topbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            height: var(--topbar-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .topbar-left h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }
        
        .topbar-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .user-details h4 {
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .user-details p {
            margin: 0;
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .dashboard-container {
            padding: 30px;
            min-height: calc(100vh - var(--topbar-height));
        }
          
        .dashboard-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            color: white;
        }
        
        .welcome-section h2 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .welcome-section p {
            opacity: 0.9;
            margin: 0;
        }
        
        .btn-logout {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .dashboard-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .card-subtitle {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .card-content {
            color: #555;
            line-height: 1.6;
        }
        
        .finance-icon {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .security-icon {
            background: linear-gradient(45deg, #11998e, #38ef7d);
            color: white;
        }
        
        .stats-icon {
            background: linear-gradient(45deg, #fa709a, #fee140);
            color: white;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0 0 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .quick-actions h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
            display: block;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }        /* Theme Switcher in Topbar */
        .theme-switcher {
            position: relative;
        }
        
        .theme-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
            z-index: 9999; /* Fix z-index issue */
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
        
        /* User Info Dropdown */
        .user-info-dropdown {
            position: relative;
        }
        
        .user-info-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .user-info-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
            z-index: 9999;
        }
        
        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .user-dropdown-item:hover {
            background: #f0f0f0;
        }
        
        .user-dropdown-icon {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }          /* Sidebar Collapse Button */
        .sidebar-collapse-btn {
            position: fixed;
            top: 20px;
            left: calc(var(--sidebar-width) - 15px);
            width: 30px;
            height: 30px;
            background: var(--primary-color);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
        }
        
        .sidebar-collapse-btn:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        /* Tombol saat sidebar collapsed - posisi berubah ke kanan */
        .sidebar.collapsed + .sidebar-collapse-btn {
            left: calc(80px - 15px);
        }.sidebar.collapsed {
            width: 80px;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
          .sidebar.collapsed .sidebar-header {
            padding: 12px 3px;
            text-align: center;
        }
        
        .sidebar.collapsed .sidebar-logo {
            font-size: 1.4rem;
            margin-bottom: 0;
        }
        
        .sidebar.collapsed .sidebar-subtitle {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        
        .sidebar.collapsed .menu-text {
            opacity: 0;
            visibility: hidden;
            width: 0;
            transition: opacity 0.2s ease, visibility 0.2s ease, width 0.2s ease;
        }
        
        .sidebar.collapsed .menu-arrow {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        
        .sidebar.collapsed .submenu {
            display: none;
        }        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 8px 4px;
            margin: 4px 6px;
            border-radius: 8px;
            position: relative;
            min-height: 36px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }          .sidebar.collapsed .menu-link:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.12);
            border-color: rgba(102, 126, 234, 0.15);
        }
          .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            border-color: transparent;        }.sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 1.1rem;
            color: inherit;
            width: auto;
            min-width: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
          /* Tooltip for collapsed sidebar */
        .sidebar.collapsed .menu-link::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 15px;
            z-index: 10000;
            pointer-events: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .sidebar.collapsed .menu-link:hover::after {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) translateX(5px);
        }        .main-content.sidebar-collapsed {
            margin-left: 70px;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
          /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-collapse-btn {
                left: 15px;
                top: 15px;
            }
            
            .sidebar.collapsed + .sidebar-collapse-btn {
                left: 15px;
            }
            
            .topbar {
                padding: 0 15px;
            }
            
            .dashboard-container {
                padding: 15px;
            }
            
            .dashboard-content {
                grid-template-columns: 1fr;
            }
              .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .user-info-toggle .user-details h4 {
                display: none;
            }
            
            .user-info-toggle .user-details p {
                display: none;
            }
            
            .user-dropdown {
                min-width: 150px;
            }
        }
        
        .theme-icon {
            font-size: 1rem;
        }
        
        .dropdown-arrow {
            font-size: 0.7rem;
            transition: transform 0.3s ease;
        }
        
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
          /* Responsive */
        @media (max-width: 768px) {
            .welcome-section h2 {
                font-size: 1.3rem;
            }
        }
          /* Theme Variations for Sidebar */
        /* Solarized Dark Theme */
        body[data-theme="solarized-dark"] {
            background: linear-gradient(135deg, #002b36 0%, #073642 100%);
        }
        
        body[data-theme="solarized-dark"] .sidebar {
            background: rgba(253, 246, 227, 0.95);
        }
        
        body[data-theme="solarized-dark"] .sidebar-logo {
            color: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .menu-link {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .menu-link:hover {
            background: rgba(38, 139, 210, 0.1);
            color: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .menu-link.active {
            background: #268bd2;
            color: white;
        }
        
        body[data-theme="solarized-dark"] .sidebar-collapse-btn {
            background: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .sidebar-collapse-btn:hover {
            background: #2aa198;
        }
        
        body[data-theme="solarized-dark"] .dashboard-card {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .quick-actions {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .card-title {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .card-subtitle {
            color: #657b83;
        }
        
        body[data-theme="solarized-dark"] .card-content {
            color: #839496;
        }
        
        body[data-theme="solarized-dark"] .action-btn {
            background: linear-gradient(45deg, #268bd2, #2aa198);
        }
        
        /* Synth Wave Theme */
        body[data-theme="synth-wave"] {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a0033 50%, #000000 100%);
        }
        
        body[data-theme="synth-wave"] .sidebar {
            background: rgba(26, 26, 26, 0.95);
            border-right: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .sidebar-logo {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .sidebar-subtitle {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .menu-link {
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .menu-link:hover {
            background: rgba(255, 0, 255, 0.1);
            color: #ff00ff;
        }
        
        body[data-theme="synth-wave"] .menu-link.active {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            color: white;
        }
        
        body[data-theme="synth-wave"] .sidebar-collapse-btn {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
        }
        
        body[data-theme="synth-wave"] .dashboard-card {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .quick-actions {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .card-title {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .card-subtitle {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .card-content {
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .action-btn {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.4);
        }
          body[data-theme="synth-wave"] .quick-actions h3 {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        /* Theme-specific styles for user dropdown */
        body[data-theme="solarized-dark"] .user-dropdown {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .user-dropdown-item {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .user-dropdown-item:hover {
            background: rgba(38, 139, 210, 0.1);
        }
        
        body[data-theme="synth-wave"] .user-dropdown {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .user-dropdown-item {
            color: #ffffff;
        }
          body[data-theme="synth-wave"] .user-dropdown-item:hover {
            background: rgba(255, 0, 255, 0.1);
        }
          /* Tooltip styles for different themes */
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link::after {
            background: linear-gradient(135deg, #268bd2, #2aa198);
            color: #fdf6e3;
            box-shadow: 0 4px 12px rgba(38, 139, 210, 0.3);
        }
        
        body[data-theme="synth-wave"] .sidebar.collapsed .menu-link::after {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            color: #ffffff;
            border: 1px solid rgba(255, 0, 255, 0.5);
            box-shadow: 0 4px 12px rgba(255, 0, 255, 0.4);
        }
          /* Theme-specific styling for collapsed sidebar */
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link {
            background: rgba(253, 246, 227, 0.9);
            border-color: rgba(38, 139, 210, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }          body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link:hover {
            background: rgba(38, 139, 210, 0.1);
            border-color: rgba(38, 139, 210, 0.25);
            box-shadow: 0 3px 10px rgba(38, 139, 210, 0.12);
        }
        
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, #268bd2, #2aa198);
        }
          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(255, 0, 255, 0.2);
            box-shadow: 0 2px 8px rgba(255, 0, 255, 0.08);
        }          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link:hover {
            background: rgba(255, 0, 255, 0.1);
            border-color: rgba(255, 0, 255, 0.3);
            box-shadow: 0 3px 10px rgba(255, 0, 255, 0.15);
        }
          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            box-shadow: 0 4px 15px rgba(255, 0, 255, 0.4);
        }
    </style>
</style>
@endpush

@section('content')
<div class="profile-edit-container">

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            {{ session('error') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Terjadi kesalahan:</strong>
            <ul style="margin: 10px 0 0 20px;">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data" id="profileEditForm">
        @csrf
        @method('PATCH')
        
        <!-- Avatar & Basic Info Card -->
        <div class="profile-card">
            <div class="profile-card-header">
                <h3>
                    <i class="fas fa-user-circle"></i>
                    Foto Profil & Informasi Dasar
                </h3>
            </div>
            <div class="profile-card-body">
                
                <!-- Avatar Upload Section -->
                <div class="avatar-upload-section">
                    <img src="{{ Auth::user()->avatar ? Storage::url(Auth::user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(Auth::user()->name) . '&background=667eea&color=ffffff&size=120' }}" 
                         alt="Avatar" class="avatar-preview" id="avatarPreview">
                    
                    <input type="file" name="avatar" id="avatarInput" accept="image/*" style="display: none;">
                    <label for="avatarInput" class="avatar-upload-btn">
                        <i class="fas fa-camera"></i> Ubah Foto
                    </label>
                    <div class="form-text">Format yang didukung: JPG, PNG, GIF. Maksimal 2MB.</div>
                    @error('avatar')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Basic Information -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Nama Lengkap *</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name', Auth::user()->name) }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="username">Username *</label>
                        <input type="text" class="form-control @error('username') is-invalid @enderror" 
                               id="username" name="username" value="{{ old('username', Auth::user()->username) }}" required>
                        <div class="form-text">Username harus unik dan tidak dapat diubah setelah disimpan</div>
                        @error('username')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" class="form-control @error('email') is-invalid @enderror" 
                               id="email" name="email" value="{{ old('email', Auth::user()->email) }}" required>
                        @if(Auth::user()->email_verified_at)
                            <div class="form-text text-success">
                                <i class="fas fa-check-circle"></i> Email sudah terverifikasi
                            </div>
                        @else
                            <div class="form-text text-warning">
                                <i class="fas fa-exclamation-triangle"></i> Email belum terverifikasi
                            </div>
                        @endif
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Nomor Telepon</label>
                        <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                               id="phone" name="phone" value="{{ old('phone', Auth::user()->phone) }}"
                               placeholder="Contoh: 0812-3456-7890">
                        <div class="form-text">Format: 0812-3456-7890 atau +62812-3456-7890</div>
                        @error('phone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
            </div>
        </div>

        <!-- Personal Details Card -->
        <div class="profile-card">
            <div class="profile-card-header">
                <h3>
                    <i class="fas fa-id-card"></i>
                    Detail Pribadi
                </h3>
            </div>
            <div class="profile-card-body">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="gender">Jenis Kelamin</label>
                        <select class="form-control form-select @error('gender') is-invalid @enderror" 
                                id="gender" name="gender">
                            <option value="">Pilih Jenis Kelamin</option>
                            <option value="L" {{ old('gender', Auth::user()->gender) == 'L' ? 'selected' : '' }}>Laki-laki</option>
                            <option value="P" {{ old('gender', Auth::user()->gender) == 'P' ? 'selected' : '' }}>Perempuan</option>
                        </select>
                        @error('gender')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="birth_date">Tanggal Lahir</label>
                        <input type="date" class="form-control @error('birth_date') is-invalid @enderror" 
                               id="birth_date" name="birth_date" 
                               value="{{ old('birth_date', Auth::user()->birth_date ? Auth::user()->birth_date->format('Y-m-d') : '') }}">
                        @error('birth_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-group">
                    <label for="bio">Bio</label>
                    <textarea class="form-control @error('bio') is-invalid @enderror" 
                              id="bio" name="bio" rows="3" 
                              placeholder="Ceritakan sedikit tentang diri Anda...">{{ old('bio', Auth::user()->bio) }}</textarea>
                    <div class="form-text">Maksimal 500 karakter</div>
                    @error('bio')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="address">Alamat</label>
                    <textarea class="form-control @error('address') is-invalid @enderror" 
                              id="address" name="address" rows="2" 
                              placeholder="Alamat lengkap Anda...">{{ old('address', Auth::user()->address) }}</textarea>
                    @error('address')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="city">Kota</label>
                        <input type="text" class="form-control @error('city') is-invalid @enderror" 
                               id="city" name="city" value="{{ old('city', Auth::user()->city) }}"
                               placeholder="Nama kota">
                        @error('city')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="postal_code">Kode Pos</label>
                        <input type="text" class="form-control @error('postal_code') is-invalid @enderror" 
                               id="postal_code" name="postal_code" value="{{ old('postal_code', Auth::user()->postal_code) }}"
                               placeholder="12345">
                        @error('postal_code')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="country">Negara</label>
                        <select class="form-control form-select @error('country') is-invalid @enderror" 
                                id="country" name="country">
                            <option value="Indonesia" {{ old('country', Auth::user()->country ?? 'Indonesia') == 'Indonesia' ? 'selected' : '' }}>Indonesia</option>
                            <option value="Malaysia" {{ old('country', Auth::user()->country) == 'Malaysia' ? 'selected' : '' }}>Malaysia</option>
                            <option value="Singapore" {{ old('country', Auth::user()->country) == 'Singapore' ? 'selected' : '' }}>Singapore</option>
                            <option value="Thailand" {{ old('country', Auth::user()->country) == 'Thailand' ? 'selected' : '' }}>Thailand</option>
                            <option value="Philippines" {{ old('country', Auth::user()->country) == 'Philippines' ? 'selected' : '' }}>Philippines</option>
                            <option value="Vietnam" {{ old('country', Auth::user()->country) == 'Vietnam' ? 'selected' : '' }}>Vietnam</option>
                            <option value="Other" {{ old('country', Auth::user()->country) == 'Other' ? 'selected' : '' }}>Lainnya</option>
                        </select>
                        @error('country')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
            </div>
        </div>

        <!-- Preferences & Settings Card -->
        <div class="profile-card">
            <div class="profile-card-header">
                <h3>
                    <i class="fas fa-cog"></i>
                    Preferensi & Pengaturan
                </h3>
            </div>
            <div class="profile-card-body">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="language">Bahasa</label>
                        <select class="form-control form-select @error('language') is-invalid @enderror" 
                                id="language" name="language">
                            <option value="id" {{ old('language', Auth::user()->language ?? 'id') == 'id' ? 'selected' : '' }}>Bahasa Indonesia</option>
                            <option value="en" {{ old('language', Auth::user()->language) == 'en' ? 'selected' : '' }}>English</option>
                        </select>
                        @error('language')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="timezone">Zona Waktu</label>
                        <select class="form-control form-select @error('timezone') is-invalid @enderror" 
                                id="timezone" name="timezone">
                            <option value="Asia/Jakarta" {{ old('timezone', Auth::user()->timezone ?? 'Asia/Jakarta') == 'Asia/Jakarta' ? 'selected' : '' }}>WIB - Asia/Jakarta</option>
                            <option value="Asia/Makassar" {{ old('timezone', Auth::user()->timezone) == 'Asia/Makassar' ? 'selected' : '' }}>WITA - Asia/Makassar</option>
                            <option value="Asia/Jayapura" {{ old('timezone', Auth::user()->timezone) == 'Asia/Jayapura' ? 'selected' : '' }}>WIT - Asia/Jayapura</option>
                            <option value="Asia/Kuala_Lumpur" {{ old('timezone', Auth::user()->timezone) == 'Asia/Kuala_Lumpur' ? 'selected' : '' }}>Malaysia</option>
                            <option value="Asia/Singapore" {{ old('timezone', Auth::user()->timezone) == 'Asia/Singapore' ? 'selected' : '' }}>Singapore</option>
                        </select>
                        @error('timezone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="theme">Tema Tampilan</label>
                        <select class="form-control form-select @error('theme') is-invalid @enderror" 
                                id="theme" name="theme">
                            <option value="light" {{ old('theme', Auth::user()->theme ?? 'light') == 'light' ? 'selected' : '' }}>
                                <i class="fas fa-sun"></i> Terang
                            </option>
                            <option value="dark" {{ old('theme', Auth::user()->theme) == 'dark' ? 'selected' : '' }}>
                                <i class="fas fa-moon"></i> Gelap
                            </option>
                            <option value="solarized-dark" {{ old('theme', Auth::user()->theme) == 'solarized-dark' ? 'selected' : '' }}>
                                <i class="fas fa-moon"></i> Solarized Dark
                            </option>
                            <option value="synth-wave" {{ old('theme', Auth::user()->theme) == 'synth-wave' ? 'selected' : '' }}>
                                <i class="fas fa-city"></i> Synth Wave
                            </option>
                        </select>
                        @error('theme')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="currency">Mata Uang</label>
                        <select class="form-control form-select @error('currency') is-invalid @enderror" 
                                id="currency" name="currency">
                            <option value="IDR" {{ old('currency', Auth::user()->currency ?? 'IDR') == 'IDR' ? 'selected' : '' }}>IDR - Rupiah Indonesia</option>
                            <option value="USD" {{ old('currency', Auth::user()->currency) == 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                            <option value="EUR" {{ old('currency', Auth::user()->currency) == 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                            <option value="MYR" {{ old('currency', Auth::user()->currency) == 'MYR' ? 'selected' : '' }}>MYR - Malaysian Ringgit</option>
                            <option value="SGD" {{ old('currency', Auth::user()->currency) == 'SGD' ? 'selected' : '' }}>SGD - Singapore Dollar</option>
                        </select>
                        @error('currency')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="date_format">Format Tanggal</label>
                        <select class="form-control form-select @error('date_format') is-invalid @enderror" 
                                id="date_format" name="date_format">
                            <option value="d/m/Y" {{ old('date_format', Auth::user()->date_format ?? 'd/m/Y') == 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                            <option value="m/d/Y" {{ old('date_format', Auth::user()->date_format) == 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                            <option value="Y-m-d" {{ old('date_format', Auth::user()->date_format) == 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                            <option value="d-m-Y" {{ old('date_format', Auth::user()->date_format) == 'd-m-Y' ? 'selected' : '' }}>DD-MM-YYYY</option>
                        </select>
                        @error('date_format')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="time_format">Format Waktu</label>
                        <select class="form-control form-select @error('time_format') is-invalid @enderror" 
                                id="time_format" name="time_format">
                            <option value="24" {{ old('time_format', Auth::user()->time_format ?? '24') == '24' ? 'selected' : '' }}>24 Jam (23:59)</option>
                            <option value="12" {{ old('time_format', Auth::user()->time_format) == '12' ? 'selected' : '' }}>12 Jam (11:59 PM)</option>
                        </select>
                        @error('time_format')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
            </div>
        </div>

        <!-- Security Settings Card -->
        <div class="profile-card">
            <div class="profile-card-header">
                <h3>
                    <i class="fas fa-shield-alt"></i>
                    Pengaturan Keamanan
                </h3>
            </div>
            <div class="profile-card-body">
                
                <div class="security-info">
                    <h5><i class="fas fa-info-circle"></i> Informasi Keamanan</h5>
                    
                    <div class="security-status">
                        <span><strong>Status Akun:</strong></span>
                        <span class="status-badge {{ Auth::user()->is_active ? 'status-active' : 'status-inactive' }}">
                            {{ Auth::user()->is_active ? 'Aktif' : 'Nonaktif' }}
                        </span>
                    </div>
                    
                    <div class="security-status">
                        <span><strong>Two-Factor Authentication:</strong></span>
                        <span class="status-badge {{ Auth::user()->two_factor_enabled ? 'status-active' : 'status-inactive' }}">
                            {{ Auth::user()->two_factor_enabled ? 'Aktif' : 'Nonaktif' }}
                        </span>
                    </div>
                    
                    @if(Auth::user()->last_login_at)
                    <div class="security-status">
                        <span><strong>Login Terakhir:</strong></span>
                        <span>{{ Auth::user()->last_login_at->format('d M Y, H:i') }}</span>
                        @if(Auth::user()->last_login_ip)
                            <small>({{ Auth::user()->last_login_ip }})</small>
                        @endif
                    </div>
                    @endif
                    
                    <div class="security-status">
                        <span><strong>Percobaan Login Gagal:</strong></span>
                        <span class="{{ Auth::user()->failed_login_attempts > 0 ? 'text-warning' : 'text-success' }}">
                            {{ Auth::user()->failed_login_attempts }}
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" name="two_factor_enabled" value="1" 
                               {{ old('two_factor_enabled', Auth::user()->two_factor_enabled) ? 'checked' : '' }}>
                        Aktifkan Two-Factor Authentication (2FA)
                    </label>
                    <div class="form-text">Meningkatkan keamanan akun dengan verifikasi dua langkah</div>
                    @error('two_factor_enabled')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="notification_enabled" value="1" 
                               {{ old('notification_enabled', Auth::user()->notification_enabled ?? true) ? 'checked' : '' }}>
                        Terima notifikasi email
                    </label>
                    <div class="form-text">Dapatkan notifikasi untuk aktivitas penting di akun Anda</div>
                </div>
                
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="profile-card">
            <div class="profile-card-body">
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                    <a href="{{ route('settings.profile') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
        </div>
        
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Avatar Preview
    const avatarInput = document.getElementById('avatarInput');
    const avatarPreview = document.getElementById('avatarPreview');
    
    avatarInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Check file size (2MB limit)
            if (file.size > 2 * 1024 * 1024) {
                alert('Ukuran file terlalu besar. Maksimal 2MB.');
                this.value = '';
                return;
            }
            
            // Check file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert('Format file tidak didukung. Gunakan JPG, PNG, GIF, atau WebP.');
                this.value = '';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                avatarPreview.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
    
    // Form validation
    const form = document.getElementById('profileEditForm');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        let errorMessages = [];
        
        // Required field validation
        const requiredFields = [
            { id: 'name', name: 'Nama Lengkap' },
            { id: 'username', name: 'Username' },
            { id: 'email', name: 'Email' }
        ];
        
        requiredFields.forEach(function(field) {
            const element = document.getElementById(field.id);
            if (!element.value.trim()) {
                element.classList.add('is-invalid');
                errorMessages.push(`${field.name} harus diisi`);
                isValid = false;
            } else {
                element.classList.remove('is-invalid');
            }
        });
        
        // Email validation
        const email = document.getElementById('email');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email.value && !emailRegex.test(email.value)) {
            email.classList.add('is-invalid');
            errorMessages.push('Format email tidak valid');
            isValid = false;
        }
        
        // Username validation (alphanumeric and underscore only)
        const username = document.getElementById('username');
        const usernameRegex = /^[a-zA-Z0-9_]+$/;
        if (username.value && !usernameRegex.test(username.value)) {
            username.classList.add('is-invalid');
            errorMessages.push('Username hanya boleh berisi huruf, angka, dan garis bawah');
            isValid = false;
        }
        
        // Phone validation (Indonesian format)
        const phone = document.getElementById('phone');
        if (phone.value) {
            // Remove all non-digit characters for validation
            const cleanPhone = phone.value.replace(/\D/g, '');
            
            // Indonesian phone number patterns:
            // 08xxxxxxxxx (10-13 digits total)
            // 62xxxxxxxxx (11-14 digits total) 
            // +62xxxxxxxxx (12-15 digits total with +)
            const phoneRegex = /^(\+?62|0)[0-9]{8,13}$/;
            
            if (!phoneRegex.test(cleanPhone) && !phoneRegex.test(phone.value)) {
                phone.classList.add('is-invalid');
                errorMessages.push('Format nomor telepon tidak valid. Contoh: 0812-3456-7890 atau +62812-3456-7890');
                isValid = false;
            } else {
                phone.classList.remove('is-invalid');
            }
        }
        
        // Date of birth validation (not in future)
        const dateOfBirth = document.getElementById('birth_date');
        if (dateOfBirth.value) {
            const birthDate = new Date(dateOfBirth.value);
            const today = new Date();
            if (birthDate > today) {
                dateOfBirth.classList.add('is-invalid');
                errorMessages.push('Tanggal lahir tidak boleh di masa depan');
                isValid = false;
            } else {
                dateOfBirth.classList.remove('is-invalid');
            }
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('Mohon periksa kembali form Anda:\n- ' + errorMessages.join('\n- '));
            // Scroll to first error
            const firstError = document.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        }
    });
    
    // Bio character counter
    const bioField = document.getElementById('bio');
    const bioCounter = document.createElement('div');
    bioCounter.className = 'form-text text-right';
    bioField.parentNode.appendChild(bioCounter);
    
    function updateBioCounter() {
        const remaining = 500 - bioField.value.length;
        bioCounter.textContent = `${bioField.value.length}/500 karakter`;
        bioCounter.style.color = remaining < 50 ? '#dc3545' : '#6c757d';
        
        if (bioField.value.length > 500) {
            bioField.classList.add('is-invalid');
            bioCounter.style.color = '#dc3545';
        } else {
            bioField.classList.remove('is-invalid');
        }
    }
    
    bioField.addEventListener('input', updateBioCounter);
    updateBioCounter();
    
    // Auto-format phone number
    const phoneField = document.getElementById('phone');
    phoneField.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        
        // Limit to 15 characters max (international format)
        if (value.length > 15) {
            value = value.substring(0, 15);
        }
        
        // Add country code if starts with 8 (Indonesian mobile)
        if (value.startsWith('8')) {
            value = '0' + value;
        }
        
        // Format display for Indonesian numbers
        if (value.startsWith('0')) {
            // Format: 0812-3456-7890
            if (value.length > 4) {
                value = value.substring(0, 4) + '-' + value.substring(4);
            }
            if (value.length > 9) {
                value = value.substring(0, 9) + '-' + value.substring(9);
            }
        } else if (value.startsWith('62')) {
            // Format: 62812-3456-7890
            if (value.length > 5) {
                value = value.substring(0, 5) + '-' + value.substring(5);
            }
            if (value.length > 10) {
                value = value.substring(0, 10) + '-' + value.substring(10);
            }
        }
        
        e.target.value = value;
    });
    
    // Real-time field validation
    const fields = ['name', 'username', 'email', 'phone'];
    fields.forEach(function(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', function() {
                // Special handling for phone field
                if (fieldId === 'phone' && this.value.trim()) {
                    const cleanPhone = this.value.replace(/\D/g, '');
                    const phoneRegex = /^(\+?62|0)[0-9]{8,13}$/;
                    
                    if (phoneRegex.test(cleanPhone) || phoneRegex.test(this.value)) {
                        this.classList.remove('is-invalid');
                    }
                } else if (this.classList.contains('is-invalid') && this.value.trim()) {
                    this.classList.remove('is-invalid');
                }
            });
        }
    });
    
    // Theme preview
    const themeSelect = document.getElementById('theme');
    themeSelect.addEventListener('change', function() {
        const selectedTheme = this.value;
        // You can add preview functionality here
        console.log('Theme changed to:', selectedTheme);
    });
    
    // Form dirty state tracking
    let formIsDirty = false;
    const formElements = form.querySelectorAll('input, select, textarea');
    
    formElements.forEach(function(element) {
        element.addEventListener('change', function() {
            formIsDirty = true;
        });
    });
    
    // Warn user before leaving if form has unsaved changes
    window.addEventListener('beforeunload', function(e) {
        if (formIsDirty) {
            e.preventDefault();
            e.returnValue = 'Anda memiliki perubahan yang belum disimpan. Yakin ingin meninggalkan halaman?';
        }
    });
    
    // Reset dirty state when form is submitted
    form.addEventListener('submit', function(e) {
        if (!e.defaultPrevented) {
            formIsDirty = false;
            
            // Add loading state to submit button
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
            
            // Re-enable after 10 seconds as fallback
            setTimeout(function() {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            }, 10000);
        }
    });
    
    // Auto-hide alert messages after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.transition = 'opacity 0.5s ease';
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 500);
        }, 5000);
    });
});
</script>
@endpush
@endsection

