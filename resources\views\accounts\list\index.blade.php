@extends('layouts.dashboard')

@section('title', 'Daftar Akun & Dompet')

@section('page-title', '🏦 Daftar Akun & Dompet')

@push('styles')
<style>
    /* Dashboard container styling sesuai referensi */
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    /* Theme support untuk breadcrumb */
    body[data-theme="solarized-dark"] .breadcrumb-item a {
        color: #b58900 !important;
    }
    
    body[data-theme="solarized-dark"] .breadcrumb-item.active {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item a {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item.active {
        color: #ffffff !important;
    }
    
    /* Theme support untuk h2 */
    body[data-theme="solarized-dark"] h2 {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] h2 {
        color: #ffffff !important;
    }
    
    /* Theme support untuk small text */
    body[data-theme="solarized-dark"] .text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="solarized-dark"] .small.text-muted,
    body[data-theme="solarized-dark"] small.text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="solarized-dark"] .small {
        color: #93a1a1 !important;
    }
    
    body[data-theme="solarized-dark"] .card-text.text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="solarized-dark"] .card-text.small {
        color: #93a1a1 !important;
    }
    
    body[data-theme="synth-wave"] .text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .small.text-muted,
    body[data-theme="synth-wave"] small.text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .small {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .card-text.text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .card-text.small {
        color: #cccccc !important;
    }

    /* Theme support untuk cards - Blue theme untuk accounts */
    body[data-theme="solarized-dark"] .account-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
    }
    
    body[data-theme="synth-wave"] .account-card {
        background: rgba(0, 128, 255, 0.1);
        border: 1px solid #0080ff;
        color: #ffffff;
    }
    
    body[data-theme="synth-wave"] .account-card .card-title,
    body[data-theme="synth-wave"] .account-card .card-text {
        color: #ffffff !important;
    }
    
    /* Default cream theme with blue accent untuk accounts */
    .account-card {
        background: #f8fffe;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 20px;
        position: relative;
    }
    
    .account-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
    }
    
    .account-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 15px;
        flex-shrink: 0;
    }
    
    .account-icon i {
        font-size: 24px !important;
        color: white !important;
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro" !important;
        font-weight: 900 !important;
        display: inline-block !important;
        line-height: 1 !important;
        text-align: center !important;
        width: auto !important;
    }
    
    .account-icon .emoji-fallback {
        font-size: 24px;
        line-height: 1;
        display: none;
    }
    
    /* FontAwesome fallback untuk emoji */
    body.fa-fallback .account-icon i {
        display: none !important;
    }
    
    body.fa-fallback .account-icon .emoji-fallback {
        display: block !important;
    }
    
    .stats-card {
        background: #f8fffe;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #333;
        padding: 20px;
        margin-bottom: 25px;
        transition: all 0.3s ease;
    }
    
    .btn-add {
        background: #f8fffe;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 12px 30px;
        color: #333;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        color: #333;
        text-decoration: none;
    }
    
    /* Theme support untuk stats card dan btn-add */
    body[data-theme="solarized-dark"] .stats-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
        color: #333;
    }
    
    body[data-theme="solarized-dark"] .btn-add {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
        color: #333;
    }
    
    body[data-theme="solarized-dark"] .btn-add:hover {
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        color: #333;
    }
    
    body[data-theme="synth-wave"] .stats-card {
        background: rgba(0, 128, 255, 0.1);
        border: 1px solid #0080ff;
        color: #ffffff;
        box-shadow: 0 20px 60px rgba(0, 128, 255, 0.2);
    }
    
    body[data-theme="synth-wave"] .btn-add {
        background: rgba(0, 128, 255, 0.1);
        border: 1px solid #0080ff;
        color: #ffffff;
        box-shadow: 0 8px 20px rgba(0, 128, 255, 0.2);
    }
    
    body[data-theme="synth-wave"] .btn-add:hover {
        box-shadow: 0 12px 30px rgba(0, 128, 255, 0.3);
        color: #ffffff;
        transform: translateY(-2px) scale(1.02);
    }
    
    /* Theme specific account icon */
    body[data-theme="solarized-dark"] .account-icon {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    body[data-theme="synth-wave"] .account-icon {
        box-shadow: 0 0 15px rgba(0,128,255,0.3);
        border: 2px solid rgba(0,128,255,0.5);
    }
    
    /* Dropdown styling */
    .dropdown-menu {
        border-radius: 10px;
        border: none;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        padding: 10px;
    }
    
    .dropdown-item {
        border-radius: 8px;
        padding: 10px 15px;
        margin-bottom: 5px;
        transition: all 0.3s ease;
    }
    
    .dropdown-item:hover {
        background: #f8f9fa;
        transform: translateX(5px);
    }
    
    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-right: 10px;
    }
    
    /* Theme support untuk dropdown */
    body[data-theme="synth-wave"] .dropdown-menu {
        background: rgba(0, 20, 40, 0.95) !important;
        border: 1px solid #0080ff !important;
    }
    
    body[data-theme="synth-wave"] .dropdown-item {
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .dropdown-item:hover {
        background: rgba(0, 128, 255, 0.2) !important;
        color: #0080ff !important;
    }
    
    /* Badge styling */
    .badge {
        font-size: 0.75em;
        padding: 0.5em 0.75em;
        border-radius: 10px;
    }
    
    /* Theme support untuk badge */
    body[data-theme="solarized-dark"] .badge.bg-success {
        background-color: #859900 !important;
    }
    
    body[data-theme="solarized-dark"] .badge.bg-secondary {
        background-color: #6c757d !important;
    }
    
    body[data-theme="solarized-dark"] .badge.bg-primary {
        background-color: #268bd2 !important;
    }
    
    body[data-theme="synth-wave"] .badge.bg-success {
        background: linear-gradient(45deg, #00ffff, #0080ff) !important;
        color: #000000 !important;
        font-weight: 600;
    }
    
    body[data-theme="synth-wave"] .badge.bg-secondary {
        background: rgba(108, 117, 125, 0.5) !important;
        color: #ffffff !important;
        border: 1px solid #6c757d;
    }
    
    body[data-theme="synth-wave"] .badge.bg-primary {
        background: linear-gradient(45deg, #0080ff, #00ffff) !important;
        color: #000000 !important;
        font-weight: 600;
    }
    
    /* Action buttons styling */
    .card .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.785rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .card .btn-sm:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    /* Theme support untuk action buttons */
    body[data-theme="synth-wave"] .card .btn-outline-primary {
        color: #0080ff !important;
        border-color: #0080ff !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-primary:hover {
        background-color: #0080ff !important;
        color: #000000 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-warning {
        color: #ffc107 !important;
        border-color: #ffc107 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-warning:hover {
        background-color: #ffc107 !important;
        color: #000000 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-success {
        color: #00ff00 !important;
        border-color: #00ff00 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-success:hover {
        background-color: #00ff00 !important;
        color: #000000 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-danger {
        color: #ff1493 !important;
        border-color: #ff1493 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-danger:hover {
        background-color: #ff1493 !important;
        color: #ffffff !important;
    }
    
    /* Balance styling */
    .balance-positive {
        color: #28a745;
        font-weight: 600;
    }
    
    .balance-negative {
        color: #dc3545;
        font-weight: 600;
    }
    
    .balance-zero {
        color: #6c757d;
        font-weight: 600;
    }
    
    /* Account type badges */
    .type-bank { background: linear-gradient(45deg, #007bff, #0056b3); }
    .type-cash { background: linear-gradient(45deg, #28a745, #1e7e34); }
    .type-e_wallet { background: linear-gradient(45deg, #17a2b8, #138496); }
    .type-investment { background: linear-gradient(45deg, #ffc107, #e0a800); }
    .type-credit_card { background: linear-gradient(45deg, #dc3545, #c82333); }
    .type-savings { background: linear-gradient(45deg, #6f42c1, #59359a); }
    .type-other { background: linear-gradient(45deg, #6c757d, #545b62); }
    
    /* Account status indicator */
    .account-status {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
    }
    
    .account-status.active {
        background: #28a745;
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    
    .account-status.inactive {
        background: #6c757d;
    }
    
    /* Filter controls */
    .filter-controls {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    body[data-theme="solarized-dark"] .filter-controls {
        background: rgba(253, 246, 227, 0.9);
    }
    
    body[data-theme="synth-wave"] .filter-controls {
        background: rgba(0, 20, 40, 0.9);
        border: 1px solid #0080ff;
        color: #ffffff;
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('dashboard') }}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="#" class="text-decoration-none">
                    <i class="fas fa-wallet me-1"></i>Akun & Dompet
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-list me-1"></i>Daftar Akun
            </li>
        </ol>
    </nav>

    <!-- Stats Card -->
    <div class="stats-card">
        <div class="row text-center">
            <div class="col-md-2 col-6">
                <div class="fw-bold fs-4">{{ $stats['total_accounts'] ?? 0 }}</div>
                <div class="small">Total Akun</div>
            </div>
            <div class="col-md-2 col-6">
                <div class="fw-bold fs-4 text-success">{{ $stats['active_accounts'] ?? 0 }}</div>
                <div class="small">Akun Aktif</div>
            </div>
            <div class="col-md-2 col-6">
                <div class="fw-bold fs-4 text-warning">{{ $stats['inactive_accounts'] ?? 0 }}</div>
                <div class="small">Akun Non Aktif</div>
            </div>
            <div class="col-md-3 col-6">
                <div class="fw-bold fs-4 text-primary">{{ $stats['total_balance'] ?? 'Rp 0' }}</div>
                <div class="small">Total Saldo</div>
            </div>
            <div class="col-md-3 col-12">
                <div class="fw-bold fs-4 text-info">{{ $stats['account_types'] ?? 0 }}</div>
                <div class="small">Jenis Akun Aktif</div>
            </div>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="filter-controls">
        <form method="GET" action="{{ route('accounts.list.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label small">Status</label>
                <select name="status" id="status" class="form-select form-select-sm">
                    <option value="all" {{ request('status') === 'all' ? 'selected' : '' }}>Semua Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Aktif</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Nonaktif</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label small">Jenis Akun</label>
                <select name="type" id="type" class="form-select form-select-sm">
                    <option value="all" {{ request('type') === 'all' ? 'selected' : '' }}>Semua Jenis</option>
                    <option value="bank" {{ request('type') === 'bank' ? 'selected' : '' }}>Bank</option>
                    <option value="cash" {{ request('type') === 'cash' ? 'selected' : '' }}>Tunai</option>
                    <option value="e_wallet" {{ request('type') === 'e_wallet' ? 'selected' : '' }}>E-Wallet</option>
                    <option value="investment" {{ request('type') === 'investment' ? 'selected' : '' }}>Investasi</option>
                    <option value="credit_card" {{ request('type') === 'credit_card' ? 'selected' : '' }}>Kartu Kredit</option>
                    <option value="savings" {{ request('type') === 'savings' ? 'selected' : '' }}>Tabungan</option>
                    <option value="other" {{ request('type') === 'other' ? 'selected' : '' }}>Lainnya</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="search" class="form-label small">Cari Akun</label>
                <input type="text" name="search" id="search" class="form-control form-control-sm" 
                       placeholder="Cari berdasarkan nama akun..." 
                       value="{{ request('search') }}">
            </div>
            <div class="col-md-2">
                <label class="form-label small text-white">.</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Header dengan tombol tambah -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-wallet text-primary me-2"></i>
            Daftar Akun & Dompet
        </h2>
        <a href="{{ route('accounts.create') }}" class="btn btn-add">
            <i class="fas fa-plus me-2"></i>Tambah Akun
        </a>
    </div>

    <!-- Accounts Grid -->
    <div class="row">
        @forelse($accounts ?? [] as $account)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card account-card h-100">
                    <!-- Status indicator -->
                    <div class="account-status {{ $account->is_active ? 'active' : 'inactive' }}"></div>
                    
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="account-icon me-3 type-{{ $account->type }}" style="background-color: {{ $account->color ?? '#007bff' }};">
                                    <i class="{{ $account->icon ?? 'fas fa-wallet' }} fa-lg" style="color: white !important;"></i>
                                    <span class="emoji-fallback">
                                        @switch($account->type)
                                            @case('bank') 🏦 @break
                                            @case('cash') 💵 @break
                                            @case('e_wallet') 📱 @break
                                            @case('investment') 📈 @break
                                            @case('credit_card') 💳 @break
                                            @case('savings') 🐷 @break
                                            @default 💰
                                        @endswitch
                                    </span>
                                </div>
                                <div>
                                    <h5 class="card-title fw-bold mb-1">{{ $account->name }}</h5>
                                    <span class="badge type-{{ $account->type }}">
                                        @switch($account->type)
                                            @case('bank') Bank @break
                                            @case('cash') Tunai @break
                                            @case('e_wallet') E-Wallet @break
                                            @case('investment') Investasi @break
                                            @case('credit_card') Kartu Kredit @break
                                            @case('savings') Tabungan @break
                                            @default Lainnya
                                        @endswitch
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex-grow-1">
                            <!-- Balance -->
                            <div class="mb-3">
                                <div class="small text-muted">Saldo Saat Ini</div>
                                <div class="fs-5 fw-bold {{ 
                                    $account->current_balance > 0 ? 'balance-positive' : 
                                    ($account->current_balance < 0 ? 'balance-negative' : 'balance-zero') 
                                }}">
                                    {{ formatRupiah($account->current_balance) }}
                                </div>
                            </div>
                            
                            <!-- Account details -->
                            @if($account->bank_name)
                                <div class="small text-muted mb-1">
                                    <i class="fas fa-building me-1"></i>
                                    {{ $account->bank_name }}
                                </div>
                            @endif
                            
                            @if($account->account_number)
                                <div class="small text-muted mb-1">
                                    <i class="fas fa-hashtag me-1"></i>
                                    {{ $account->account_number }}
                                </div>
                            @endif
                            
                            @if($account->description)
                                <p class="card-text text-muted small mb-2">{{ Str::limit($account->description, 100) }}</p>
                            @endif
                            
                            <!-- Credit limit for credit cards -->
                            @if($account->type === 'credit_card' && $account->credit_limit)
                                <div class="small text-muted mb-2">
                                    <i class="fas fa-credit-card me-1"></i>
                                    Limit: {{ formatRupiah($account->credit_limit) }}
                                </div>
                            @endif
                            
                            <!-- Transaction count -->
                            <div class="small text-muted mt-1 mb-3">
                                <i class="fas fa-exchange-alt me-1"></i>
                                {{ $account->transactions_count ?? 0 }} transaksi
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="d-flex gap-2 flex-wrap mt-auto">
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="editAccount({{ $account->id }})" title="Edit Akun">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </button>
                                <button class="btn btn-sm btn-outline-{{ $account->is_active ? 'warning' : 'success' }}" 
                                        onclick="toggleStatus({{ $account->id }}, {{ $account->is_active ? 'false' : 'true' }})"
                                        title="{{ $account->is_active ? 'Nonaktifkan' : 'Aktifkan' }} Akun">
                                    <i class="fas fa-{{ $account->is_active ? 'eye-slash' : 'eye' }} me-1"></i>
                                    {{ $account->is_active ? 'Nonaktifkan' : 'Aktifkan' }}
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteAccount({{ $account->id }}, '{{ $account->name }}')"
                                        title="Hapus Akun">
                                    <i class="fas fa-trash me-1"></i>Hapus
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-wallet text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">Belum ada akun atau dompet</h4>
                    <p class="text-muted">Mulai dengan menambahkan akun atau dompet pertama Anda</p>
                    <a href="{{ route('accounts.create.index') }}" class="btn btn-add">
                        <i class="fas fa-plus me-2"></i>Tambah Akun Pertama
                    </a>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if(isset($accounts) && $accounts->hasPages())
        <div class="d-flex justify-content-center mt-4">
            {{ $accounts->links() }}
        </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Force initialize Bootstrap dropdowns
    setTimeout(function() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
            console.log('Initializing Bootstrap dropdowns...');
            var dropdownElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
            console.log('Bootstrap dropdowns initialized:', dropdownList.length);
        } else {
            console.log('Bootstrap not available, using manual dropdown');
        }
    }, 100);
    
    // Check if FontAwesome is loaded
    function isFontAwesomeLoaded() {
        const testElement = document.createElement('i');
        testElement.className = 'fas fa-heart';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement, ':before');
        const content = computedStyle.getPropertyValue('content');
        
        document.body.removeChild(testElement);
        
        // FontAwesome loaded if content is not "none" or empty
        return content && content !== 'none' && content !== '""';
    }
    
    // Add fallback class if FontAwesome is not loaded
    if (!isFontAwesomeLoaded()) {
        console.log('FontAwesome not detected, using emoji fallbacks');
        document.body.classList.add('fa-fallback');
    }
    
    // Manual dropdown toggle as additional fallback
    document.addEventListener('click', function(e) {
        if (e.target.closest('[data-bs-toggle="dropdown"]')) {
            const button = e.target.closest('[data-bs-toggle="dropdown"]');
            const menu = button.nextElementSibling;
            
            if (menu && menu.classList.contains('dropdown-menu')) {
                e.preventDefault();
                e.stopPropagation();
                
                // Close all other dropdowns
                document.querySelectorAll('.dropdown-menu.show').forEach(otherMenu => {
                    if (otherMenu !== menu) {
                        otherMenu.classList.remove('show');
                    }
                });
                
                // Toggle current dropdown
                menu.classList.toggle('show');
                console.log('Dropdown toggled manually');
            }
        } else if (!e.target.closest('.dropdown-menu')) {
            // Close all dropdowns when clicking outside
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });
});

function editAccount(accountId) {
    // Redirect to create page with edit parameter (temporary solution)
    window.location.href = `/accounts/create-account?edit=${accountId}`;
    
    // Alternative: Show info modal
    // Swal.fire({
    //     title: 'Edit Akun',
    //     text: 'Fitur edit akun akan segera tersedia. Saat ini Anda dapat menghapus dan membuat akun baru.',
    //     icon: 'info',
    //     confirmButtonText: 'OK'
    // });
}

function toggleStatus(accountId, newStatus) {
    const action = newStatus ? 'mengaktifkan' : 'menonaktifkan';
    const statusText = newStatus ? 'aktif' : 'nonaktif';
    const icon = newStatus ? 'success' : 'warning';
    
    Swal.fire({
        title: `${newStatus ? 'Aktifkan' : 'Nonaktifkan'} Akun?`,
        html: `Apakah Anda yakin ingin ${action} akun ini?<br><br><small class="text-muted">Akun ${statusText} ${newStatus ? 'akan muncul' : 'tidak akan muncul'} di dropdown transaksi.</small>`,
        icon: icon,
        showCancelButton: true,
        confirmButtonColor: newStatus ? '#28a745' : '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: `Ya, ${newStatus ? 'Aktifkan' : 'Nonaktifkan'}!`,
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: `${newStatus ? 'Mengaktifkan' : 'Menonaktifkan'}...`,
                text: 'Sedang memproses perubahan status',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Send toggle request
            fetch(`/accounts/${accountId}/toggle-status`, {
                method: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ is_active: newStatus })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengubah status akun.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}

function deleteAccount(accountId, accountName) {
    Swal.fire({
        title: 'Hapus Akun?',
        html: `Apakah Anda yakin ingin menghapus akun <strong>${accountName}</strong>?<br><br><small class="text-muted">Tindakan ini tidak dapat dibatalkan dan akan menghapus semua transaksi terkait!</small>`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus akun',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Send delete request
            fetch(`/accounts/${accountId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menghapus akun.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}
</script>
@endpush
