<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'type',
        'icon',
        'color',
        'description',
        'budget_limit',
        'budget_period',
        'is_active',
        'sort_order',
        'metadata'
    ];

    protected $casts = [
        'budget_limit' => 'decimal:2',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'metadata' => 'array'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->name;
    }

    public function getTypeColorAttribute()
    {
        return match($this->type) {
            'income' => 'success',
            'expense' => 'danger',
            default => 'secondary'
        };
    }

    // Helper methods
    public function getTotalSpent($startDate = null, $endDate = null)
    {
        $query = $this->transactions()->where('type', 'expense')->completed();
        
        if ($startDate && $endDate) {
            $query->inDateRange($startDate, $endDate);
        }
        
        return $query->sum('amount');
    }

    public function getTotalEarned($startDate = null, $endDate = null)
    {
        $query = $this->transactions()->where('type', 'income')->completed();
        
        if ($startDate && $endDate) {
            $query->inDateRange($startDate, $endDate);
        }
        
        return $query->sum('amount');
    }

    public function getBudgetUsagePercentage($startDate = null, $endDate = null)
    {
        if (!$this->budget_limit) {
            return 0;
        }

        $spent = $this->getTotalSpent($startDate, $endDate);
        return ($spent / $this->budget_limit) * 100;
    }
}
