<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Account;
use App\Models\User;

class AccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::first();
        
        if (!$user) {
            $this->command->error('No user found. Please create a user first.');
            return;
        }

        $accounts = [
            [
                'name' => 'Dompet',
                'type' => 'cash',
                'initial_balance' => 500000,
                'current_balance' => 500000,
                'icon' => 'fas fa-wallet',
                'color' => '#2ecc71',
                'description' => 'Uang cash di dompet',
                'include_in_total' => true
            ],
            [
                'name' => 'BCA - Tabungan',
                'type' => 'bank',
                'account_number' => '**********',
                'bank_name' => 'Bank Central Asia',
                'initial_balance' => ********,
                'current_balance' => ********,
                'icon' => 'fas fa-university',
                'color' => '#3498db',
                'description' => 'Rekening tabungan utama',
                'interest_rate' => 2.5,
                'include_in_total' => true
            ],
            [
                'name' => 'Mandiri - Giro',
                'type' => 'bank',
                'account_number' => '**********',
                'bank_name' => 'Bank Mandiri',
                'initial_balance' => 5000000,
                'current_balance' => 5000000,
                'icon' => 'fas fa-university',
                'color' => '#f39c12',
                'description' => 'Rekening giro untuk transaksi bisnis',
                'include_in_total' => true
            ],
            [
                'name' => 'BCA Credit Card',
                'type' => 'credit_card',
                'account_number' => '4532-****-****-1234',
                'bank_name' => 'Bank Central Asia',
                'initial_balance' => 0,
                'current_balance' => 0,
                'credit_limit' => ********,
                'due_date' => now()->addDays(15)->toDateString(),
                'icon' => 'fas fa-credit-card',
                'color' => '#e74c3c',
                'description' => 'Kartu kredit untuk pembelian online',
                'include_in_total' => false
            ],
            [
                'name' => 'GoPay',
                'type' => 'e_wallet',
                'initial_balance' => 250000,
                'current_balance' => 250000,
                'icon' => 'fas fa-mobile-alt',
                'color' => '#00aa13',
                'description' => 'E-wallet GoPay',
                'include_in_total' => true
            ],
            [
                'name' => 'OVO',
                'type' => 'e_wallet',
                'initial_balance' => 150000,
                'current_balance' => 150000,
                'icon' => 'fas fa-mobile-alt',
                'color' => '#4c3fb1',
                'description' => 'E-wallet OVO',
                'include_in_total' => true
            ],
            [
                'name' => 'DANA',
                'type' => 'e_wallet',
                'initial_balance' => 100000,
                'current_balance' => 100000,
                'icon' => 'fas fa-mobile-alt',
                'color' => '#118eea',
                'description' => 'E-wallet DANA',
                'include_in_total' => true
            ],
            [
                'name' => 'Investasi Saham',
                'type' => 'investment',
                'initial_balance' => ********,
                'current_balance' => ********,
                'icon' => 'fas fa-chart-line',
                'color' => '#9b59b6',
                'description' => 'Portfolio saham dan reksadana',
                'interest_rate' => 12.0,
                'include_in_total' => true
            ],
            [
                'name' => 'Deposito BRI',
                'type' => 'investment',
                'account_number' => 'DEP-*********',
                'bank_name' => 'Bank Rakyat Indonesia',
                'initial_balance' => ********,
                'current_balance' => ********,
                'icon' => 'fas fa-piggy-bank',
                'color' => '#1abc9c',
                'description' => 'Deposito jangka panjang',
                'interest_rate' => 5.5,
                'include_in_total' => true
            ]
        ];

        foreach ($accounts as $accountData) {
            Account::create([
                'user_id' => $user->id,
                'name' => $accountData['name'],
                'type' => $accountData['type'],
                'account_number' => $accountData['account_number'] ?? null,
                'bank_name' => $accountData['bank_name'] ?? null,
                'initial_balance' => $accountData['initial_balance'],
                'current_balance' => $accountData['current_balance'],
                'icon' => $accountData['icon'],
                'color' => $accountData['color'],
                'description' => $accountData['description'],
                'credit_limit' => $accountData['credit_limit'] ?? null,
                'due_date' => $accountData['due_date'] ?? null,
                'interest_rate' => $accountData['interest_rate'] ?? null,
                'include_in_total' => $accountData['include_in_total']
            ]);
        }

        $this->command->info('Accounts created successfully!');
    }
}
