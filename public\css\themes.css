/* Global Theme CSS for MyMoney Application */

/* Light Theme (Default) */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #212529;
    transition: all 0.3s ease;
}

/* Solarized Dark Theme */
body[data-theme="solarized-dark"] {
    background: linear-gradient(135deg, #002b36 0%, #073642 100%) !important;
    color: #839496;
}

body[data-theme="solarized-dark"] .card,
body[data-theme="solarized-dark"] .form-control,
body[data-theme="solarized-dark"] .modal-content {
    background-color: #fdf6e3 !important;
    color: #586e75 !important;
    border-color: #eee8d5 !important;
}

body[data-theme="solarized-dark"] .form-control::placeholder {
    color: #93a1a1 !important;
}

body[data-theme="solarized-dark"] .form-control:focus {
    border-color: #268bd2 !important;
    box-shadow: 0 0 0 0.2rem rgba(38, 139, 210, 0.25) !important;
}

body[data-theme="solarized-dark"] .btn-primary {
    background: linear-gradient(45deg, #268bd2, #2aa198) !important;
    border-color: #268bd2 !important;
}

body[data-theme="solarized-dark"] .text-muted,
body[data-theme="solarized-dark"] small,
body[data-theme="solarized-dark"] .small {
    color: #657b83 !important;
}

body[data-theme="solarized-dark"] h1,
body[data-theme="solarized-dark"] h2,
body[data-theme="solarized-dark"] h3,
body[data-theme="solarized-dark"] h4,
body[data-theme="solarized-dark"] h5,
body[data-theme="solarized-dark"] h6,
body[data-theme="solarized-dark"] .card-title {
    color: #586e75 !important;
}

/* Synth Wave Theme */
body[data-theme="synth-wave"] {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a0033 50%, #000000 100%) !important;
    color: #ffffff;
}

body[data-theme="synth-wave"] .card {
    background: #1a1a1a !important;
    color: #ffffff !important;
    border: 1px solid #ff00ff !important;
    box-shadow: 0 0 20px rgba(255, 0, 255, 0.2) !important;
}

body[data-theme="synth-wave"] .modal-content {
    background: #1a1a1a !important;
    color: #ffffff !important;
    border: 1px solid #ff00ff !important;
}

body[data-theme="synth-wave"] .form-control {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
    border-color: #ff00ff !important;
}

body[data-theme="synth-wave"] .form-control::placeholder {
    color: #00ffff !important;
}

body[data-theme="synth-wave"] .form-control:focus {
    border-color: #00ffff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

body[data-theme="synth-wave"] .btn-primary {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    border-color: #ff00ff !important;
    box-shadow: 0 0 20px rgba(255, 0, 255, 0.4) !important;
}

body[data-theme="synth-wave"] .text-muted,
body[data-theme="synth-wave"] small,
body[data-theme="synth-wave"] .small {
    color: #00ffff !important;
}

body[data-theme="synth-wave"] h1,
body[data-theme="synth-wave"] h2,
body[data-theme="synth-wave"] h3,
body[data-theme="synth-wave"] h4,
body[data-theme="synth-wave"] h5,
body[data-theme="synth-wave"] h6 {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .card-title {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .card-header {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
    border-bottom: 1px solid #ff00ff !important;
}

/* Dark Theme (jika ada) */
body[data-theme="dark"] {
    background-color: #1a202c !important;
    color: #e2e8f0;
}

body[data-theme="dark"] .card,
body[data-theme="dark"] .modal-content {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important;
    border-color: #4a5568 !important;
}

body[data-theme="dark"] .form-control {
    background-color: #4a5568 !important;
    color: #e2e8f0 !important;
    border-color: #718096 !important;
}

body[data-theme="dark"] .form-control::placeholder {
    color: #a0aec0 !important;
}

body[data-theme="dark"] .form-control:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

body[data-theme="dark"] .text-muted,
body[data-theme="dark"] small,
body[data-theme="dark"] .small {
    color: #a0aec0 !important;
}

body[data-theme="dark"] h1,
body[data-theme="dark"] h2,
body[data-theme="dark"] h3,
body[data-theme="dark"] h4,
body[data-theme="dark"] h5,
body[data-theme="dark"] h6,
body[data-theme="dark"] .card-title {
    color: #e2e8f0 !important;
}

/* Global Sidebar Theme Styles */
body[data-theme="solarized-dark"] .sidebar {
    background: rgba(253, 246, 227, 0.95) !important;
}

body[data-theme="solarized-dark"] .sidebar-logo {
    color: #268bd2 !important;
}

body[data-theme="solarized-dark"] .menu-link {
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .menu-link:hover {
    background: rgba(38, 139, 210, 0.1) !important;
    color: #268bd2 !important;
}

body[data-theme="solarized-dark"] .menu-link.active {
    background: #268bd2 !important;
    color: white !important;
}

body[data-theme="synth-wave"] .sidebar {
    background: rgba(26, 26, 26, 0.95) !important;
    border-right: 1px solid #ff00ff !important;
}

body[data-theme="synth-wave"] .sidebar-logo {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .menu-link {
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .menu-link:hover {
    background: rgba(255, 0, 255, 0.1) !important;
    color: #ff00ff !important;
}

body[data-theme="synth-wave"] .menu-link.active {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
}

/* Global Progress Bar Styles */
body[data-theme="solarized-dark"] .progress {
    background-color: #eee8d5 !important;
}

body[data-theme="synth-wave"] .progress {
    background-color: #1a1a1a !important;
    border: 1px solid #ff00ff !important;
}

/* Global Badge Styles */
body[data-theme="solarized-dark"] .badge {
    background: linear-gradient(45deg, #268bd2, #2aa198) !important;
    color: white !important;
}

body[data-theme="synth-wave"] .badge {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
    box-shadow: 0 0 10px rgba(255, 0, 255, 0.3) !important;
}

/* Responsive */
@media (max-width: 768px) {
    body[data-theme="synth-wave"] h1,
    body[data-theme="synth-wave"] h2,
    body[data-theme="synth-wave"] h3 {
        font-size: calc(1rem + 0.5vw) !important;
    }
}
