<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\SecurityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class TwoFactorController extends Controller
{
    /**
     * Show 2FA verification form
     */
    public function showVerifyForm()
    {
        $user = Auth::user();
        
        if (!$user || !$user->has2FAEnabled()) {
            return redirect()->route('dashboard');
        }
        
        if (session('2fa_verified') === true) {
            return redirect()->intended('dashboard');
        }
        
        return view('auth.2fa-verify');
    }
    
    /**
     * Verify 2FA code
     */
    public function verify(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
        ], [
            'code.required' => 'Kode verifikasi wajib diisi.',
        ]);
        
        $user = Auth::user();
        
        if (!$user || !$user->has2FAEnabled()) {
            return redirect()->route('login');
        }
        
        $code = str_replace(' ', '', $request->code);
        $isValidCode = false;
        $method = '';
        
        // Try TOTP code first (6 digits)
        if (strlen($code) === 6 && is_numeric($code)) {
            $isValidCode = $user->verify2FACode($code);
            $method = 'TOTP';
        }
        
        // Try backup code if TOTP failed (8 characters)
        if (!$isValidCode && strlen($code) === 8) {
            $isValidCode = $user->useBackupCode($code);
            $method = 'backup_code';
        }
        
        if (!$isValidCode) {
            SecurityLog::logEvent(
                $user->id,
                '2fa_verification_failed',
                'Failed 2FA verification attempt',
                'medium',
                [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'code_length' => strlen($code)
                ]
            );
            
            return back()->withErrors([
                'code' => 'Kode verifikasi tidak valid. Gunakan kode 6 digit dari authenticator app atau backup code 8 karakter.'
            ]);
        }
        
        // Mark as verified in session
        session(['2fa_verified' => true]);
        
        SecurityLog::logEvent(
            $user->id,
            '2fa_verification_success',
            'Successful 2FA verification',
            'low',
            [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'method' => $method
            ]
        );
        
        // Show backup code warning if used
        if ($method === 'backup_code') {
            $remainingCodes = count($user->backup_codes ?? []);
            if ($remainingCodes <= 3) {
                session()->flash('warning', "Anda baru saja menggunakan backup code. Tersisa {$remainingCodes} backup codes. Segera regenerate backup codes baru.");
            }
        }
        
        return redirect()->intended('dashboard')
            ->with('success', 'Verifikasi 2FA berhasil!');
    }
    
    /**
     * Resend 2FA (not applicable for TOTP, but show instruction)
     */
    public function resend()
    {
        return back()->with('info', 'Kode TOTP diperbarui setiap 30 detik secara otomatis di aplikasi authenticator Anda. Tidak perlu meminta kode baru.');
    }
    
    /**
     * Show 2FA verification form for sensitive operations
     */
    public function showSensitiveVerifyForm()
    {
        $user = Auth::user();
        
        if (!$user || !$user->has2FAEnabled()) {
            return redirect()->route('dashboard')->with('error', '2FA tidak aktif.');
        }
        
        return view('auth.2fa-verify-sensitive');
    }
    
    /**
     * Verify 2FA for sensitive operations
     */
    public function verifySensitive(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
        ]);
        
        $user = Auth::user();
        
        if (!$user || !$user->has2FAEnabled()) {
            return redirect()->route('login');
        }
        
        $code = str_replace(' ', '', $request->code);
        $isValidCode = false;
        $method = '';
        
        // Try TOTP code first
        if (strlen($code) === 6 && is_numeric($code)) {
            $isValidCode = $user->verify2FACode($code);
            $method = 'TOTP';
        }
        
        // Try backup code if TOTP failed
        if (!$isValidCode && strlen($code) === 8) {
            $isValidCode = $user->useBackupCode($code);
            $method = 'backup_code';
        }
        
        if (!$isValidCode) {
            return back()->withErrors([
                'code' => 'Kode verifikasi tidak valid.'
            ]);
        }
        
        // Mark as verified for sensitive operations
        session(['sensitive_2fa_verified_at' => now()]);
        
        SecurityLog::logEvent(
            $user->id,
            'sensitive_2fa_verification',
            'Successful 2FA verification for sensitive operation',
            'medium',
            [
                'ip' => $request->ip(),
                'method' => $method,
                'intended_url' => session('intended_sensitive_url')
            ]
        );
        
        $intendedUrl = session('intended_sensitive_url', route('dashboard'));
        session()->forget('intended_sensitive_url');
        
        return redirect($intendedUrl)
            ->with('success', 'Verifikasi 2FA berhasil. Anda dapat melanjutkan operasi.');
    }
}
