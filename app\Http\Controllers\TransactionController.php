<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\Account;
use App\Models\Category;
use App\Models\RecurringTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class TransactionController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * API endpoint for AJAX transactions data.
     */
    public function apiIndex(Request $request)
    {
        // Debug log
        \Log::info('TransactionController@apiIndex called', [
            'user_id' => Auth::id(),
            'request_params' => $request->all()
        ]);
        
        $query = Transaction::where('user_id', Auth::id())
                          ->with(['account', 'category', 'toAccount']);

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('account_id')) {
            $query->where('account_id', $request->account_id);
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('transaction_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('transaction_date', '<=', $request->date_to);
        }

        if ($request->filled('amount_min')) {
            $query->where('amount', '>=', $request->amount_min);
        }

        if ($request->filled('amount_max')) {
            $query->where('amount', '<=', $request->amount_max);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Get total count before pagination
        $total = $query->count();
        
        // Pagination
        $perPage = $request->get('per_page', 10);
        $page = $request->get('page', 1);
        $offset = ($page - 1) * $perPage;
        
        $transactions = $query->orderBy('transaction_date', 'desc')
                            ->offset($offset)
                            ->limit($perPage)
                            ->get();

        // Debug log
        \Log::info('TransactionController@apiIndex result', [
            'total_count' => $total,
            'returned_count' => $transactions->count(),
            'page' => $page,
            'per_page' => $perPage
        ]);

        return response()->json([
            'data' => $transactions,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'total' => $total,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ]);
    }

    /**
     * Display a listing of the resource (Riwayat Transaksi).
     */
    public function index(Request $request)
    {
        $query = Transaction::where('user_id', Auth::id())
                          ->with(['account', 'category', 'toAccount']);

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('account_id')) {
            $query->where('account_id', $request->account_id);
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('transaction_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('transaction_date', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%");
            });
        }

        // Debug logging untuk troubleshooting
        \Log::info('Transaction Index Debug:', [
            'user_id' => Auth::id(),
            'total_transactions' => Transaction::where('user_id', Auth::id())->count(),
            'query_count' => $query->count(),
            'request_filters' => $request->all()
        ]);
        
        $transactions = $query->orderBy('transaction_date', 'desc')
                            ->orderBy('created_at', 'desc')
                            ->paginate(15);

        $accounts = Account::where('user_id', Auth::id())->get();
        // Untuk filter, tampilkan semua kategori (aktif dan nonaktif) karena transaksi lama mungkin menggunakan kategori yang sekarang nonaktif
        $categories = Category::where('user_id', Auth::id())->get();

        // Statistics
        $totalIncome = Transaction::where('user_id', Auth::id())
                                ->where('type', 'income')
                                ->sum('amount');
        
        $totalExpense = Transaction::where('user_id', Auth::id())
                                 ->where('type', 'expense')
                                 ->sum('amount');

        $currentMonthIncome = Transaction::where('user_id', Auth::id())
                                       ->where('type', 'income')
                                       ->whereMonth('transaction_date', now()->month)
                                       ->whereYear('transaction_date', now()->year)
                                       ->sum('amount');

        $currentMonthExpense = Transaction::where('user_id', Auth::id())
                                        ->where('type', 'expense')
                                        ->whereMonth('transaction_date', now()->month)
                                        ->whereYear('transaction_date', now()->year)
                                        ->sum('amount');

        return view('transactions.index', compact(
            'transactions', 
            'accounts', 
            'categories',
            'totalIncome',
            'totalExpense',
            'currentMonthIncome',
            'currentMonthExpense'
        ));
    }

    /**
     * Show the form for creating a new resource (Tambah Transaksi).
     */
    public function create()
    {
        // Hanya ambil akun yang aktif untuk dropdown transaksi
        $accounts = Account::where('user_id', Auth::id())
                          ->where('is_active', true)
                          ->orderBy('name')
                          ->get();
        // Hanya ambil kategori yang aktif untuk dropdown transaksi
        $categories = Category::where('user_id', Auth::id())
                             ->where('is_active', true)
                             ->orderBy('type')
                             ->orderBy('name')
                             ->get();
        
        return view('transactions.create', compact('accounts', 'categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Debug logging untuk input request
        \Log::info('Transaction Store Request:', [
            'all_data' => $request->all(),
            'user_id' => Auth::id(),
            'account_id' => $request->account_id,
            'to_account_id' => $request->to_account_id,
            'category_id' => $request->category_id,
            'type' => $request->type
        ]);

        // Debug: Check if accounts exist for this user
        $accountExists = \App\Models\Account::where('id', $request->account_id)
            ->where('user_id', Auth::id())->exists();
        \Log::info('Account exists check:', [
            'account_id' => $request->account_id,
            'user_id' => Auth::id(),
            'exists' => $accountExists
        ]);

        // Debug: Check if to_account exists (for transfer)
        if ($request->type === 'transfer' && $request->to_account_id) {
            $toAccountExists = \App\Models\Account::where('id', $request->to_account_id)
                ->where('user_id', Auth::id())->exists();
            \Log::info('To Account exists check:', [
                'to_account_id' => $request->to_account_id,
                'user_id' => Auth::id(),
                'exists' => $toAccountExists
            ]);
        }

        // Debug: Check if category exists (for non-transfer)
        if ($request->type !== 'transfer' && $request->category_id) {
            $categoryExists = \App\Models\Category::where('id', $request->category_id)
                ->where('user_id', Auth::id())
                ->where('is_active', 1)->exists();
            \Log::info('Category exists check:', [
                'category_id' => $request->category_id,
                'user_id' => Auth::id(),
                'exists' => $categoryExists
            ]);
        }

        $validator = Validator::make($request->all(), [
            'type' => 'required|in:income,expense,transfer',
            'account_id' => 'required|exists:accounts,id,user_id,' . Auth::id(),
            'to_account_id' => [
                'required_if:type,transfer',
                'nullable',
                'exists:accounts,id,user_id,' . Auth::id(),
                'different:account_id'
            ],
            'category_id' => [
                'required_unless:type,transfer',
                'sometimes',
                'nullable',
                'exists:categories,id,user_id,' . Auth::id() . ',is_active,1'
            ],
            'amount' => 'required|numeric|min:0.01',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'transaction_date' => 'required|date',
            'payment_method' => 'nullable|string|max:50',
            'reference_number' => 'nullable|string|max:100',
            'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:2048',
            'tags' => 'nullable|string',
            'location' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            // Debug logging untuk validation errors
            \Log::error('Transaction Validation Failed:', [
                'errors' => $validator->errors()->toArray(),
                'input' => $request->all(),
                'user_id' => Auth::id()
            ]);
            
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput()
                           ->with('debug_info', [
                               'validation_errors' => $validator->errors()->toArray(),
                               'user_id' => Auth::id(),
                               'input_data' => $request->except(['_token', 'attachments'])
                           ]);
        }

        try {
            DB::beginTransaction();

            // Handle file uploads
            $attachments = [];
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('transactions', 'public');
                    $attachments[] = $path;
                }
            }

            // Process tags
            $tags = isset($request->tags) && $request->tags ? explode(',', $request->tags) : [];
            $tags = array_map('trim', $tags);

            $transactionData = [
                'user_id' => Auth::id(),
                'account_id' => $request->account_id,
                'to_account_id' => $request->to_account_id ?? null,
                // category_id harus null (bukan string kosong atau '?') jika transfer
                'category_id' => $request->type !== 'transfer' ? ($request->category_id ?? null) : null,
                'type' => $request->type,
                'amount' => $request->amount,
                'title' => $request->title,
                'description' => $request->description ?? '',
                'transaction_date' => $request->transaction_date,
                'payment_method' => $request->payment_method ?? 'cash',
                'reference_number' => $request->reference_number ?? null,
                'attachments' => $attachments,
                'tags' => $tags,
                'location' => $request->location ?? null,
                'currency' => 'IDR',
                'status' => 'completed',
                'processed_at' => now(),
            ];

            // Pastikan category_id benar-benar null jika transfer (untuk mencegah string kosong atau '?')
            if ($request->type === 'transfer') {
                $transactionData['category_id'] = null;
            }

            // Pastikan category_id tidak berisi string kosong atau karakter aneh
            if (array_key_exists('category_id', $transactionData) && (empty($transactionData['category_id']) || $transactionData['category_id'] === '?' || $transactionData['category_id'] === '')) {
                $transactionData['category_id'] = null;
            }
            $transaction = Transaction::create($transactionData);

            // Update account balances untuk transaksi utama (akun asal)
            $this->updateAccountBalances($transaction);

            // Jika transfer, buat transaksi lawan (akun tujuan) dan update saldo akun tujuan
            if ($request->type === 'transfer') {
                $transferData = $transactionData;
                $transferData['account_id'] = $request->to_account_id;
                $transferData['to_account_id'] = $request->account_id;
                $transferData['type'] = 'transfer'; // Tetap transfer, bukan income
                $transferData['title'] = 'Transfer dari ' . $transaction->account->name;
                $transferTujuan = Transaction::create($transferData);
                // Update saldo akun tujuan
                $this->updateAccountBalances($transferTujuan);
            }

            // Trigger real-time update events via Pusher
            try {
                // Load transaction with relationships for event data
                $transaction->load(['account', 'category']);
                
                // Broadcast new transaction event
                $eventData = [
                    'user_id' => Auth::id(),
                    'transaction' => [
                        'id' => $transaction->id,
                        'type' => $transaction->type,
                        'amount' => $transaction->amount,
                        'title' => $transaction->title,
                        'category' => $transaction->category ? $transaction->category->name : 'Transfer',
                        'account' => $transaction->account->name,
                        'time' => 'Baru saja'
                    ]
                ];
                
                // Use Pusher to broadcast the event
                $pusher = new \Pusher\Pusher(
                    config('broadcasting.connections.pusher.key'),
                    config('broadcasting.connections.pusher.secret'),
                    config('broadcasting.connections.pusher.app_id'),
                    [
                        'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                        'useTLS' => true
                    ]
                );
                
                $pusher->trigger('financial-updates', 'new-transaction', $eventData);
                \Log::info('Pusher event triggered for new transaction', $eventData);
                
            } catch (\Exception $pusherError) {
                \Log::error('Failed to trigger Pusher event: ' . $pusherError->getMessage());
            }

            DB::commit();

            return redirect()->route('transactions.index')
                           ->with('success', 'Transaksi berhasil ditambahkan!');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->back()
                           ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Transaction $transaction)
    {
        // Ensure user can only view their own transactions
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        $transaction->load(['account', 'category', 'toAccount', 'recurringTransaction']);

        return view('transactions.show', compact('transaction'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Transaction $transaction)
    {
        // Ensure user can only edit their own transactions
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        // Untuk edit, tampilkan semua akun (termasuk non-aktif) 
        // karena transaksi lama mungkin menggunakan akun yang sekarang non-aktif
        $accounts = Account::where('user_id', Auth::id())->orderBy('name')->get();
        $categories = Category::where('user_id', Auth::id())->get();
        
        return view('transactions.edit', compact('transaction', 'accounts', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Transaction $transaction)
    {
        // Ensure user can only update their own transactions
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        // Clean and preprocess the amount field
        $requestData = $request->all();
        if (isset($requestData['amount'])) {
            $amount = $requestData['amount'];
            
            // Handle Indonesian format: 5.000.000 or 5.000.000,50
            if (strpos($amount, ',') !== false) {
                // Format: 5.000.000,50 (Indonesian with decimal)
                $parts = explode(',', $amount);
                $integerPart = str_replace('.', '', $parts[0]); // Remove dots from integer part
                $decimalPart = isset($parts[1]) ? $parts[1] : '';
                $requestData['amount'] = $integerPart . ($decimalPart ? '.' . $decimalPart : '');
            } else {
                // Format: 5.000.000 (Indonesian without decimal) or 5000000 (plain number)
                // Check if there are multiple dots (Indonesian format) or single dot (US decimal)
                $dotCount = substr_count($amount, '.');
                if ($dotCount > 1) {
                    // Multiple dots = Indonesian thousands separator
                    $requestData['amount'] = str_replace('.', '', $amount);
                } else if ($dotCount == 1) {
                    // Single dot - could be decimal or thousands separator
                    $dotPos = strrpos($amount, '.');
                    $afterDot = substr($amount, $dotPos + 1);
                    
                    if (strlen($afterDot) <= 2 && ctype_digit($afterDot)) {
                        // Likely decimal (1-2 digits after dot)
                        $requestData['amount'] = $amount;
                    } else {
                        // Likely thousands separator
                        $requestData['amount'] = str_replace('.', '', $amount);
                    }
                } else {
                    // No dots - plain number
                    $requestData['amount'] = $amount;
                }
            }
            
            // Final cleanup - ensure only digits and one decimal point
            $requestData['amount'] = preg_replace('/[^\d.]/', '', $requestData['amount']);
        }

        // Debug log
        \Log::info('Transaction update request data:', [
            'user_id' => Auth::id(),
            'transaction_id' => $transaction->id,
            'original_amount' => $request->input('amount'),
            'processed_amount' => $requestData['amount'],
            'amount_is_numeric' => is_numeric($requestData['amount']),
            'original_data' => $request->all(),
            'processed_data' => $requestData
        ]);

        // Prepare validation rules
        $rules = [
            'type' => 'required|in:income,expense,transfer',
            'account_id' => 'required|exists:accounts,id,user_id,' . Auth::id(),
            'amount' => 'required|numeric|min:0.01',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'transaction_date' => 'required|date',
            'payment_method' => 'nullable|string|max:50',
            'reference_number' => 'nullable|string|max:100',
            'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:2048',
            'tags' => 'nullable|string',
            'location' => 'nullable|string|max:255',
        ];

        // Add conditional rules based on transaction type
        if ($requestData['type'] === 'transfer') {
            $rules['to_account_id'] = [
                'required',
                'exists:accounts,id,user_id,' . Auth::id(),
                'different:account_id'
            ];
        } else {
            // For income/expense, category is required
            $rules['category_id'] = [
                'required',
                'exists:categories,id,user_id,' . Auth::id()
            ];
        }

        $validator = Validator::make($requestData, $rules, [
            'account_id.exists' => 'Akun yang dipilih tidak valid atau tidak tersedia.',
            'category_id.exists' => 'Kategori yang dipilih tidak valid atau tidak tersedia.',
            'category_id.required' => 'Kategori harus dipilih untuk transaksi pemasukan atau pengeluaran.',
            'amount.numeric' => 'Jumlah harus berupa angka yang valid.',
            'amount.min' => 'Jumlah minimal adalah 0.01.',
            'to_account_id.required' => 'Akun tujuan harus dipilih untuk transfer.',
            'to_account_id.exists' => 'Akun tujuan yang dipilih tidak valid.',
            'to_account_id.different' => 'Akun tujuan harus berbeda dengan akun asal.',
        ]);

        if ($validator->fails()) {
            \Log::error('Validation failed for transaction update:', [
                'errors' => $validator->errors()->toArray(),
                'input' => $requestData
            ]);
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        try {
            DB::beginTransaction();

            // Revert old balance changes
            $this->revertAccountBalances($transaction);

            // Handle file uploads
            $attachments = $transaction->attachments ?? [];
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('transactions', 'public');
                    $attachments[] = $path;
                }
            }

            // Process tags
            $tags = isset($requestData['tags']) && $requestData['tags'] ? explode(',', $requestData['tags']) : [];
            $tags = array_map('trim', $tags);

            $transaction->update([
                'account_id' => $requestData['account_id'],
                'to_account_id' => $requestData['to_account_id'] ?? null,
                'category_id' => $requestData['type'] !== 'transfer' ? ($requestData['category_id'] ?? null) : null,
                'type' => $requestData['type'],
                'amount' => $requestData['amount'],
                'title' => $requestData['title'],
                'description' => $requestData['description'] ?? '',
                'transaction_date' => $requestData['transaction_date'],
                'payment_method' => $requestData['payment_method'] ?? 'cash',
                'reference_number' => $requestData['reference_number'] ?? null,
                'attachments' => $attachments,
                'tags' => $tags,
                'location' => $requestData['location'] ?? null,
            ]);

            // Apply new balance changes
            $this->updateAccountBalances($transaction);

            DB::commit();

            return redirect()->route('transactions.index')
                           ->with('success', 'Transaksi berhasil diperbarui!');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->back()
                           ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, Transaction $transaction)
    {
        // Ensure user can only delete their own transactions
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        try {
            DB::beginTransaction();

            // Revert account balance changes
            $this->revertAccountBalances($transaction);

            // Delete attachments
            if ($transaction->attachments) {
                foreach ($transaction->attachments as $attachment) {
                    Storage::disk('public')->delete($attachment);
                }
            }

            $transaction->delete();

            DB::commit();

            // Return JSON response for AJAX
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Transaksi berhasil dihapus!'
                ]);
            }

            return redirect()->route('transactions.index')
                           ->with('success', 'Transaksi berhasil dihapus!');

        } catch (\Exception $e) {
            DB::rollBack();
            
            \Log::error('Transaction delete error: ' . $e->getMessage());
            
            // Return JSON response for AJAX
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage()
                ], 500);
            }
            
            return redirect()->route('transactions.index')
                           ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Show transfer form (Transfer Antar Akun).
     */
    public function transfer()
    {
        // Hanya ambil akun yang aktif untuk transfer antar akun
        $accounts = Account::where('user_id', Auth::id())
                          ->where('is_active', true)
                          ->orderBy('name')
                          ->get();
        
        return view('transactions.transfer', compact('accounts'));
    }

    /**
     * Process transfer.
     */
    public function processTransfer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from_account_id' => 'required|exists:accounts,id|different:to_account_id',
            'to_account_id' => 'required|exists:accounts,id',
            'amount' => 'required|numeric|min:0.01',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'transaction_date' => 'required|date',
            'reference_number' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        try {
            DB::beginTransaction();

            $fromAccount = Account::find($request->from_account_id);
            $toAccount = Account::find($request->to_account_id);

            // Create outgoing transaction
            $outTransaction = Transaction::create([
                'user_id' => Auth::id(),
                'account_id' => $request->from_account_id,
                'to_account_id' => $request->to_account_id,
                'type' => 'transfer',
                'amount' => $request->amount,
                'title' => $request->title,
                'description' => $request->description,
                'transaction_date' => $request->transaction_date,
                'reference_number' => $request->reference_number,
                'currency' => 'IDR',
                'status' => 'completed',
                'processed_at' => now(),
            ]);

            // Create incoming transaction
            Transaction::create([
                'user_id' => Auth::id(),
                'account_id' => $request->to_account_id,
                'to_account_id' => $request->from_account_id,
                'type' => 'income',
                'amount' => $request->amount,
                'title' => 'Transfer dari ' . $fromAccount->name,
                'description' => $request->description,
                'transaction_date' => $request->transaction_date,
                'reference_number' => $request->reference_number,
                'currency' => 'IDR',
                'status' => 'completed',
                'processed_at' => now(),
            ]);

            // Update account balances
            $fromAccount->decrement('current_balance', $request->amount);
            $toAccount->increment('current_balance', $request->amount);

            DB::commit();

            return redirect()->route('transactions.index')
                           ->with('success', 'Transfer berhasil diproses!');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->back()
                           ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Show recurring transactions (Transaksi Berulang).
     */
    public function recurring()
    {
        $recurringTransactions = RecurringTransaction::where('user_id', Auth::id())
                                                   ->with(['account', 'category'])
                                                   ->orderBy('created_at', 'desc')
                                                   ->paginate(10);

        return view('transactions.recurring', compact('recurringTransactions'));
    }

    /**
     * Show import form (Import Data).
     */
    public function import()
    {
        $accounts = Account::where('user_id', Auth::id())->get();
        
        return view('transactions.import', compact('accounts'));
    }

    /**
     * Process import.
     */
    public function processImport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,xlsx|max:5120',
            'account_id' => 'required|exists:accounts,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        try {
            // This is a placeholder for CSV/Excel import logic
            // You would typically use a package like maatwebsite/excel here
            
            return redirect()->route('transactions.index')
                           ->with('success', 'Data berhasil diimport!');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Update account balances based on transaction.
     */
    private function updateAccountBalances(Transaction $transaction)
    {
        $account = Account::find($transaction->account_id);

        if ($transaction->type === 'income') {
            $account->increment('current_balance', $transaction->amount);
        } elseif ($transaction->type === 'expense') {
            $account->decrement('current_balance', $transaction->amount);
        } elseif ($transaction->type === 'transfer') {
            // transaksi utama: account_id = akun asal, to_account_id = akun tujuan (saldo akun asal berkurang)
            // transaksi lawan: account_id = akun tujuan, to_account_id = akun asal (saldo akun tujuan bertambah)
            if ($transaction->to_account_id == $account->id) {
                // Transaksi lawan (akun tujuan), saldo bertambah
                $account->increment('current_balance', $transaction->amount);
            } else {
                // Transaksi utama (akun asal), saldo berkurang
                $account->decrement('current_balance', $transaction->amount);
            }
        }
    }

    /**
     * Revert account balances when updating/deleting transaction.
     */
    private function revertAccountBalances(Transaction $transaction)
    {
        $account = Account::find($transaction->account_id);
        
        if ($transaction->type === 'income') {
            $account->decrement('current_balance', $transaction->amount);
        } elseif ($transaction->type === 'expense') {
            $account->increment('current_balance', $transaction->amount);
        }
    }

    /**
     * Get transactions data for API/AJAX calls.
     */
    public function debugTransactions()
    {
        $transactions = Transaction::where('user_id', Auth::id())
                                 ->with(['account', 'category', 'toAccount'])
                                 ->orderBy('transaction_date', 'desc')
                                 ->get();
        
        return response()->json([
            'user_id' => Auth::id(),
            'total_count' => $transactions->count(),
            'transactions' => $transactions->map(function($t) {
                return [
                    'id' => $t->id,
                    'title' => $t->title,
                    'type' => $t->type,
                    'amount' => $t->amount,
                    'transaction_date' => $t->transaction_date,
                    'account_name' => $t->account ? $t->account->name : null,
                    'category_name' => $t->category ? $t->category->name : null,
                ];
            })
        ]);
    }

    /**
     * Debug method to show transaction data directly
     */
    public function debugView()
    {
        $transactions = Transaction::where('user_id', Auth::id())
                                 ->with(['account', 'category', 'toAccount'])
                                 ->orderBy('transaction_date', 'desc')
                                 ->get();
        
        return view('transactions.debug', compact('transactions'));
    }

    /**
     * Get statistics data for API/AJAX calls.
     */
    public function apiStatistics(Request $request)
    {
        $query = Transaction::where('user_id', Auth::id());

        // Apply same filters as main query
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        if ($request->filled('account_id')) {
            $query->where('account_id', $request->account_id);
        }
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('transaction_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('transaction_date', '<=', $request->date_to);
        }
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $totalIncome = (clone $query)->where('type', 'income')->sum('amount');
        $totalExpense = (clone $query)->where('type', 'expense')->sum('amount');
        $totalTransfer = (clone $query)->where('type', 'transfer')->sum('amount');

        return response()->json([
            'total_income' => $totalIncome,
            'total_expense' => $totalExpense,
            'total_transfer' => $totalTransfer,
            'net_income' => $totalIncome - $totalExpense
        ]);
    }
}
