<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Notification;
use Carbon\Carbon;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user or create one for testing
        $user = User::first();
        
        if (!$user) {
            return; // No users found, skip seeding
        }

        // Sample notifications
        $notifications = [
            [
                'type' => 'success',
                'title' => 'Transaksi Berhasil',
                'message' => 'Pembayaran sebesar Rp 150.000 untuk "Belanja Groceries" berhasil diproses.',
                'is_important' => false,
                'action_url' => '/transactions',
                'icon' => 'fas fa-check-circle',
                'created_at' => now()->subMinutes(5),
            ],
            [
                'type' => 'warning',
                'title' => 'Budget Hampir Habis',
                'message' => 'Budget kategori "Makan<PERSON> & <PERSON>uman" sudah mencapai 85% dari limit bulanan.',
                'is_important' => true,
                'action_url' => '/budget',
                'icon' => 'fas fa-exclamation-triangle',
                'created_at' => now()->subHours(2),
            ],
            [
                'type' => 'info',
                'title' => 'Laporan Mingguan Tersedia',
                'message' => 'Laporan keuangan minggu ini telah siap. Total pengeluaran: Rp 2.450.000',
                'is_important' => false,
                'action_url' => '/reports',
                'icon' => 'fas fa-chart-line',
                'created_at' => now()->subHours(6),
            ],
            [
                'type' => 'error',
                'title' => 'Login Gagal Terdeteksi',
                'message' => 'Terdeteksi 3 kali percobaan login gagal dari IP ************* dalam 10 menit terakhir.',
                'is_important' => true,
                'action_url' => '/settings/security',
                'icon' => 'fas fa-shield-alt',
                'created_at' => now()->subHours(12),
                'read_at' => now()->subHours(11), // This one is read
            ],
            [
                'type' => 'success',
                'title' => '2FA Berhasil Diaktifkan',
                'message' => 'Two-Factor Authentication telah berhasil diaktifkan untuk akun Anda. Keamanan akun meningkat!',
                'is_important' => false,
                'action_url' => '/settings/security',
                'icon' => 'fas fa-shield-check',
                'created_at' => now()->subDay(),
                'read_at' => now()->subHours(20),
            ],
            [
                'type' => 'info',
                'title' => 'Backup Otomatis Selesai',
                'message' => 'Backup data otomatis bulanan telah selesai. Data Anda aman tersimpan.',
                'is_important' => false,
                'action_url' => '/settings/backup',
                'icon' => 'fas fa-database',
                'created_at' => now()->subDays(2),
                'read_at' => now()->subDays(2)->addHours(1),
            ],
            [
                'type' => 'warning',
                'title' => 'Transaksi Besar Terdeteksi',
                'message' => 'Transaksi sebesar Rp 5.000.000 untuk "Transfer ke Tabungan" melebihi batas harian.',
                'is_important' => true,
                'action_url' => '/transactions',
                'icon' => 'fas fa-money-bill-wave',
                'created_at' => now()->subDays(3),
            ],
            [
                'type' => 'info',
                'title' => 'Goal Tercapai!',
                'message' => 'Selamat! Target tabungan "Liburan Bali" sebesar Rp 10.000.000 telah tercapai.',
                'is_important' => false,
                'action_url' => '/goals',
                'icon' => 'fas fa-trophy',
                'created_at' => now()->subDays(5),
                'read_at' => now()->subDays(5)->addHours(2),
            ],
            [
                'type' => 'error',
                'title' => 'Sync Error',
                'message' => 'Gagal melakukan sinkronisasi dengan bank. Silakan coba lagi atau hubungi support.',
                'is_important' => true,
                'action_url' => '/settings',
                'icon' => 'fas fa-sync-alt',
                'created_at' => now()->subWeek(),
                'read_at' => now()->subWeek()->addHours(1),
            ],
            [
                'type' => 'success',
                'title' => 'Akun Terverifikasi',
                'message' => 'Email Anda telah berhasil diverifikasi. Semua fitur MyMoney sekarang dapat digunakan.',
                'is_important' => false,
                'action_url' => '/dashboard',
                'icon' => 'fas fa-check-circle',
                'created_at' => now()->subWeeks(2),
                'read_at' => now()->subWeeks(2)->addMinutes(30),
            ],
        ];

        foreach ($notifications as $notificationData) {
            Notification::create([
                'user_id' => $user->id,
                'type' => $notificationData['type'],
                'title' => $notificationData['title'],
                'message' => $notificationData['message'],
                'is_important' => $notificationData['is_important'],
                'action_url' => $notificationData['action_url'] ?? null,
                'icon' => $notificationData['icon'] ?? null,
                'channel' => 'web',
                'read_at' => $notificationData['read_at'] ?? null,
                'created_at' => $notificationData['created_at'],
                'updated_at' => $notificationData['created_at'],
            ]);
        }

        $this->command->info('Sample notifications created successfully!');
    }
}
