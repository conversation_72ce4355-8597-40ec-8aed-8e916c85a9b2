<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('account_id');
            $table->unsignedBigInteger('category_id');
            $table->unsignedBigInteger('to_account_id')->nullable(); // For transfers
            $table->string('type')->default('expense'); // expense, income, transfer
            $table->decimal('amount', 15, 2);
            $table->string('title');
            $table->text('description')->nullable();
            $table->date('transaction_date');
            $table->string('payment_method')->nullable(); // cash, card, bank_transfer, e_wallet, etc
            $table->string('reference_number')->nullable();
            $table->json('attachments')->nullable(); // receipts, photos
            $table->json('tags')->nullable();
            $table->string('location')->nullable();
            $table->decimal('exchange_rate', 8, 4)->default(1.0000);
            $table->string('currency', 3)->default('IDR');
            $table->boolean('is_recurring')->default(false);
            $table->unsignedBigInteger('recurring_transaction_id')->nullable();
            $table->string('status')->default('completed'); // pending, completed, cancelled
            $table->timestamp('processed_at')->nullable();
            $table->json('metadata')->nullable(); // Additional data for specific transaction types
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['user_id', 'transaction_date']);
            $table->index(['account_id', 'type']);
            $table->index(['category_id', 'transaction_date']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
