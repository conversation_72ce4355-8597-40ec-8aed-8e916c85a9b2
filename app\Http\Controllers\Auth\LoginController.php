<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\SecurityLog;
use App\Models\User;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/dashboard';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }

    /**
     * Get the login username to be used by the controller.
     *
     * @return string
     */
    public function username()
    {
        return 'username';
    }    /**
     * Handle a login request to the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function login(Request $request)
    {
        $this->validateLogin($request);
        
        // Rate limiting check
        if (method_exists($this, 'hasTooManyLoginAttempts') &&
            $this->hasTooManyLoginAttempts($request)) {
            $this->fireLockoutEvent($request);
            return $this->sendLockoutResponse($request);
        }

        // Find user by username
        $user = User::where('username', $request->username)->first();
        
        if (!$user) {
            SecurityLog::logEvent(
                null,
                'login_failed',
                'Login attempt dengan username tidak terdaftar: ' . $request->username,
                'medium',
                [
                    'username' => $request->username,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]
            );
            
            $this->incrementLoginAttempts($request);
            throw ValidationException::withMessages([
                'username' => ['Username atau password salah.'],
            ]);
        }

        // Check if account is locked
        if ($user->isLocked()) {
            SecurityLog::logEvent(
                $user->id,
                'login_blocked',
                'Login attempt pada akun yang terkunci',
                'high',
                [
                    'ip' => $request->ip(),
                    'locked_until' => $user->locked_until,
                    'user_agent' => $request->userAgent()
                ]
            );
            
            throw ValidationException::withMessages([
                'username' => ['Akun Anda terkunci karena terlalu banyak percobaan login yang gagal. Silakan coba lagi pada ' . $user->locked_until->format('d/m/Y H:i') . '.'],
            ]);
        }

        // Check if account is active
        if (!$user->is_active) {
            SecurityLog::logEvent(
                $user->id,
                'login_blocked',
                'Login attempt pada akun non-aktif',
                'medium'
            );
            
            // Check if email is verified or not
            if (!$user->hasVerifiedEmail()) {
                throw ValidationException::withMessages([
                    'username' => ['Akun Anda belum aktif. Silakan verifikasi email Anda terlebih dahulu.'],
                ]);
            } else {
                // If email is verified but account is not active, auto-activate the account
                $user->is_active = true;
                $user->save();
                
                SecurityLog::logEvent(
                    $user->id,
                    'account_auto_activated',
                    'Akun diaktifkan otomatis karena email sudah terverifikasi',
                    'low'
                );
            }
        }

        // Check password
        if (!Hash::check($request->password, $user->password)) {
            $user->incrementLoginAttempts();
            
            SecurityLog::logEvent(
                $user->id,
                'login_failed',
                'Login gagal dengan password salah',
                'medium',
                [
                    'ip' => $request->ip(),
                    'failed_attempts' => $user->login_attempts,
                    'user_agent' => $request->userAgent()
                ]
            );
            
            $this->incrementLoginAttempts($request);
            throw ValidationException::withMessages([
                'username' => ['Username atau password salah.'],
            ]);
        }

        // Check for concurrent sessions (prevent double login)
        if ($user->hasActiveSession() && $user->current_session_id !== $request->session()->getId()) {
            SecurityLog::logEvent(
                $user->id,
                'concurrent_login_attempt',
                'Percobaan login ganda terdeteksi',
                'high',
                [
                    'ip' => $request->ip(),
                    'current_session' => $user->current_session_id,
                    'new_session' => $request->session()->getId()
                ]
            );
            
            throw ValidationException::withMessages([
                'username' => ['Akun ini sudah login di tempat lain. Silakan logout terlebih dahulu atau tunggu session expire.'],
            ]);
        }

        // Check if password needs to be changed
        if ($user->passwordNeedsChange()) {
            SecurityLog::logEvent(
                $user->id,
                'password_change_required',
                'Login dengan password yang perlu diganti',
                'medium'
            );
            
            // Store user ID in session for password change process
            $request->session()->put('password_change_required', $user->id);
            
            return redirect()->route('password.change.form')
                ->with('warning', 'Password Anda sudah lama tidak diganti. Silakan ganti password untuk keamanan akun.');
        }

        // Login success
        $this->clearLoginAttempts($request);
        $user->resetLoginAttempts();
        
        // Update session information
        $user->updateSessionId($request->session()->getId());
        
        Auth::login($user, $request->filled('remember'));
        
        SecurityLog::logEvent(
            $user->id,
            'login_success',
            'Login berhasil',
            'low',
            [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'session_id' => $request->session()->getId()
            ]
        );

        $request->session()->regenerate();

        return $this->sendLoginResponse($request);
    }    /**
     * Validate the user login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateLogin(Request $request)
    {
        $request->validate([
            'username' => 'required|string|max:255',
            'password' => 'required|string|min:8',
            'captcha' => 'required|numeric|min:0|max:9999',
        ], [
            'username.required' => 'Username wajib diisi.',
            'password.required' => 'Password wajib diisi.',
            'password.min' => 'Password minimal 8 karakter.',
            'captcha.required' => 'Captcha wajib diisi.',
            'captcha.numeric' => 'Captcha harus berupa angka.',
        ]);
        
        // Verify captcha with detailed logging
        $sessionCaptcha = session('captcha');
        $userCaptcha = $request->captcha;
        
        if (!$sessionCaptcha) {
            SecurityLog::logEvent(
                null,
                'captcha_session_missing',
                'Captcha session not found during login',
                'medium',
                [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'username' => $request->username
                ]
            );
            
            throw ValidationException::withMessages([
                'captcha' => ['Session captcha tidak ditemukan. Silakan refresh halaman.'],
            ]);
        }
        
        if ($userCaptcha != $sessionCaptcha) {
            SecurityLog::logEvent(
                null,
                'captcha_validation_failed',
                'Captcha validation failed during login',
                'medium',
                [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'username' => $request->username,
                    'provided_captcha' => $userCaptcha,
                    'expected_captcha' => $sessionCaptcha
                ]
            );
            
            throw ValidationException::withMessages([
                'captcha' => ['Captcha tidak valid. Silakan coba lagi.'],
            ]);
        }
    }/**
     * Log the user out of the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        if (Auth::check()) {
            $user = Auth::user();
            
            // Clear user session
            $user->clearSession();
            
            SecurityLog::logEvent(
                Auth::id(),
                'logout',
                'User logout',
                'low',
                [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'session_id' => $request->session()->getId()
                ]
            );
        }

        $this->guard()->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        if ($response = $this->loggedOut($request)) {
            return $response;
        }

        // Add success flash message for logout
        $request->session()->flash('success', 'Anda telah berhasil logout. Sampai jumpa!');

        return $request->wantsJson()
            ? new JsonResponse([], 204)
            : redirect('/');
    }

    /**
     * Send the response after the user was authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    protected function sendLoginResponse(Request $request)
    {
        $request->session()->regenerate();

        $this->clearLoginAttempts($request);

        if ($response = $this->authenticated($request, $this->guard()->user())) {
            return $response;
        }        return $request->wantsJson()
                    ? new JsonResponse([], 204)
                    : redirect()->intended($this->redirectPath());
    }

    /**
     * The user has been authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  mixed  $user
     * @return mixed
     */
    protected function authenticated(Request $request, $user)
    {
        // Log successful authentication
        SecurityLog::logEvent(
            $user->id,
            'authentication_success',
            'User successfully authenticated and redirected',
            'low',
            [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'redirect_to' => $this->redirectPath()
            ]
        );

        return null; // Continue with default behavior
    }
}
