<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\RecurringTransaction;
use App\Models\User;
use App\Models\Account;
use App\Models\Category;
use Carbon\Carbon;

class RecurringTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Find the demo user
        $user = User::where('email', '<EMAIL>')->first();
        
        if (!$user) {
            $this->command->error('Demo user not found. Please run UserSeeder first.');
            return;
        }

        // Get user's accounts and categories
        $accounts = Account::where('user_id', $user->id)->get();
        $categories = Category::where('user_id', $user->id)->get();

        if ($accounts->isEmpty() || $categories->isEmpty()) {
            $this->command->error('No accounts or categories found. Please run AccountSeeder and CategorySeeder first.');
            return;
        }

        $salaryAccount = $accounts->where('name', 'Bank BCA')->first() ?? $accounts->first();
        $savingsAccount = $accounts->where('name', 'Tabungan')->first() ?? $accounts->skip(1)->first();
        
        $salaryCategory = $categories->where('name', 'Gaji')->first();
        $utilityCategory = $categories->where('name', 'Listrik & Air')->first();
        $foodCategory = $categories->where('name', 'Makanan & Minuman')->first();
        $transportCategory = $categories->where('name', 'Transport')->first();
        $savingsCategory = $categories->where('name', 'Tabungan')->first();

        $recurringTransactions = [
            // Monthly Income
            [
                'user_id' => $user->id,
                'account_id' => $salaryAccount->id,
                'category_id' => $salaryCategory?->id,
                'type' => 'income',
                'amount' => 7500000,
                'title' => 'Gaji Bulanan',
                'description' => 'Gaji pokok + tunjangan',
                'frequency' => 'monthly',
                'start_date' => now()->startOfMonth(),
                'next_run_date' => now()->startOfMonth()->addMonth(),
                'is_active' => true,
                'payment_method' => 'bank_transfer',
                'tags' => ['gaji', 'income', 'bulanan'],
            ],
            
            // Monthly Bills
            [
                'user_id' => $user->id,
                'account_id' => $salaryAccount->id,
                'category_id' => $utilityCategory?->id,
                'type' => 'expense',
                'amount' => 450000,
                'title' => 'Tagihan Listrik',
                'description' => 'Tagihan listrik PLN bulanan',
                'frequency' => 'monthly',
                'start_date' => now()->day(20),
                'next_run_date' => now()->day(20)->addMonth(),
                'is_active' => true,
                'payment_method' => 'bank_transfer',
                'tags' => ['listrik', 'tagihan', 'bulanan'],
            ],
            
            [
                'user_id' => $user->id,
                'account_id' => $salaryAccount->id,
                'category_id' => $utilityCategory?->id,
                'type' => 'expense',
                'amount' => 150000,
                'title' => 'Tagihan Air PDAM',
                'description' => 'Tagihan air PDAM bulanan',
                'frequency' => 'monthly',
                'start_date' => now()->day(25),
                'next_run_date' => now()->day(25)->addMonth(),
                'is_active' => true,
                'payment_method' => 'bank_transfer',
                'tags' => ['air', 'tagihan', 'bulanan'],
            ],
            
            // Monthly Savings
            [
                'user_id' => $user->id,
                'account_id' => $salaryAccount->id,
                'to_account_id' => $savingsAccount?->id,
                'category_id' => $savingsCategory?->id,
                'type' => 'expense',
                'amount' => 1500000,
                'title' => 'Transfer Tabungan Bulanan',
                'description' => 'Transfer otomatis ke tabungan setiap bulan',
                'frequency' => 'monthly',
                'start_date' => now()->day(1),
                'next_run_date' => now()->day(1)->addMonth(),
                'is_active' => true,
                'payment_method' => 'bank_transfer',
                'tags' => ['tabungan', 'transfer', 'bulanan'],
            ],
            
            // Weekly Expenses
            [
                'user_id' => $user->id,
                'account_id' => $salaryAccount->id,
                'category_id' => $foodCategory?->id,
                'type' => 'expense',
                'amount' => 200000,
                'title' => 'Belanja Mingguan',
                'description' => 'Belanja kebutuhan sehari-hari',
                'frequency' => 'weekly',
                'start_date' => now()->startOfWeek()->addDay(5), // Saturday
                'next_run_date' => now()->startOfWeek()->addDay(5)->addWeek(),
                'is_active' => true,
                'payment_method' => 'cash',
                'tags' => ['belanja', 'kebutuhan', 'mingguan'],
            ],
            
            [
                'user_id' => $user->id,
                'account_id' => $salaryAccount->id,
                'category_id' => $transportCategory?->id,
                'type' => 'expense',
                'amount' => 100000,
                'title' => 'Isi Bensin Mingguan',
                'description' => 'Pengisian bahan bakar kendaraan',
                'frequency' => 'weekly',
                'start_date' => now()->startOfWeek(),
                'next_run_date' => now()->startOfWeek()->addWeek(),
                'is_active' => true,
                'payment_method' => 'cash',
                'tags' => ['bensin', 'transport', 'mingguan'],
            ],
            
            // Daily Expenses
            [
                'user_id' => $user->id,
                'account_id' => $salaryAccount->id,
                'category_id' => $foodCategory?->id,
                'type' => 'expense',
                'amount' => 25000,
                'title' => 'Makan Siang Kantor',
                'description' => 'Biaya makan siang di kantor',
                'frequency' => 'daily',
                'start_date' => now(),
                'next_run_date' => now()->addDay(),
                'is_active' => false, // Not active for demo
                'payment_method' => 'cash',
                'tags' => ['makan', 'kantor', 'harian'],
            ],
            
            // Yearly Expenses
            [
                'user_id' => $user->id,
                'account_id' => $salaryAccount->id,
                'category_id' => $categories->where('name', 'Asuransi')->first()?->id,
                'type' => 'expense',
                'amount' => 2400000,
                'title' => 'Premi Asuransi Kesehatan',
                'description' => 'Pembayaran premi asuransi kesehatan tahunan',
                'frequency' => 'yearly',
                'start_date' => now()->startOfYear()->addMonth(),
                'next_run_date' => now()->startOfYear()->addYear()->addMonth(),
                'is_active' => true,
                'payment_method' => 'bank_transfer',
                'tags' => ['asuransi', 'kesehatan', 'tahunan'],
            ],
            
            // Bonus Income
            [
                'user_id' => $user->id,
                'account_id' => $salaryAccount->id,
                'category_id' => $categories->where('name', 'Bonus')->first()?->id,
                'type' => 'income',
                'amount' => 3000000,
                'title' => 'Bonus Kinerja',
                'description' => 'Bonus kinerja triwulanan',
                'frequency' => 'monthly', // Every 3 months but using monthly for demo
                'start_date' => now()->addMonths(3),
                'next_run_date' => now()->addMonths(3),
                'is_active' => true,
                'payment_method' => 'bank_transfer',
                'tags' => ['bonus', 'kinerja', 'triwulanan'],
                'metadata' => [
                    'quarterly' => true,
                    'performance_based' => true
                ]
            ],
        ];

        foreach ($recurringTransactions as $data) {
            RecurringTransaction::create($data);
            $this->command->info("Created recurring transaction: {$data['title']}");
        }

        $this->command->info('Recurring transactions seeded successfully!');
    }
}
