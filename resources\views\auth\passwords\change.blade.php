@extends('layouts.dashboard')

@section('title', 'Ubah Password')

@section('page-title', 'Ubah Password')

@section('content')
<div class="main-container">
    <div class="row">
        <!-- Change Password Form -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lock me-2"></i>
                        Form Ubah Password
                    </h5>
                </div>
                <div class="card-body p-4">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Te<PERSON><PERSON><PERSON> k<PERSON><PERSON>han:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('password.change') }}" id="changePasswordForm">
                        @csrf
                        
                        <!-- Current Password -->
                        <div class="mb-4">
                            <label for="current_password" class="form-label fw-semibold">
                                <i class="fas fa-unlock-alt text-muted me-1"></i>
                                Password Saat Ini
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control @error('current_password') is-invalid @enderror" 
                                       id="current_password" 
                                       name="current_password" 
                                       placeholder="Masukkan password saat ini"
                                       required>
                                <button class="btn btn-outline-secondary toggle-password" 
                                        type="button" 
                                        data-target="current_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                                @error('current_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <small class="form-text text-muted">
                                Konfirmasi password lama untuk keamanan
                            </small>
                        </div>

                        <!-- New Password -->
                        <div class="mb-4">
                            <label for="new_password" class="form-label fw-semibold">
                                <i class="fas fa-key text-muted me-1"></i>
                                Password Baru
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control @error('new_password') is-invalid @enderror" 
                                       id="new_password" 
                                       name="new_password" 
                                       placeholder="Masukkan password baru"
                                       required>
                                <button class="btn btn-outline-secondary toggle-password" 
                                        type="button" 
                                        data-target="new_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                                @error('new_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Password Strength Indicator -->
                            <div class="password-strength mt-2">
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="strength-text text-muted">Masukkan password untuk melihat kekuatan</small>
                            </div>
                            <!-- Password Requirements -->
                            <div class="password-requirements mt-3">
                                <small class="text-muted d-block mb-2">Password harus memenuhi:</small>
                                <div class="requirement-list">
                                    <div class="requirement" data-requirement="length">
                                        <i class="fas fa-times text-danger"></i>
                                        <span>Minimal 8 karakter</span>
                                    </div>
                                    <div class="requirement" data-requirement="uppercase">
                                        <i class="fas fa-times text-danger"></i>
                                        <span>Huruf besar (A-Z)</span>
                                    </div>
                                    <div class="requirement" data-requirement="lowercase">
                                        <i class="fas fa-times text-danger"></i>
                                        <span>Huruf kecil (a-z)</span>
                                    </div>
                                    <div class="requirement" data-requirement="number">
                                        <i class="fas fa-times text-danger"></i>
                                        <span>Angka (0-9)</span>
                                    </div>
                                    <div class="requirement" data-requirement="special">
                                        <i class="fas fa-times text-danger"></i>
                                        <span>Karakter khusus (!@#$%^&*)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Confirm New Password -->
                        <div class="mb-4">
                            <label for="new_password_confirmation" class="form-label fw-semibold">
                                <i class="fas fa-check-double text-muted me-1"></i>
                                Konfirmasi Password Baru
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control @error('new_password_confirmation') is-invalid @enderror" 
                                       id="new_password_confirmation" 
                                       name="new_password_confirmation" 
                                       placeholder="Ulangi password baru"
                                       required>
                                <button class="btn btn-outline-secondary toggle-password" 
                                        type="button" 
                                        data-target="new_password_confirmation">
                                    <i class="fas fa-eye"></i>
                                </button>
                                @error('new_password_confirmation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="password-match mt-2" style="display: none;">
                                <small class="match-text"></small>
                            </div>
                        </div>

                        <!-- Security Options -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-cog text-primary me-2"></i>
                                    Opsi Keamanan Tambahan
                                </h6>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="logout_other_devices" 
                                           name="logout_other_devices"
                                           checked>
                                    <label class="form-check-label" for="logout_other_devices">
                                        <strong>Logout dari semua perangkat lain</strong>
                                        <br>
                                        <small class="text-muted">
                                            Akan mengeluarkan akun Anda dari perangkat lain untuk keamanan
                                        </small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 justify-content-end">
                            <a href="{{ route('settings.profile') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <span class="spinner-border spinner-border-sm me-2" 
                                      role="status" 
                                      style="display: none;"></span>
                                <i class="fas fa-save me-1"></i>
                                Ubah Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Security Info Sidebar -->
        <div class="col-lg-4">
            <!-- Password Tips -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Tips Password Aman
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Gunakan kombinasi huruf, angka, dan simbol
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Minimal 8 karakter, lebih panjang lebih baik
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Hindari informasi personal (nama, tanggal lahir)
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Jangan gunakan password yang sama untuk akun lain
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            Ubah password secara berkala
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Security Status -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Status Keamanan
                    </h6>
                </div>
                <div class="card-body">
                    <div class="security-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Password terakhir diubah</span>
                            <span class="badge bg-primary">
                                {{ auth()->user()->password_changed_at ? auth()->user()->password_changed_at->diffForHumans() : 'Tidak diketahui' }}
                            </span>
                        </div>
                    </div>
                    <div class="security-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Login terakhir</span>
                            <span class="badge bg-success">
                                {{ auth()->user()->last_login_at ? auth()->user()->last_login_at->diffForHumans() : 'Sekarang' }}
                            </span>
                        </div>
                    </div>
                    <div class="security-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Keamanan akun</span>
                            <span class="badge bg-warning">
                                <i class="fas fa-star me-1"></i>
                                Baik
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        Butuh Bantuan?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-3">
                        Jika Anda mengalami masalah atau lupa password, 
                        hubungi administrator sistem.
                    </p>
                    <div class="d-grid">
                        <a href="#" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-envelope me-1"></i>
                            Hubungi Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* CSS khusus untuk halaman ubah password */
/* Base styles yang menyesuaikan dengan tema dashboard */
.main-container {
    background-color: transparent;
    color: inherit;
    min-height: 100vh;
    padding: 2rem;
}

.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: inherit;
}

.page-description {
    color: inherit;
    font-size: 0.95rem;
    opacity: 0.8;
}

.alert-icon {
    opacity: 0.8;
}

/* Card styling untuk semua tema */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
    font-weight: 500;
}

.card-title {
    font-weight: 600;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Form controls dan input styling */
.form-control {
    border-radius: 0.375rem;
}

.form-control::placeholder {
    opacity: 0.7;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
}

/* Small text styling */
.form-text, small, .small {
    font-size: 0.875rem;
}

/* Button styling */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

/* Password strength dan requirements */
.password-strength .progress {
    border-radius: 3px;
}

.password-strength .progress-bar {
    transition: all 0.3s ease;
}

.password-requirements {
    font-size: 0.875rem;
}

.requirement {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.requirement i {
    width: 16px;
    margin-right: 0.5rem;
    font-size: 0.75rem;
}

.requirement.valid i {
    color: #198754 !important;
}

.password-match small.text-success {
    color: #198754 !important;
}

.password-match small.text-danger {
    color: #dc3545 !important;
}

.security-item {
    font-size: 0.875rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.security-item:last-child {
    border-bottom: none;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #5a6fd8;
}

.alert {
    border-radius: 0.5rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .main-container {
        padding: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .d-flex.gap-2 {
        flex-direction: column;
    }
    
    .d-flex.gap-2 .btn {
        width: 100%;
    }
}

/* Tema-specific overrides untuk elemen khusus halaman ini */
body[data-theme="synth-wave"] .bg-gradient-primary {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
}

body[data-theme="solarized-dark"] .bg-gradient-primary {
    background: linear-gradient(45deg, #268bd2, #2aa198) !important;
}


}

.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--bs-body-color, #212529);
}

.page-description {
    color: var(--bs-secondary, #6c757d);
    font-size: 0.95rem;
}

.alert-icon {
    opacity: 0.8;
}

/* Card styling untuk semua tema */
.card {
    border: none;
    border-radius: 0.5rem;
    background-color: var(--bs-body-bg, #ffffff);
    color: var(--bs-body-color, #212529);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
    font-weight: 500;
    border-bottom: 1px solid var(--bs-border-color, #dee2e6);
}

.card-title {
    color: inherit !important;
    font-weight: 600;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Form controls dan input styling */
.form-control {
    border-radius: 0.375rem;
    background-color: var(--bs-body-bg, #ffffff);
    border-color: var(--bs-border-color, #dee2e6);
    color: var(--bs-body-color, #212529);
}

.form-control::placeholder {
    color: var(--bs-secondary, #6c757d);
    opacity: 0.7;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background-color: var(--bs-body-bg, #ffffff);
    color: var(--bs-body-color, #212529);
}

.form-label {
    color: var(--bs-body-color, #212529);
    font-weight: 600;
}

/* Small text styling */
.form-text, small, .small {
    color: var(--bs-secondary, #6c757d) !important;
    font-size: 0.875rem;
}

/* Button styling */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-outline-secondary {
    color: var(--bs-secondary, #6c757d);
    border-color: var(--bs-border-color, #dee2e6);
}

.btn-outline-secondary:hover {
    background-color: var(--bs-secondary, #6c757d);
    border-color: var(--bs-secondary, #6c757d);
}

/* Password strength dan requirements */
.password-strength .progress {
    border-radius: 3px;
    background-color: var(--bs-light, #f8f9fa);
}

.password-strength .progress-bar {
    transition: all 0.3s ease;
}

.password-requirements {
    font-size: 0.875rem;
}

.requirement {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
    color: var(--bs-body-color, #212529);
}

.requirement i {
    width: 16px;
    margin-right: 0.5rem;
    font-size: 0.75rem;
}

.requirement.valid i {
    color: #198754 !important;
}

.password-match small.text-success {
    color: #198754 !important;
}

.password-match small.text-danger {
    color: #dc3545 !important;
}

.security-item {
    font-size: 0.875rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--bs-border-color, #e9ecef);
    color: var(--bs-body-color, #212529);
}

.security-item:last-child {
    border-bottom: none;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #5a6fd8;
}

.breadcrumb-item.active {
    color: var(--bs-secondary, #6c757d);
}

.alert {
    border-radius: 0.5rem;
}

.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    border-color: rgba(13, 202, 240, 0.2);
    color: var(--bs-info, #0dcaf0);
}

/* Tema Dark Mode - Mengikuti dashboard utama */
body[data-theme="dark"] {
    background-color: #1a202c !important;
}

body[data-theme="dark"] .main-container {
    background-color: #1a202c;
    color: #e2e8f0;
}

body[data-theme="dark"] .card {
    background-color: #2d3748;
    color: #e2e8f0;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
}

body[data-theme="dark"] .card-header {
    border-bottom-color: #4a5568;
}

body[data-theme="dark"] .card-title {
    color: #e2e8f0 !important;
}

body[data-theme="dark"] .form-control {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
}

body[data-theme="dark"] .form-control::placeholder {
    color: #a0aec0;
    opacity: 0.8;
}

body[data-theme="dark"] .form-control:focus {
    background-color: #4a5568;
    color: #e2e8f0;
    border-color: #667eea;
}

body[data-theme="dark"] .page-title {
    color: #e2e8f0;
}

body[data-theme="dark"] .page-description {
    color: #a0aec0;
}

body[data-theme="dark"] .form-label {
    color: #e2e8f0;
}

body[data-theme="dark"] .form-text,
body[data-theme="dark"] small,
body[data-theme="dark"] .small {
    color: #a0aec0 !important;
}

body[data-theme="dark"] .requirement {
    color: #e2e8f0;
}

body[data-theme="dark"] .security-item {
    color: #e2e8f0;
    border-bottom-color: #4a5568;
}

body[data-theme="dark"] .breadcrumb-item.active {
    color: #a0aec0;
}

body[data-theme="dark"] .password-strength .progress {
    background-color: #4a5568;
}

/* Tema Solarized Dark - Sesuai dengan dashboard */
body[data-theme="solarized-dark"] {
    background: linear-gradient(135deg, #002b36 0%, #073642 100%) !important;
}

body[data-theme="solarized-dark"] .main-container {
    background: linear-gradient(135deg, #002b36 0%, #073642 100%);
    color: #839496;
}

body[data-theme="solarized-dark"] .card {
    background: #fdf6e3;
    color: #586e75;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

body[data-theme="solarized-dark"] .card-header {
    border-bottom-color: #eee8d5;
}

body[data-theme="solarized-dark"] .card-title {
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .form-control {
    background-color: #fdf6e3;
    border-color: #eee8d5;
    color: #586e75;
}

body[data-theme="solarized-dark"] .form-control::placeholder {
    color: #93a1a1;
    opacity: 0.8;
}

body[data-theme="solarized-dark"] .form-control:focus {
    background-color: #fdf6e3;
    color: #586e75;
    border-color: #268bd2;
}

body[data-theme="solarized-dark"] .page-title {
    color: #586e75;
}

body[data-theme="solarized-dark"] .page-description {
    color: #657b83;
}

body[data-theme="solarized-dark"] .form-label {
    color: #586e75;
}

body[data-theme="solarized-dark"] .form-text,
body[data-theme="solarized-dark"] small,
body[data-theme="solarized-dark"] .small {
    color: #657b83 !important;
}

body[data-theme="solarized-dark"] .requirement {
    color: #586e75;
}

body[data-theme="solarized-dark"] .security-item {
    color: #586e75;
    border-bottom-color: #eee8d5;
}

body[data-theme="solarized-dark"] .breadcrumb-item.active {
    color: #657b83;
}

body[data-theme="solarized-dark"] .password-strength .progress {
    background-color: #eee8d5;
}

/* Tema Synth Wave - Sesuai dengan dashboard */
body[data-theme="synth-wave"] {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a0033 50%, #000000 100%) !important;
}

body[data-theme="synth-wave"] .main-container {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a0033 50%, #000000 100%);
    color: #ffffff;
}

body[data-theme="synth-wave"] .card {
    background: #1a1a1a;
    color: #ffffff;
    border: 1px solid #ff00ff;
    box-shadow: 0 0 20px rgba(255, 0, 255, 0.2);
}

body[data-theme="synth-wave"] .card-header {
    border-bottom: 1px solid #ff00ff !important;
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
}

body[data-theme="synth-wave"] .card-title {
    color: white !important;
    text-shadow: none;
}

body[data-theme="synth-wave"] .form-control {
    background-color: #1a1a1a;
    border-color: #ff00ff;
    color: #ffffff;
}

body[data-theme="synth-wave"] .form-control::placeholder {
    color: #00ffff;
    opacity: 0.7;
}

body[data-theme="synth-wave"] .form-control:focus {
    background-color: #1a1a1a;
    color: #ffffff;
    border-color: #00ffff;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25);
}

body[data-theme="synth-wave"] .page-title {
    color: #ff00ff;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
}

body[data-theme="synth-wave"] .page-description {
    color: #00ffff;
}

body[data-theme="synth-wave"] .form-label {
    color: #ff00ff;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
}

body[data-theme="synth-wave"] .form-text,
body[data-theme="synth-wave"] small,
body[data-theme="synth-wave"] .small {
    color: #00ffff !important;
}

body[data-theme="synth-wave"] .requirement {
    color: #ffffff;
}

body[data-theme="synth-wave"] .security-item {
    color: #ffffff;
    border-bottom-color: #ff00ff;
}

body[data-theme="synth-wave"] .breadcrumb-item.active {
    color: #00ffff;
}

body[data-theme="synth-wave"] .password-strength .progress {
    background-color: #1a1a1a;
    border: 1px solid #ff00ff;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .page-title {
        font-size: 1.5rem;
    }
    
    .d-flex.gap-2 {
        flex-direction: column;
    }
    
    .d-flex.gap-2 .btn {
        width: 100%;
    }
}

/* Sidebar styling telah dihapus - menggunakan tema dari dashboard utama */
</style>
@endpush

@push('scripts')
<script>
   
    background-color: var(--bs-body-bg);
    color: var(--bs-body-color);
    min-height: 100vh;
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    document.querySelectorAll('.toggle-password').forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const input = document.getElementById(targetId);
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // Password strength checker
    const newPasswordInput = document.getElementById('new_password');
    const progressBar = document.querySelector('.password-strength .progress-bar');
    const strengthText = document.querySelector('.strength-text');
    const requirements = document.querySelectorAll('.requirement');

    newPasswordInput.addEventListener('input', function() {
        const password = this.value;
        checkPasswordStrength(password);
        checkPasswordRequirements(password);
    });

    function checkPasswordStrength(password) {
        let strength = 0;
        let strengthText = '';
        let strengthClass = '';

        if (password.length >= 8) strength += 20;
        if (password.match(/[a-z]/)) strength += 20;
        if (password.match(/[A-Z]/)) strength += 20;
        if (password.match(/[0-9]/)) strength += 20;
        if (password.match(/[^a-zA-Z0-9]/)) strength += 20;

        if (strength === 0) {
            strengthText = 'Masukkan password untuk melihat kekuatan';
            strengthClass = '';
        } else if (strength <= 40) {
            strengthText = 'Lemah';
            strengthClass = 'bg-danger';
        } else if (strength <= 60) {
            strengthText = 'Sedang';
            strengthClass = 'bg-warning';
        } else if (strength <= 80) {
            strengthText = 'Kuat';
            strengthClass = 'bg-info';
        } else {
            strengthText = 'Sangat Kuat';
            strengthClass = 'bg-success';
        }

        progressBar.style.width = strength + '%';
        progressBar.className = 'progress-bar ' + strengthClass;
        document.querySelector('.strength-text').textContent = strengthText;
    }

    function checkPasswordRequirements(password) {
        const checks = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /[0-9]/.test(password),
            special: /[^a-zA-Z0-9]/.test(password)
        };

        requirements.forEach(req => {
            const requirement = req.getAttribute('data-requirement');
            const icon = req.querySelector('i');
            
            if (checks[requirement]) {
                icon.classList.remove('fa-times', 'text-danger');
                icon.classList.add('fa-check', 'text-success');
                req.classList.add('valid');
            } else {
                icon.classList.remove('fa-check', 'text-success');
                icon.classList.add('fa-times', 'text-danger');
                req.classList.remove('valid');
            }
        });
    }

    // Password confirmation checker
    const confirmPasswordInput = document.getElementById('new_password_confirmation');
    const passwordMatchDiv = document.querySelector('.password-match');
    const matchText = document.querySelector('.match-text');

    confirmPasswordInput.addEventListener('input', function() {
        const password = newPasswordInput.value;
        const confirmPassword = this.value;

        if (confirmPassword.length > 0) {
            passwordMatchDiv.style.display = 'block';
            
            if (password === confirmPassword) {
                matchText.textContent = '✓ Password cocok';
                matchText.className = 'text-success';
            } else {
                matchText.textContent = '✗ Password tidak cocok';
                matchText.className = 'text-danger';
            }
        } else {
            passwordMatchDiv.style.display = 'none';
        }
    });

    // Form submission
    const form = document.getElementById('changePasswordForm');
    const submitBtn = document.getElementById('submitBtn');
    const spinner = submitBtn.querySelector('.spinner-border');

    form.addEventListener('submit', function(e) {
        submitBtn.disabled = true;
        spinner.style.display = 'inline-block';
        
        // Re-enable after 3 seconds in case of error
        setTimeout(() => {
            submitBtn.disabled = false;
            spinner.style.display = 'none';
        }, 3000);
    });
});
</script>
@endpush
@endsection
