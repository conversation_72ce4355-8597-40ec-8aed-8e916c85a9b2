<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class VerifyTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:verify-user {username}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manually verify a test user email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $username = $this->argument('username');
        
        $user = User::where('username', $username)->first();
        
        if (!$user) {
            $this->error("User '{$username}' not found!");
            return 1;
        }
        
        if ($user->hasVerifiedEmail()) {
            $this->info("User '{$username}' is already verified.");
            return 0;
        }
        
        $user->update([
            'email_verified_at' => now(),
            'is_active' => true,
            'verification_token' => null,
        ]);
        
        $this->info("User '{$username}' has been verified and activated successfully!");
        $this->info("Username: {$user->username}");
        $this->info("Email: {$user->email}");
        $this->info("Status: Verified and Active");
        
        return 0;
    }
}
