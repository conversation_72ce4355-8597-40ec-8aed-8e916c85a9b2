<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Budget extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category_id',
        'name',
        'description',
        'amount',
        'period_type', // monthly, yearly, weekly
        'start_date',
        'end_date',
        'is_active',
        'alert_percentage', // Alert when spending reaches this % of budget
        'color',
        'icon',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
        'alert_percentage' => 'integer',
    ];

    /**
     * Get the user that owns the budget
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category for this budget
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get transactions related to this budget
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'category_id', 'category_id')
                   ->where('type', 'expense')
                   ->where('user_id', $this->user_id)
                   ->whereBetween('transaction_date', [$this->start_date, $this->end_date]);
    }

    /**
     * Calculate spent amount for this budget period
     */
    public function getSpentAmountAttribute(): float
    {
        return $this->transactions()->sum('amount') ?? 0;
    }

    /**
     * Calculate remaining amount
     */
    public function getRemainingAmountAttribute(): float
    {
        return $this->amount - $this->spent_amount;
    }

    /**
     * Calculate percentage used
     */
    public function getPercentageUsedAttribute(): float
    {
        if ($this->amount <= 0) return 0;
        return ($this->spent_amount / $this->amount) * 100;
    }

    /**
     * Check if budget is over limit
     */
    public function getIsOverBudgetAttribute(): bool
    {
        return $this->spent_amount > $this->amount;
    }

    /**
     * Check if budget needs alert
     */
    public function getNeedsAlertAttribute(): bool
    {
        return $this->percentage_used >= $this->alert_percentage;
    }

    /**
     * Get status color based on usage
     */
    public function getStatusColorAttribute(): string
    {
        $percentage = $this->percentage_used;
        
        if ($percentage >= 100) return 'danger';
        if ($percentage >= $this->alert_percentage) return 'warning';
        if ($percentage >= 70) return 'info';
        return 'success';
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'Rp ' . number_format($this->amount, 0, ',', '.');
    }

    /**
     * Get formatted spent amount
     */
    public function getFormattedSpentAmountAttribute(): string
    {
        return 'Rp ' . number_format($this->spent_amount, 0, ',', '.');
    }

    /**
     * Get formatted remaining amount
     */
    public function getFormattedRemainingAmountAttribute(): string
    {
        return 'Rp ' . number_format($this->remaining_amount, 0, ',', '.');
    }

    /**
     * Scope for active budgets
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for current period budgets
     */
    public function scopeCurrentPeriod($query)
    {
        $now = now();
        return $query->where('start_date', '<=', $now)
                     ->where('end_date', '>=', $now);
    }

    /**
     * Scope for user budgets
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
