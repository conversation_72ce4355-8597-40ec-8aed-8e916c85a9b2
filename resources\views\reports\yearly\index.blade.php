@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON><PERSON>')

@push('styles')
<style>
    .report-card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
        transition: all 0.3s ease;
    }
    
    .report-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .metric-value {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        word-break: break-word;
        line-height: 1.2;
    }
    
    @media (min-width: 1400px) {
        .metric-value {
            font-size: 2.2rem;
        }
    }
    
    @media (max-width: 768px) {
        .metric-value {
            font-size: 1.5rem;
        }
    }
    
    .metric-label {
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .comparison-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .comparison-positive {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }
    
    .comparison-negative {
        background-color: rgba(220, 53, 69, 0.2);
        color: #dc3545;
    }
    
    .year-selector {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
    }
    
    .year-selector:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .chart-container {
        position: relative;
        height: 400px;
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .summary-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .summary-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        border: none;
        padding: 1rem;
    }
    
    .summary-table td {
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        vertical-align: middle;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 2rem;
    }
    
    .export-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .export-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.25rem 0.5rem rgba(40, 167, 69, 0.3);
        color: white;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-calendar mr-2 text-primary"></i>
                        Laporan Tahunan
                    </h1>
                    <p class="text-muted mb-0">Analisis komprehensif keuangan tahunan Anda</p>
                </div>
                <div class="d-flex align-items-center">
                    <select id="yearSelector" class="year-selector mr-3">
                        @for($i = date('Y'); $i >= date('Y') - 5; $i--)
                            <option value="{{ $i }}" {{ $i == date('Y') ? 'selected' : '' }}>{{ $i }}</option>
                        @endfor
                    </select>
                    <button class="btn export-btn" onclick="exportYearlyReport()">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="reportContent">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="metric-card">
                    <div class="metric-value" id="totalIncome">Rp 0</div>
                    <div class="metric-label">Total Pemasukan</div>
                    <div class="mt-2">
                        <span id="incomeComparison" class="comparison-badge">-</span>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="metric-card" style="background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);">
                    <div class="metric-value" id="totalExpense">Rp 0</div>
                    <div class="metric-label">Total Pengeluaran</div>
                    <div class="mt-2">
                        <span id="expenseComparison" class="comparison-badge">-</span>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="metric-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <div class="metric-value" id="netSavings">Rp 0</div>
                    <div class="metric-label">Tabungan Bersih</div>
                    <div class="mt-2">
                        <span id="savingsComparison" class="comparison-badge">-</span>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="metric-card" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
                    <div class="metric-value" id="savingsRate">0%</div>
                    <div class="metric-label">Tingkat Tabungan</div>
                    <div class="mt-2">
                        <span id="rateComparison" class="comparison-badge">-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-xl-8">
                <div class="chart-container">
                    <h5 class="mb-4">
                        <i class="fas fa-chart-line mr-2 text-primary"></i>
                        Trend Bulanan <span id="currentYear">{{ date('Y') }}</span>
                    </h5>
                    <canvas id="monthlyTrendChart"></canvas>
                </div>
            </div>
            <div class="col-xl-4">
                <div class="chart-container">
                    <h5 class="mb-4">
                        <i class="fas fa-chart-pie mr-2 text-primary"></i>
                        Distribusi Kategori
                    </h5>
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Quarterly Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="summary-table">
                    <h5 class="p-3 mb-0 bg-primary text-white">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Ringkasan Kuartalan
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Kuartal</th>
                                    <th>Pemasukan</th>
                                    <th>Pengeluaran</th>
                                    <th>Selisih</th>
                                    <th>Tingkat Tabungan</th>
                                    <th>Transaksi</th>
                                </tr>
                            </thead>
                            <tbody id="quarterlyData">
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        Memuat data...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Categories -->
        <div class="row">
            <div class="col-xl-6">
                <div class="summary-table">
                    <h5 class="p-3 mb-0 bg-success text-white">
                        <i class="fas fa-arrow-up mr-2"></i>
                        Top Kategori Pemasukan
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Kategori</th>
                                    <th>Jumlah</th>
                                    <th>Persentase</th>
                                </tr>
                            </thead>
                            <tbody id="topIncomeCategories">
                                <tr>
                                    <td colspan="3" class="text-center text-muted py-4">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        Memuat data...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-xl-6">
                <div class="summary-table">
                    <h5 class="p-3 mb-0 bg-danger text-white">
                        <i class="fas fa-arrow-down mr-2"></i>
                        Top Kategori Pengeluaran
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Kategori</th>
                                    <th>Jumlah</th>
                                    <th>Persentase</th>
                                </tr>
                            </thead>
                            <tbody id="topExpenseCategories">
                                <tr>
                                    <td colspan="3" class="text-center text-muted py-4">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        Memuat data...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let monthlyTrendChart, categoryChart;
    
    // Initialize charts
    initializeCharts();
    
    // Load initial data
    loadYearlyReport(document.getElementById('yearSelector').value);
    
    // Year selector change handler
    document.getElementById('yearSelector').addEventListener('change', function() {
        loadYearlyReport(this.value);
    });
    
    function initializeCharts() {
        // Monthly Trend Chart
        const monthlyCtx = document.getElementById('monthlyTrendChart').getContext('2d');
        monthlyTrendChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'],
                datasets: [{
                    label: 'Pemasukan',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Pengeluaran',
                    data: [],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
        
        // Category Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        categoryChart = new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: [
                        '#667eea', '#764ba2', '#f093fb', '#f5576c',
                        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                        '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    }
    
    function loadYearlyReport(year) {
        document.getElementById('currentYear').textContent = year;
        
        fetch(`{{ route('reports.api.yearly') }}?year=${year}`, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                console.log('API Response:', data); // Debug log
                if (data.success) {
                    updateSummaryCards(data.data);
                    updateCharts(data.data);
                    updateQuarterlyData(data.data.quarterly);
                    updateTopCategories(data.data.categories);
                } else {
                    showError('Gagal memuat data laporan tahunan');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Terjadi kesalahan saat memuat data');
            });
    }
    
    function updateSummaryCards(data) {
        document.getElementById('totalIncome').textContent = formatCurrency(data.summary.total_income);
        document.getElementById('totalExpense').textContent = formatCurrency(data.summary.total_expense);
        document.getElementById('netSavings').textContent = formatCurrency(data.summary.net_savings);
        document.getElementById('savingsRate').textContent = data.summary.savings_rate + '%';
        
        // Update comparison badges
        updateComparisonBadge('incomeComparison', data.summary.income_growth);
        updateComparisonBadge('expenseComparison', data.summary.expense_growth);
        updateComparisonBadge('savingsComparison', data.summary.savings_growth);
        updateComparisonBadge('rateComparison', data.summary.rate_change);
    }
    
    function updateComparisonBadge(elementId, value) {
        const element = document.getElementById(elementId);
        const isPositive = value >= 0;
        
        element.className = `comparison-badge ${isPositive ? 'comparison-positive' : 'comparison-negative'}`;
        element.innerHTML = `<i class="fas fa-arrow-${isPositive ? 'up' : 'down'} mr-1"></i>${Math.abs(value)}%`;
    }
    
    function updateCharts(data) {
        // Update monthly trend chart
        const monthlyData = data.monthly_data || [];
        if (monthlyTrendChart && monthlyTrendChart.data.datasets && monthlyTrendChart.data.datasets.length >= 2) {
            monthlyTrendChart.data.datasets[0].data = monthlyData.map(m => m.income || 0);
            monthlyTrendChart.data.datasets[1].data = monthlyData.map(m => m.expense || 0);
            monthlyTrendChart.update();
        }
        
        // Update category chart - convert categories object to array format
        const expenseCategories = data.categories?.expense || {};
        const categoryEntries = Object.entries(expenseCategories).slice(0, 8);
        if (categoryChart && categoryChart.data.datasets && categoryChart.data.datasets.length > 0) {
            categoryChart.data.labels = categoryEntries.map(([name, amount]) => name);
            categoryChart.data.datasets[0].data = categoryEntries.map(([name, amount]) => amount);
            categoryChart.update();
        }
    }
    
    function updateQuarterlyData(quarterly) {
        const tbody = document.getElementById('quarterlyData');
        tbody.innerHTML = '';
        
        // Convert quarterly object to array
        const quarterArray = Object.entries(quarterly || {}).map(([quarter, data]) => ({
            quarter: quarter,
            ...data
        }));
        
        quarterArray.forEach((quarterData, index) => {
            const row = document.createElement('tr');
            const savingsRate = quarterData.income > 0 ? 
                ((quarterData.income - quarterData.expense) / quarterData.income * 100).toFixed(1) : 0;
            
            row.innerHTML = `
                <td><strong>${quarterData.quarter}</strong></td>
                <td class="text-success font-weight-bold">${formatCurrency(quarterData.income)}</td>
                <td class="text-danger font-weight-bold">${formatCurrency(quarterData.expense)}</td>
                <td class="${quarterData.balance >= 0 ? 'text-success' : 'text-danger'} font-weight-bold">
                    ${formatCurrency(quarterData.balance)}
                </td>
                <td><span class="badge badge-info">${savingsRate}%</span></td>
                <td><span class="badge badge-secondary">-</span></td>
            `;
            tbody.appendChild(row);
        });
    }
    
    function updateTopCategories(categories) {
        // Update top income categories
        const incomeTable = document.getElementById('topIncomeCategories');
        incomeTable.innerHTML = '';
        
        const incomeCategories = categories?.income || {};
        const totalIncome = Object.values(incomeCategories).reduce((sum, amount) => sum + amount, 0);
        const incomeEntries = Object.entries(incomeCategories).slice(0, 5);
        
        incomeEntries.forEach(([name, amount]) => {
            const percentage = totalIncome > 0 ? ((amount / totalIncome) * 100).toFixed(1) : 0;
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${name}</td>
                <td class="text-success font-weight-bold">${formatCurrency(amount)}</td>
                <td><span class="badge badge-success">${percentage}%</span></td>
            `;
            incomeTable.appendChild(row);
        });
        
        // Update top expense categories
        const expenseTable = document.getElementById('topExpenseCategories');
        expenseTable.innerHTML = '';
        
        const expenseCategories = categories?.expense || {};
        const totalExpense = Object.values(expenseCategories).reduce((sum, amount) => sum + amount, 0);
        const expenseEntries = Object.entries(expenseCategories).slice(0, 5);
        
        expenseEntries.forEach(([name, amount]) => {
            const percentage = totalExpense > 0 ? ((amount / totalExpense) * 100).toFixed(1) : 0;
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${name}</td>
                <td class="text-danger font-weight-bold">${formatCurrency(amount)}</td>
                <td><span class="badge badge-danger">${percentage}%</span></td>
            `;
            expenseTable.appendChild(row);
        });
    }
    
    function showError(message) {
        // Show toast or alert
        console.error(message);
        alert(message); // Simple alert for now
    }
    
    function formatCurrency(amount) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }
    
    // Export functionality
    window.exportYearlyReport = function() {
        const year = document.getElementById('yearSelector').value;
        window.open(`/api/reports/yearly/export?year=${year}&format=pdf`, '_blank');
    };
});
</script>
@endpush
