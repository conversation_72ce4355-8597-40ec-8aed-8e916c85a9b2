<!DOCTYPE html>
<html lang="id" data-theme="{{ auth()->user()->theme ?? 'light' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Dashboard') - My Money | Personal Finance Management</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Advanced Personal Finance Management System with Real-time Analytics">
    <meta name="author" content="My Money Development Team">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon Suite -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon-16x16.png') }}">
    
    <!-- Preconnect untuk Performance Optimization -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//unpkg.com">
    
    <!-- Critical CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" 
          integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Font Awesome Pro -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" 
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">
    
    <!-- Premium Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    
    <!-- Animation Libraries -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Advanced UI Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/perfect-scrollbar@1.5.5/css/perfect-scrollbar.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.css">
    
    <!-- Chart & Visualization Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css">
    
    <!-- Main Stylesheet -->
    <style>
        /* ===== CSS CUSTOM PROPERTIES ===== */
        :root {
            /* Layout Dimensions */
            --sidebar-width: 320px;
            --sidebar-collapsed-width: 85px;
            --topbar-height: 85px;
            --footer-height: 60px;
            --content-padding: 35px;
            --card-padding: 30px;
            
            /* Advanced Color Palette */
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-200: #bfdbfe;
            --primary-300: #93c5fd;
            --primary-400: #60a5fa;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-800: #1e40af;
            --primary-900: #1e3a8a;
            
            --secondary-50: #f8fafc;
            --secondary-100: #f1f5f9;
            --secondary-200: #e2e8f0;
            --secondary-300: #cbd5e1;
            --secondary-400: #94a3b8;
            --secondary-500: #64748b;
            --secondary-600: #475569;
            --secondary-700: #334155;
            --secondary-800: #1e293b;
            --secondary-900: #0f172a;
            
            /* Semantic Colors */
            --success-color: #10b981;
            --success-light: #34d399;
            --success-dark: #059669;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --warning-dark: #d97706;
            --danger-color: #ef4444;
            --danger-light: #f87171;
            --danger-dark: #dc2626;
            --info-color: #3b82f6;
            --info-light: #60a5fa;
            --info-dark: #2563eb;
            
            /* Advanced Gradients */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --gradient-warning: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --gradient-danger: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --gradient-dark: linear-gradient(135deg, #232526 0%, #414345 100%);
            --gradient-aurora: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            
            /* Glass Morphism */
            --glass-bg: rgba(255, 255, 255, 0.12);
            --glass-bg-hover: rgba(255, 255, 255, 0.18);
            --glass-bg-active: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.15);
            --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            --glass-shadow-hover: 0 12px 48px rgba(31, 38, 135, 0.45);
            
            /* Typography */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-secondary: 'Poppins', sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, monospace;
            
            /* Text Colors */
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --text-light: #d1d5db;
            --text-white: #ffffff;
            --text-black: #000000;
            
            /* Border & Radius */
            --border-color: rgba(255, 255, 255, 0.15);
            --border-color-light: rgba(255, 255, 255, 0.08);
            --border-radius-xs: 4px;
            --border-radius-sm: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 20px;
            --border-radius-2xl: 24px;
            --border-radius-full: 9999px;
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
            --shadow-glow-hover: 0 0 30px rgba(102, 126, 234, 0.5);
            
            /* Animations & Transitions */
            --transition-ultra-fast: all 0.1s ease;
            --transition-fast: all 0.2s ease;
            --transition-medium: all 0.3s ease;
            --transition-slow: all 0.5s ease;
            --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --transition-elastic: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            
            /* Z-Index Stack */
            --z-base: 0;
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-fixed: 1030;
            --z-modal-backdrop: 1040;
            --z-modal: 1050;
            --z-popover: 1060;
            --z-tooltip: 1070;
            --z-max: 2147483647;
        }

        /* ===== DARK THEMES ===== */
        [data-theme="solarized-dark"] {
            --primary-500: #2aa198;
            --primary-600: #268bd2;
            --secondary-500: #859900;
            --gradient-primary: linear-gradient(135deg, #002b36 0%, #073642 100%);
            --gradient-secondary: linear-gradient(135deg, #2aa198 0%, #268bd2 100%);
            --gradient-aurora: linear-gradient(135deg, #2aa198 0%, #268bd2 25%, #859900 50%, #cb4b16 75%, #dc322f 100%);
            --text-primary: #fdf6e3;
            --text-secondary: #93a1a1;
            --text-muted: #586e75;
            --text-white: #fdf6e3;
            --text-black: #002b36;
            --glass-bg: rgba(0, 43, 54, 0.8);
            --glass-bg-hover: rgba(0, 43, 54, 0.9);
            --border-color: rgba(147, 161, 161, 0.2);
            --border-color-light: rgba(147, 161, 161, 0.1);
            --shadow-glow: 0 0 30px rgba(42, 161, 152, 0.4);
            --shadow-glow-hover: 0 0 30px rgba(42, 161, 152, 0.6);
            --success-color: #859900;
            --warning-color: #cb4b16;
            --danger-color: #dc322f;
            --info-color: #268bd2;
            --gradient-success: linear-gradient(135deg, #859900 0%, #b58900 100%);
            --gradient-warning: linear-gradient(135deg, #cb4b16 0%, #dc322f 100%);
            --gradient-danger: linear-gradient(135deg, #dc322f 0%, #d33682 100%);
            --gradient-info: linear-gradient(135deg, #268bd2 0%, #6c71c4 100%);
        }

        [data-theme="synth-wave"] {
            --primary-500: #ff006e;
            --primary-600: #8338ec;
            --secondary-500: #06ffa5;
            --gradient-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            --gradient-secondary: linear-gradient(135deg, #06ffa5 0%, #3a86ff 100%);
            --gradient-aurora: linear-gradient(135deg, #ff006e 0%, #8338ec 25%, #06ffa5 50%, #ffbe0b 75%, #fb5607 100%);
            --text-primary: #ffffff;
            --text-secondary: #b794f6;
            --text-muted: #805ad5;
            --text-white: #ffffff;
            --text-black: #0a0a0a;
            --glass-bg: rgba(26, 26, 46, 0.8);
            --glass-bg-hover: rgba(26, 26, 46, 0.9);
            --border-color: rgba(255, 0, 110, 0.3);
            --border-color-light: rgba(255, 0, 110, 0.1);
            --shadow-glow: 0 0 30px rgba(255, 0, 110, 0.4);
            --shadow-glow-hover: 0 0 30px rgba(255, 0, 110, 0.6);
            --success-color: #06ffa5;
            --warning-color: #ffbe0b;
            --danger-color: #ff006e;
            --info-color: #3a86ff;
            --gradient-success: linear-gradient(135deg, #06ffa5 0%, #00d4aa 100%);
            --gradient-warning: linear-gradient(135deg, #ffbe0b 0%, #fb5607 100%);
            --gradient-danger: linear-gradient(135deg, #ff006e 0%, #8338ec 100%);
            --gradient-info: linear-gradient(135deg, #3a86ff 0%, #8338ec 100%);
        }

        /* ===== GLOBAL RESET & BASE STYLES ===== */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: var(--font-primary);
            font-weight: 400;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--gradient-primary);
            min-height: 100vh;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* ===== CUSTOM SCROLLBAR ===== */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--glass-bg);
            border-radius: var(--border-radius-full);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gradient-primary);
            border-radius: var(--border-radius-full);
            border: 2px solid transparent;
            background-clip: padding-box;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gradient-secondary);
            background-clip: padding-box;
        }

        ::-webkit-scrollbar-corner {
            background: var(--glass-bg);
        }

        /* Firefox Scrollbar */
        * {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-500) var(--glass-bg);
        }

        /* ===== SELECTION STYLES ===== */
        ::selection {
            background: var(--primary-500);
            color: white;
        }

        ::-moz-selection {
            background: var(--primary-500);
            color: white;
        }

        /* ===== FOCUS STYLES ===== */
        :focus {
            outline: 2px solid var(--primary-500);
            outline-offset: 2px;
        }

        :focus:not(:focus-visible) {
            outline: none;
        }

        /* ===== APP CONTAINER ===== */
        .app-container {
            display: flex;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* ===== SIDEBAR COMPONENT ===== */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border-right: 1px solid var(--border-color);
            z-index: var(--z-fixed);
            transition: var(--transition-smooth);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-aurora);
            opacity: 0.05;
            pointer-events: none;
        }

        /* Sidebar Header */
        .sidebar-header {
            padding: 25px 20px;
            border-bottom: 1px solid var(--border-color);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: var(--topbar-height);
        }

        .sidebar-logo {
            font-family: var(--font-secondary);
            font-size: 1.8rem;
            font-weight: 800;
            background: var(--gradient-aurora);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: var(--transition-smooth);
            text-align: center;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar.collapsed .sidebar-logo .logo-text {
            opacity: 0;
            transform: translateX(-20px);
        }

        .logo-icon {
            font-size: 2rem;
            filter: drop-shadow(0 0 10px rgba(102, 126, 234, 0.5));
        }

        .sidebar-toggle {
            position: absolute;
            top: 50%;
            right: -15px;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            background: var(--gradient-primary);
            border: none;
            border-radius: var(--border-radius-full);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-bounce);
            z-index: 10;
            box-shadow: var(--shadow-lg);
        }

        .sidebar-toggle:hover {
            transform: translateY(-50%) scale(1.15);
            box-shadow: var(--shadow-glow-hover);
        }

        .sidebar-toggle:active {
            transform: translateY(-50%) scale(0.95);
        }

        /* Sidebar Navigation */
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 20px 0;
        }

        .sidebar-menu {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .menu-section {
            margin-bottom: 35px;
        }

        .menu-section-title {
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            color: var(--text-light);
            padding: 0 25px 15px;
            margin-bottom: 15px;
            position: relative;
            opacity: 0.8;
        }

        .menu-section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 25px;
            right: 25px;
            height: 1px;
            background: linear-gradient(90deg, var(--primary-500), transparent);
        }

        .sidebar.collapsed .menu-section-title {
            opacity: 0;
            pointer-events: none;
            transform: translateX(-20px);
        }

        /* Menu Items */
        .menu-item {
            position: relative;
            margin-bottom: 8px;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 16px 25px;
            color: var(--text-white);
            text-decoration: none;
            transition: var(--transition-smooth);
            cursor: pointer;
            border-radius: 0 25px 25px 0;
            margin-right: 20px;
            position: relative;
            overflow: hidden;
            font-weight: 500;
        }

        .menu-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
            transition: var(--transition-fast);
        }

        .menu-link:hover::before {
            left: 100%;
        }

        .menu-link:hover {
            background: var(--glass-bg-hover);
            color: var(--text-white);
            text-decoration: none;
            transform: translateX(8px);
            box-shadow: var(--shadow-lg);
        }

        .menu-link.active {
            background: var(--gradient-primary);
            color: var(--text-white);
            box-shadow: var(--shadow-glow);
            transform: translateX(8px);
        }

        .menu-link.active::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 50%;
            background: var(--secondary-500);
            border-radius: 2px 0 0 2px;
            box-shadow: 0 0 10px var(--secondary-500);
        }

        .menu-icon {
            width: 24px;
            height: 24px;
            margin-right: 18px;
            text-align: center;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-smooth);
            flex-shrink: 0;
        }

        .menu-link:hover .menu-icon {
            transform: scale(1.1);
            filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
        }

        .menu-text {
            font-weight: 500;
            font-size: 0.95rem;
            transition: var(--transition-smooth);
            white-space: nowrap;
            flex: 1;
        }

        .sidebar.collapsed .menu-text {
            opacity: 0;
            pointer-events: none;
            transform: translateX(-15px);
        }

        .menu-arrow {
            margin-left: auto;
            transition: var(--transition-smooth);
            font-size: 0.85rem;
            opacity: 0.7;
        }

        .sidebar.collapsed .menu-arrow {
            opacity: 0;
            pointer-events: none;
            transform: translateX(10px);
        }

        .menu-badge {
            background: var(--gradient-danger);
            color: white;
            font-size: 0.7rem;
            padding: 3px 8px;
            border-radius: var(--border-radius-full);
            margin-left: auto;
            font-weight: 600;
            animation: pulse 2s infinite;
            box-shadow: var(--shadow-sm);
        }

        .sidebar.collapsed .menu-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            margin: 0;
            width: 8px;
            height: 8px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0;
        }

        /* Submenu Styles */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: var(--transition-smooth);
            background: rgba(0, 0, 0, 0.15);
            margin-right: 20px;
            border-radius: 0 20px 20px 0;
            backdrop-filter: blur(10px);
        }

        .submenu.active {
            max-height: 600px;
            padding: 12px 0;
        }

        .submenu-item {
            position: relative;
        }

        .submenu-link {
            display: flex;
            align-items: center;
            padding: 12px 25px 12px 55px;
            color: rgba(255, 255, 255, 0.85);
            text-decoration: none;
            transition: var(--transition-smooth);
            font-size: 0.9rem;
            position: relative;
            font-weight: 450;
        }

        .submenu-link::before {
            content: '';
            position: absolute;
            left: 40px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: var(--secondary-500);
            border-radius: 50%;
            transition: var(--transition-smooth);
        }

        .submenu-link:hover {
            color: var(--text-white);
            text-decoration: none;
            background: var(--glass-bg-hover);
            transform: translateX(8px);
        }

        .submenu-link:hover::before {
            transform: translateY(-50%) scale(1.8);
            box-shadow: 0 0 12px var(--secondary-500);
            background: var(--secondary-500);
        }

        .submenu-link.active {
            background: var(--glass-bg-active);
            color: var(--text-white);
            font-weight: 600;
        }

        .submenu-link.active::before {
            background: var(--secondary-500);
            box-shadow: 0 0 15px var(--secondary-500);
            transform: translateY(-50%) scale(2);
        }

        .submenu-icon {
            width: 18px;
            margin-right: 12px;
            text-align: center;
            font-size: 1rem;
            opacity: 0.8;
        }

        /* ===== MAIN CONTENT AREA ===== */
        .main-content {
            margin-left: var(--sidebar-width);
            flex: 1;
            min-height: 100vh;
            transition: var(--transition-smooth);
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.02);
        }

        .sidebar.collapsed + .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* ===== TOPBAR COMPONENT ===== */
        .topbar {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            height: var(--topbar-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--content-padding);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: var(--z-sticky);
            box-shadow: var(--shadow-sm);
        }

        .topbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-aurora);
            opacity: 0.03;
            pointer-events: none;
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 25px;
            flex: 1;
        }

        .page-title {
            font-family: var(--font-secondary);
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-white);
            margin: 0;
            background: var(--gradient-aurora);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .breadcrumb-item {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition-fast);
            padding: 6px 12px;
            border-radius: var(--border-radius-sm);
        }

        .breadcrumb-item:hover {
            color: var(--text-white);
            background: var(--glass-bg);
        }

        .breadcrumb-item.active {
            color: var(--secondary-500);
            background: var(--glass-bg);
            font-weight: 600;
        }

        .breadcrumb-separator {
            color: rgba(255, 255, 255, 0.4);
            font-size: 0.8rem;
        }

        .topbar-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        /* Search Component */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-input {
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-full);
            padding: 12px 50px 12px 20px;
            color: var(--text-white);
            width: 280px;
            transition: var(--transition-smooth);
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }

        .search-input:focus {
            outline: none;
            background: var(--glass-bg-hover);
            border-color: var(--primary-500);
            width: 350px;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .search-icon {
            position: absolute;
            right: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.6);
            pointer-events: none;
        }

        /* Notification Component */
        .notification-bell {
            position: relative;
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            color: var(--text-white);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 12px;
            border-radius: var(--border-radius-full);
            transition: var(--transition-smooth);
            backdrop-filter: blur(10px);
        }

        .notification-bell:hover {
            background: var(--glass-bg-hover);
            transform: scale(1.05);
            box-shadow: var(--shadow-glow);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: var(--gradient-danger);
            color: white;
            font-size: 0.7rem;
            padding: 3px 6px;
            border-radius: var(--border-radius-full);
            font-weight: 700;
            box-shadow: var(--shadow-md);
            animation: bounce 2s infinite;
        }

        /* User Profile Component */
        .user-profile {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px 18px;
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-full);
            cursor: pointer;
            transition: var(--transition-smooth);
            backdrop-filter: blur(10px);
        }

        .user-profile:hover {
            background: var(--glass-bg-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .user-avatar {
            width: 42px;
            height: 42px;
            border-radius: var(--border-radius-full);
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            border: 2px solid var(--border-color);
            overflow: hidden;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            text-align: left;
        }

        .user-name {
            font-weight: 600;
            color: var(--text-white);
            font-size: 0.95rem;
            line-height: 1.3;
        }

        .user-role {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.3;
            font-weight: 450;
        }

        /* Theme Switcher */
        .theme-switcher .dropdown-toggle {
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            color: var(--text-white);
            padding: 10px 15px;
            border-radius: var(--border-radius-sm);
            backdrop-filter: blur(10px);
        }

        .theme-switcher .dropdown-toggle:hover {
            background: var(--glass-bg-hover);
        }

        /* ===== CONTENT WRAPPER ===== */
        .content-wrapper {
            flex: 1;
            padding: var(--content-padding);
            background: rgba(255, 255, 255, 0.02);
            backdrop-filter: blur(5px);
            margin: 20px;
            border-radius: var(--border-radius-xl);
            border: 1px solid var(--border-color-light);
            position: relative;
            overflow: hidden;
        }

        .content-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-aurora);
            opacity: 0.02;
            pointer-events: none;
        }

        .content-header {
            margin-bottom: 35px;
            padding: 25px 0;
            border-bottom: 1px solid var(--border-color-light);
            position: relative;
        }

        .content-header h1 {
            font-family: var(--font-secondary);
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--text-white);
            margin-bottom: 10px;
            background: var(--gradient-aurora);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .content-header p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-size: 1.1rem;
            font-weight: 400;
        }

        /* ===== CARD COMPONENTS ===== */
        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--glass-shadow);
            transition: var(--transition-smooth);
            overflow: hidden;
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-aurora);
            opacity: 0.03;
            pointer-events: none;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: var(--glass-shadow-hover);
            border-color: rgba(255, 255, 255, 0.25);
        }

        .card-header {
            display: flex;
            align-items: center;
            padding: var(--card-padding);
            border-bottom: 1px solid var(--border-color);
            background: rgba(255, 255, 255, 0.05);
            position: relative;
        }

        .card-icon {
            width: 55px;
            height: 55px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: white;
            font-size: 1.4rem;
            box-shadow: var(--shadow-lg);
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .card-title {
            font-family: var(--font-secondary);
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text-white);
            margin: 0;
        }

        .card-body {
            padding: var(--card-padding);
            position: relative;
        }

        /* ===== FORM COMPONENTS ===== */
        .form-group {
            margin-bottom: 28px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-white);
            margin-bottom: 10px;
            font-size: 0.95rem;
            letter-spacing: 0.5px;
        }

        .form-control {
            width: 100%;
            padding: 16px 20px;
            background: var(--glass-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            color: var(--text-white);
            font-size: 0.95rem;
            transition: var(--transition-smooth);
            backdrop-filter: blur(10px);
            font-family: var(--font-primary);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-500);
            background: var(--glass-bg-hover);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 20px center;
            background-repeat: no-repeat;
            background-size: 16px 12px;
            padding-right: 55px;
            cursor: pointer;
        }

        .form-control:invalid {
            border-color: var(--danger-color);
        }

        .form-control:valid {
            border-color: var(--success-color);
        }

        /* ===== BUTTON COMPONENTS ===== */
        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: var(--border-radius-md);
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: var(--transition-smooth);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            position: relative;
            overflow: hidden;
            font-family: var(--font-primary);
            letter-spacing: 0.5px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition-fast);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:hover {
            box-shadow: var(--shadow-glow-hover);
        }

        .btn-secondary {
            background: var(--gradient-dark);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--gradient-success);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-warning {
            background: var(--gradient-warning);
            color: var(--text-black);
            box-shadow: var(--shadow-md);
        }

        .btn-danger {
            background: var(--gradient-danger);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-info {
            background: var(--gradient-info);
            color: var(--text-black);
            box-shadow: var(--shadow-md);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Loading State */
        .btn.loading {
            pointer-events: none;
            position: relative;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* ===== ANIMATIONS ===== */
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.05); }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-8px,0); }
            70% { transform: translate3d(0,-4px,0); }
            90% { transform: translate3d(0,-2px,0); }
        }

        @keyframes slideInLeft {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes fadeInUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes fadeInDown {
            from { transform: translateY(-30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes zoomIn {
            from { transform: scale(0.8); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        /* ===== UTILITY CLASSES ===== */
        .animate-slide-in-left { animation: slideInLeft 0.6s ease-out; }
        .animate-slide-in-right { animation: slideInRight 0.6s ease-out; }
        .animate-fade-in-up { animation: fadeInUp 0.6s ease-out; }
        .animate-fade-in-down { animation: fadeInDown 0.6s ease-out; }
        .animate-zoom-in { animation: zoomIn 0.4s ease-out; }

        .text-gradient {
            background: var(--gradient-aurora);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-effect {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--border-color);
        }

        .shadow-glow {
            box-shadow: var(--shadow-glow);
        }

        .shadow-glow-hover:hover {
            box-shadow: var(--shadow-glow-hover);
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 1400px) {
            :root {
                --sidebar-width: 280px;
                --content-padding: 25px;
                --card-padding: 25px;
            }
        }

        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 260px;
                --content-padding: 20px;
                --card-padding: 20px;
            }
            
            .search-input {
                width: 220px;
            }
            
            .search-input:focus {
                width: 280px;
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar.collapsed + .main-content {
                margin-left: 0;
            }
            
            .topbar {
                padding: 0 20px;
            }
            
            .page-title {
                font-size: 1.5rem;
            }
            
            .breadcrumb-nav {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .topbar-right > *:not(:last-child) {
                display: none;
            }
            
            .search-container {
                display: none;
            }
            
            .user-info {
                display: none;
            }
            
            .content-wrapper {
                margin: 15px;
                padding: 20px;
            }
            
            .content-header h1 {
                font-size: 1.8rem;
            }
            
            .card-header {
                padding: 20px;
            }
            
            .card-body {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .content-header h1 {
                font-size: 1.5rem;
            }
            
            .card-header {
                padding: 15px;
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
            
            .card-icon {
                margin-right: 0;
            }
            
            .card-body {
                padding: 15px;
            }
            
            .form-control {
                padding: 14px 16px;
            }
            
            .btn {
                padding: 12px 24px;
                font-size: 0.9rem;
            }
        }

        /* ===== MOBILE OVERLAY ===== */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: var(--z-modal-backdrop);
            display: none;
            backdrop-filter: blur(5px);
        }

        @media (max-width: 992px) {
            .sidebar-overlay.active {
                display: block;
            }
        }

        /* ===== PRINT STYLES ===== */
        @media print {
            .sidebar, .topbar, .sidebar-toggle {
                display: none !important;
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .content-wrapper {
                margin: 0 !important;
                border: none !important;
                box-shadow: none !important;
                background: white !important;
            }
            
            * {
                color: black !important;
                background: white !important;
            }
        }

        /* ===== ACCESSIBILITY ===== */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        @media (prefers-contrast: high) {
            :root {
                --border-color: rgba(255, 255, 255, 0.5);
                --glass-bg: rgba(255, 255, 255, 0.2);
            }
        }

        /* ===== CUSTOM SCROLLBAR FOR SIDEBAR ===== */
        .sidebar-nav::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-nav::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar-nav::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        .sidebar-nav::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Custom Styles for specific pages */
        @stack('styles')
    </style>
    
    <!-- Custom Styles -->
    @stack('styles')
</head>
