<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\NotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Events\ThemeChanged;

class ProfileController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }    /**
     * Show the user profile.
     */
    public function show()
    {
        $user = Auth::user();
        return view('profile.show', compact('user'));
    }

    /**
     * Show the user profile (alias for show).
     */
    public function index()
    {
        return $this->show();
    }

    /**
     * Show the form for editing the profile.
     */
    public function edit()
    {
        $user = Auth::user();
        return view('profile.edit', compact('user'));
    }

    /**
     * Update the user profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'username' => 'required|string|max:255|unique:users,username,' . $user->id,
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'gender' => 'nullable|in:L,P',
            'birth_date' => 'nullable|date',
            'bio' => 'nullable|string|max:500',
            'address' => 'nullable|string|max:1000',
            'city' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:10',
            'country' => 'nullable|string|max:255',
            'timezone' => 'nullable|string|max:255',
            'language' => 'nullable|in:id,en',
            'theme' => 'nullable|in:light,dark,solarized-dark,synth-wave',
            'currency' => 'nullable|string|max:3',
            'date_format' => 'nullable|string|max:20',
            'time_format' => 'nullable|in:12,24',
            'two_factor_enabled' => 'nullable|boolean',
            'notification_enabled' => 'nullable|boolean',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        // Handle avatar upload
        $updateData = $request->only([
            'username',
            'name',
            'email', 
            'phone',
            'gender',
            'birth_date',
            'bio',
            'address',
            'city',
            'postal_code',
            'country',
            'timezone',
            'language',
            'theme',
            'currency',
            'date_format',
            'time_format',
            'two_factor_enabled',
            'notification_enabled'
        ]);

        // Convert boolean values
        $updateData['two_factor_enabled'] = $request->has('two_factor_enabled') ? 1 : 0;
        $updateData['notification_enabled'] = $request->has('notification_enabled') ? 1 : 0;

        // Set default values if not provided
        $updateData['country'] = $updateData['country'] ?? 'Indonesia';
        $updateData['timezone'] = $updateData['timezone'] ?? 'Asia/Jakarta';
        $updateData['language'] = $updateData['language'] ?? 'id';
        $updateData['theme'] = $updateData['theme'] ?? 'light';
        $updateData['currency'] = $updateData['currency'] ?? 'IDR';
        $updateData['date_format'] = $updateData['date_format'] ?? 'd/m/Y';
        $updateData['time_format'] = $updateData['time_format'] ?? '24';

        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Store new avatar
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $updateData['avatar'] = $avatarPath;
        }

        // Check if theme has changed to trigger event
        $oldTheme = $user->theme;
        $newTheme = $updateData['theme'];

        $user->update($updateData);

        // Trigger theme changed event if needed
        if ($oldTheme !== $newTheme) {
            event(new ThemeChanged($user, $oldTheme, $newTheme));
        }

        return redirect()->route('settings.profile')
                        ->with('success', 'Profil berhasil diperbarui!');
    }

    /**
     * Show security settings.
     */
    public function security()
    {
        $user = Auth::user();
        return view('profile.security', compact('user'));
    }    /**
     * Update security settings.
     */
    public function updateSecurity(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|min:8|confirmed',
        ]);

        $user = Auth::user();

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()
                            ->withErrors(['current_password' => 'Password saat ini salah.'])
                            ->withInput();
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
        ]);
        
        return redirect()->route('profile.security')
                        ->with('success', 'Password berhasil diperbarui!');
    }    /**
     * Show notification settings.
     */
    public function notifications()
    {
        $user = Auth::user();
        $settings = $user->getNotificationSettings();
        
        return view('settings.notifications', compact('user', 'settings'));
    }

    /**
     * Update notification settings.
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'email_enabled' => 'boolean',
            'email_login_alerts' => 'boolean',
            'email_security_alerts' => 'boolean',
            'email_transaction_alerts' => 'boolean',
            'email_budget_alerts' => 'boolean',
            'email_weekly_summary' => 'boolean',
            'email_monthly_report' => 'boolean',
            'email_marketing' => 'boolean',
            'web_enabled' => 'boolean',
            'web_transaction_alerts' => 'boolean',
            'web_budget_warnings' => 'boolean',
            'web_goal_progress' => 'boolean',
            'web_reminders' => 'boolean',
            'push_enabled' => 'boolean',
            'push_transaction_alerts' => 'boolean',
            'push_security_alerts' => 'boolean',
            'push_budget_warnings' => 'boolean',
            'sms_enabled' => 'boolean',
            'sms_security_alerts' => 'boolean',
            'sms_critical_alerts' => 'boolean',
            'sms_phone' => 'nullable|string|max:20',
            'quiet_hours_start' => 'required|date_format:H:i',
            'quiet_hours_end' => 'required|date_format:H:i',
            'weekend_notifications' => 'boolean',
            'summary_frequency' => 'required|in:daily,weekly,monthly',
            'budget_alert_frequency' => 'required|in:instant,daily,weekly',
            'custom_keywords' => 'nullable|string',
            'large_transaction_threshold' => 'required|numeric|min:0',
            'channels' => 'nullable|array',
        ]);

        // Process custom keywords
        $customKeywords = [];
        if ($request->custom_keywords) {
            $customKeywords = array_map('trim', explode(',', $request->custom_keywords));
            $customKeywords = array_filter($customKeywords); // Remove empty values
        }

        // Process notification channels
        $notificationChannels = [];
        if ($request->channels) {
            foreach ($request->channels as $channel => $config) {
                if (isset($config['enabled']) && $config['enabled']) {
                    $notificationChannels[$channel] = $config;
                }
            }
        }

        $settings = $user->getNotificationSettings();
        $settings->update([
            'email_enabled' => $request->has('email_enabled'),
            'email_login_alerts' => $request->has('email_login_alerts'),
            'email_security_alerts' => $request->has('email_security_alerts'),
            'email_transaction_alerts' => $request->has('email_transaction_alerts'),
            'email_budget_alerts' => $request->has('email_budget_alerts'),
            'email_weekly_summary' => $request->has('email_weekly_summary'),
            'email_monthly_report' => $request->has('email_monthly_report'),
            'email_marketing' => $request->has('email_marketing'),
            'web_enabled' => $request->has('web_enabled'),
            'web_transaction_alerts' => $request->has('web_transaction_alerts'),
            'web_budget_warnings' => $request->has('web_budget_warnings'),
            'web_goal_progress' => $request->has('web_goal_progress'),
            'web_reminders' => $request->has('web_reminders'),
            'push_enabled' => $request->has('push_enabled'),
            'push_transaction_alerts' => $request->has('push_transaction_alerts'),
            'push_security_alerts' => $request->has('push_security_alerts'),
            'push_budget_warnings' => $request->has('push_budget_warnings'),
            'sms_enabled' => $request->has('sms_enabled'),
            'sms_security_alerts' => $request->has('sms_security_alerts'),
            'sms_critical_alerts' => $request->has('sms_critical_alerts'),
            'sms_phone' => $request->sms_phone,
            'quiet_hours_start' => $request->quiet_hours_start,
            'quiet_hours_end' => $request->quiet_hours_end,
            'weekend_notifications' => $request->has('weekend_notifications'),
            'summary_frequency' => $request->summary_frequency,
            'budget_alert_frequency' => $request->budget_alert_frequency,
            'custom_keywords' => $customKeywords,
            'large_transaction_threshold' => $request->large_transaction_threshold,
            'notification_channels' => $notificationChannels,
        ]);
        
        return redirect()->route('settings.notifications')
                        ->with('success', 'Pengaturan notifikasi berhasil diperbarui!');
    }

    /**
     * Show backup settings.
     */
    public function backup()
    {
        return view('settings.backup');
    }

    /**
     * Process backup.
     */
    public function processBackup(Request $request)
    {
        // Logic untuk backup akan ditambahkan nanti
        
        return redirect()->route('settings.backup')
                        ->with('success', 'Backup berhasil dibuat!');
    }    /**
     * Update user theme preference.
     */
    public function updateTheme(Request $request)
    {
        \Log::info('Theme update request received', [
            'theme' => $request->theme,
            'user_id' => Auth::id(),
            'request_data' => $request->all()
        ]);

        $request->validate([
            'theme' => 'required|in:light,solarized-dark,synth-wave'
        ]);

        $user = Auth::user();
        
        \Log::info('Before update - current theme', [
            'current_theme' => $user->theme,
            'new_theme' => $request->theme
        ]);
        
        $user->update([
            'theme' => $request->theme
        ]);
        
        $user->refresh();
        
        \Log::info('After update - theme saved', [
            'saved_theme' => $user->theme
        ]);

        // Trigger real-time event untuk Pusher
        try {
            event(new ThemeChanged($user, $request->theme));
            \Log::info('ThemeChanged event dispatched successfully');
        } catch (\Exception $e) {
            \Log::error('Failed to dispatch ThemeChanged event: ' . $e->getMessage());
        }

        return response()->json([
            'success' => true,
            'message' => 'Theme berhasil diperbarui',
            'theme' => $request->theme
        ]);
    }
}
