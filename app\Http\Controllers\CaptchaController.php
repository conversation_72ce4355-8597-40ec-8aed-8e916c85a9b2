<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CaptchaController extends Controller
{    /**
     * Generate captcha image
     */
    public function generate()
    {
        try {
            // Check if GD extension is available
            if (!extension_loaded('gd')) {
                return $this->generateSimpleCaptcha();
            }
            
            // Generate random 4 digit number
            $captcha = rand(1000, 9999);
            
            // Store in session
            session(['captcha' => $captcha]);
            
            // Create image with smaller size
            $width = 80;
            $height = 26;
            $image = imagecreatetruecolor($width, $height);
            
            // Colors
            $background = imagecolorallocate($image, 255, 255, 255);
            $textColor = imagecolorallocate($image, 0, 0, 0);
            $lineColor = imagecolorallocate($image, 128, 128, 128);
            
            // Fill background
            imagefill($image, 0, 0, $background);
            
            // Add some noise lines
            for ($i = 0; $i < 2; $i++) {
                imageline($image, 0, rand(0, $height), $width, rand(0, $height), $lineColor);
            }
            
            // Add text with smaller font
            imagestring($image, 3, 18, 6, $captcha, $textColor);
            
            // Output image
            ob_start();
            imagepng($image);
            $imageData = ob_get_contents();
            ob_end_clean();
            
            // Clean up
            imagedestroy($image);
            
            return response($imageData)
                ->header('Content-Type', 'image/png')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');
                
        } catch (\Exception $e) {
            return $this->generateSimpleCaptcha();
        }
    }
    
    /**
     * Generate simple text captcha if GD is not available
     */
    private function generateSimpleCaptcha()
    {
        $captcha = rand(1000, 9999);
        session(['captcha' => $captcha]);
        
        // Create simple SVG captcha
        $svg = '<?xml version="1.0" encoding="UTF-8"?>
        <svg width="80" height="26" xmlns="http://www.w3.org/2000/svg">
            <rect width="80" height="26" fill="white" stroke="gray" stroke-width="1"/>
            <text x="15" y="18" font-family="Arial" font-size="14" fill="black">' . $captcha . '</text>
        </svg>';
        
        return response($svg)
            ->header('Content-Type', 'image/svg+xml')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }
    
    /**
     * Refresh captcha (AJAX)
     */
    public function refresh()
    {
        return response()->json([
            'captcha_url' => route('captcha.generate') . '?' . time()
        ]);
    }
}
