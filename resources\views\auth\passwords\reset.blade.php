<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'My Money') }} - Reset Password</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Bootstrap & Custom CSS -->
    @vite(['resources/sass/app.scss'])
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Figtree', sans-serif;
            overflow: hidden;
        }
        
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
        }
        
        .auth-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 650px;
            height: 420px;
            display: flex;
        }
        
        .auth-left {
            flex: 1;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .auth-animation {
            text-align: center;
            color: white;
            z-index: 2;
            position: relative;
        }
        
        .auth-animation h2 {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            animation: fadeInUp 1s ease-out;
        }
        
        .auth-animation p {
            font-size: 1.1rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.2s both;
        }
        
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }
        
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            top: 20%;
            left: 20%;
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            top: 60%;
            right: 20%;
            width: 40px;
            height: 40px;
            background: white;
            transform: rotate(45deg);
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            bottom: 20%;
            left: 30%;
            width: 50px;
            height: 50px;
            background: white;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation-delay: 4s;
        }
        
        .auth-right {
            flex: 1;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }
        
        .form-container {
            position: relative;
            width: 100%;
        }
        
        .form-slide {
            width: 100%;
            opacity: 0;
            transform: translateX(30px);
            transition: all 0.4s ease;
            position: absolute;
            visibility: hidden;
        }
        
        .form-slide.active {
            opacity: 1;
            transform: translateX(0);
            position: relative;
            visibility: visible;
        }
        
        .form-slide.exit {
            opacity: 0;
            transform: translateX(-30px);
        }
        
        .form-title {
            text-align: center;
            margin-bottom: 1.5rem;
        }
        
        .form-title h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .form-title p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.3rem;
            font-weight: 500;
            color: #555;
            font-size: 0.85rem;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: white;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-control.is-invalid {
            border-color: #dc3545;
        }
        
        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }
        
        .btn-primary {
            width: 100%;
            padding: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
        }
        
        .form-links {
            text-align: center;
            margin-top: 1rem;
        }
        
        .form-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin: 0 8px;
            font-size: 0.85rem;
        }
        
        .form-links a:hover {
            text-decoration: underline;
        }
        
        /* Theme Switcher */
        .theme-switcher-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .theme-switcher {
            position: relative;
        }
        
        .theme-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .theme-icon {
            font-size: 1rem;
        }
        
        .dropdown-arrow {
            font-size: 0.7rem;
            transition: transform 0.3s ease;
        }
        
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .auth-card {
                flex-direction: column;
                height: auto;
                max-height: 90vh;
            }
            
            .auth-left {
                min-height: 150px;
                flex: none;
            }
            
            .auth-animation h2 {
                font-size: 1.8rem;
            }
            
            .auth-right {
                padding: 1.5rem;
            }
        }
        
        /* Theme Variations */
        /* Solarized Dark Theme */
        body[data-theme="solarized-dark"] {
            background: linear-gradient(135deg, #002b36 0%, #073642 100%);
        }
        
        body[data-theme="solarized-dark"] .auth-left {
            background: linear-gradient(45deg, #073642, #586e75);
        }
        
        body[data-theme="solarized-dark"] .auth-card {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .form-title h3 {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .form-group label {
            color: #657b83;
        }
        
        body[data-theme="solarized-dark"] .form-title p {
            color: #839496;
        }
        
        body[data-theme="solarized-dark"] .btn-primary {
            background: linear-gradient(45deg, #268bd2, #2aa198);
        }
        
        body[data-theme="solarized-dark"] .form-links a {
            color: #268bd2;
        }
        
        /* Synth Wave Theme */
        body[data-theme="synth-wave"] {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a0033 50%, #000000 100%);
        }
        
        body[data-theme="synth-wave"] .auth-left {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
        }
        
        body[data-theme="synth-wave"] .auth-card {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .form-control {
            background: #2a2a2a;
            border-color: #ff00ff;
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .form-control:focus {
            border-color: #00ffff;
            box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.2);
        }
        
        body[data-theme="synth-wave"] .form-title h3 {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .form-group label {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .form-title p {
            color: #ff0080;
        }
        
        body[data-theme="synth-wave"] .btn-primary {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.4);
        }
        
        body[data-theme="synth-wave"] .form-links a {
            color: #00ffff;
        }
    </style>
</head>
<body>
    <!-- Theme Switcher -->
    <div class="theme-switcher-container">
        <div class="theme-switcher">
            <button class="theme-toggle" id="themeToggle" onclick="toggleThemeDropdown()">
                <i class="theme-icon" id="themeIcon">🌞</i>
                <span class="theme-text" id="themeText">Light</span>
                <i class="dropdown-arrow">▼</i>
            </button>
            <div class="theme-dropdown" id="themeDropdown">
                <div class="theme-option" onclick="setTheme('light')">
                    <i class="theme-option-icon">🌞</i>
                    <span>Light</span>
                </div>
                <div class="theme-option" onclick="setTheme('solarized-dark')">
                    <i class="theme-option-icon">🌙</i>
                    <span>Solarized Dark</span>
                </div>
                <div class="theme-option" onclick="setTheme('synth-wave')">
                    <i class="theme-option-icon">🌆</i>
                    <span>Synth Wave</span>
                </div>
            </div>
        </div>
    </div>

    <div class="auth-container">
        <div class="auth-card">
            <!-- Left Side - Animation -->
            <div class="auth-left">
                <div class="floating-shapes">
                    <div class="shape"></div>
                    <div class="shape"></div>
                    <div class="shape"></div>
                </div>
                <div class="auth-animation">
                    <h2 id="animationTitle">Reset Password</h2>
                    <p id="animationSubtitle">Buat password baru yang aman</p>
                </div>
            </div>
            
            <!-- Right Side - Form -->
            <div class="auth-right">
                <div class="form-container">
                    <!-- Reset Password Form -->
                    <div id="reset-form" class="form-slide active">
                        <div class="form-title">
                            <h3>Reset Password</h3>
                            <p>Masukkan password baru Anda</p>
                        </div>
                        
                        <form method="POST" action="{{ route('password.update') }}">
                            @csrf
                            <input type="hidden" name="token" value="{{ $token }}">
                            
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" 
                                       name="email" value="{{ $email ?? old('email') }}" required autocomplete="email" autofocus readonly>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="form-group">
                                <label for="password">Password Baru</label>
                                <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" 
                                       name="password" required autocomplete="new-password" minlength="8">
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="form-group">
                                <label for="password-confirm">Konfirmasi Password</label>
                                <input id="password-confirm" type="password" class="form-control" 
                                       name="password_confirmation" required autocomplete="new-password">
                            </div>
                            
                            <button type="submit" class="btn-primary">Reset Password</button>
                        </form>
                        
                        <div class="form-links">
                            <a href="{{ route('login') }}">Kembali ke Login</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    @vite(['resources/js/app.js'])
    
    <script>
        // Theme functionality
        function toggleThemeDropdown() {
            const dropdown = document.getElementById('themeDropdown');
            dropdown.classList.toggle('show');
        }
        
        function setTheme(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');
            
            switch(theme) {
                case 'light':
                    themeIcon.textContent = '🌞';
                    themeText.textContent = 'Light';
                    break;
                case 'solarized-dark':
                    themeIcon.textContent = '🌙';
                    themeText.textContent = 'Solarized Dark';
                    break;
                case 'synth-wave':
                    themeIcon.textContent = '🌆';
                    themeText.textContent = 'Synth Wave';
                    break;
            }
            
            document.getElementById('themeDropdown').classList.remove('show');
        }
        
        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            setTheme(savedTheme);
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const themeSwitcher = document.querySelector('.theme-switcher');
            if (!themeSwitcher.contains(event.target)) {
                document.getElementById('themeDropdown').classList.remove('show');
            }
        });
        
        // SweetAlert2 Flash Messages
        @if(session('status'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('status') }}',
                confirmButtonColor: '#667eea',
                timer: 3000,
                timerProgressBar: true
            });
        @endif

        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                confirmButtonColor: '#667eea',
                timer: 3000,
                timerProgressBar: true
            });
        @endif

        @if(session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Terjadi Kesalahan!',
                text: '{{ session('error') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        @if(session('warning'))
            Swal.fire({
                icon: 'warning',
                title: 'Peringatan!',
                text: '{{ session('warning') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        @if(session('info'))
            Swal.fire({
                icon: 'info',
                title: 'Informasi',
                text: '{{ session('info') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        @if($errors->any())
            let errorMessage = '';
            @foreach($errors->all() as $error)
                errorMessage += '{{ $error }}\n';
            @endforeach
            
            Swal.fire({
                icon: 'error',
                title: 'Validasi Gagal!',
                text: errorMessage,
                confirmButtonColor: '#667eea'
            });
        @endif
    </script>
</body>
</html>
