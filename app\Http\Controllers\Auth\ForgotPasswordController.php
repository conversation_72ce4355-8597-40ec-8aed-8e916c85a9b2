<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;

class ForgotPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

    use SendsPasswordResetEmails;
    
    /**
     * Send a reset link to the given user.
     */
    public function sendResetLinkEmail(Request $request)
    {
        $this->validateEmail($request);

        try {
            // We will send the password reset link to this user. Once we have attempted
            // to send the link, we will examine the response then see the message we
            // need to show to the user. Finally, we'll send out a proper response.
            $response = $this->broker()->sendResetLink(
                $this->credentials($request)
            );

            if ($response == Password::RESET_LINK_SENT) {
                return redirect()->route('login')->with('success', 'Link reset password telah dikirim ke email Anda!');
            }

            // If an error occurred, return with error message
            return redirect()->route('login')->with('warning', 'Email tidak ditemukan dalam sistem kami.');
            
        } catch (\Exception $e) {
            \Log::error('Password reset error: ' . $e->getMessage());
            return redirect()->route('login')->with('error', 'Terjadi kesalahan saat mengirim email reset password. Silakan coba lagi.');
        }
    }
}
