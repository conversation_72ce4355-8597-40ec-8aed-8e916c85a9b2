<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ProfileEditTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    /** @test */
    public function user_can_view_profile_edit_page()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->get(route('profile.edit'));

        $response->assertStatus(200);
        $response->assertViewIs('profile.edit');
        $response->assertViewHas('user', $user);
    }

    /** @test */
    public function user_can_update_basic_profile_information()
    {
        $user = User::factory()->create([
            'name' => 'Old Name',
            'username' => 'oldusername',
            'email' => '<EMAIL>',
        ]);

        $response = $this->actingAs($user)
            ->patch(route('profile.update'), [
                'name' => 'New Name',
                'username' => 'newusername',
                'email' => '<EMAIL>',
                'phone' => '081234567890',
                'gender' => 'L',
                'country' => 'Indonesia',
                'timezone' => 'Asia/Jakarta',
                'language' => 'id',
                'theme' => 'light',
            ]);

        $response->assertRedirect(route('settings.profile'));
        $response->assertSessionHas('success', 'Profil berhasil diperbarui!');

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'New Name',
            'username' => 'newusername',
            'email' => '<EMAIL>',
            'phone' => '081234567890',
            'gender' => 'L',
        ]);
    }

    /** @test */
    public function user_can_upload_avatar()
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('avatar.jpg', 100, 100);

        $response = $this->actingAs($user)
            ->patch(route('profile.update'), [
                'name' => $user->name,
                'username' => $user->username,
                'email' => $user->email,
                'avatar' => $file,
            ]);

        $response->assertRedirect(route('settings.profile'));
        
        $user->refresh();
        $this->assertNotNull($user->avatar);
        Storage::disk('public')->assertExists($user->avatar);
    }

    /** @test */
    public function profile_update_validates_required_fields()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->patch(route('profile.update'), [
                'name' => '',
                'username' => '',
                'email' => '',
            ]);

        $response->assertSessionHasErrors(['name', 'username', 'email']);
    }

    /** @test */
    public function profile_update_validates_email_format()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->patch(route('profile.update'), [
                'name' => 'Test Name',
                'username' => 'testuser',
                'email' => 'invalid-email',
            ]);

        $response->assertSessionHasErrors(['email']);
    }

    /** @test */
    public function profile_update_validates_unique_username()
    {
        $user1 = User::factory()->create(['username' => 'existinguser']);
        $user2 = User::factory()->create(['username' => 'anotheruser']);

        $response = $this->actingAs($user2)
            ->patch(route('profile.update'), [
                'name' => 'Test Name',
                'username' => 'existinguser', // Try to use existing username
                'email' => '<EMAIL>',
            ]);

        $response->assertSessionHasErrors(['username']);
    }

    /** @test */
    public function profile_update_validates_unique_email()
    {
        $user1 = User::factory()->create(['email' => '<EMAIL>']);
        $user2 = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->actingAs($user2)
            ->patch(route('profile.update'), [
                'name' => 'Test Name',
                'username' => 'testuser',
                'email' => '<EMAIL>', // Try to use existing email
            ]);

        $response->assertSessionHasErrors(['email']);
    }

    /** @test */
    public function profile_update_validates_avatar_file_type()
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->create('document.pdf', 100);

        $response = $this->actingAs($user)
            ->patch(route('profile.update'), [
                'name' => $user->name,
                'username' => $user->username,
                'email' => $user->email,
                'avatar' => $file,
            ]);

        $response->assertSessionHasErrors(['avatar']);
    }

    /** @test */
    public function profile_update_validates_avatar_file_size()
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->create('large-avatar.jpg', 3000); // 3MB

        $response = $this->actingAs($user)
            ->patch(route('profile.update'), [
                'name' => $user->name,
                'username' => $user->username,
                'email' => $user->email,
                'avatar' => $file,
            ]);

        $response->assertSessionHasErrors(['avatar']);
    }

    /** @test */
    public function old_avatar_is_deleted_when_new_one_is_uploaded()
    {
        $user = User::factory()->create();
        
        // Upload first avatar
        $oldFile = UploadedFile::fake()->image('old-avatar.jpg');
        $oldPath = $oldFile->store('avatars', 'public');
        $user->update(['avatar' => $oldPath]);

        // Upload new avatar
        $newFile = UploadedFile::fake()->image('new-avatar.jpg');
        
        $response = $this->actingAs($user)
            ->patch(route('profile.update'), [
                'name' => $user->name,
                'username' => $user->username,
                'email' => $user->email,
                'avatar' => $newFile,
            ]);

        $response->assertRedirect(route('settings.profile'));
        
        // Check old avatar is deleted and new one exists
        Storage::disk('public')->assertMissing($oldPath);
        
        $user->refresh();
        Storage::disk('public')->assertExists($user->avatar);
    }

    /** @test */
    public function user_cannot_access_profile_edit_when_not_authenticated()
    {
        $response = $this->get(route('profile.edit'));
        $response->assertRedirect(route('login'));
    }
}
