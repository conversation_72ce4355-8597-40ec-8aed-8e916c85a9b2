<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use App\Models\Transaction;
use App\Models\Account;
use Carbon\Carbon;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Artisan::command('debug:income', function () {
    $userId = 2; // Assuming user ID 2

    $this->info('=== DEBUG INCOME & BALANCE GROWTH ===');
    
    // Get current month data
    $currentMonth = Carbon::now()->startOfMonth();
    $currentMonthEnd = Carbon::now()->endOfMonth();
    $today = Carbon::today();

    $this->info("User ID: $userId");
    $this->info("Current Month: " . $currentMonth->format('Y-m-d') . " to " . $currentMonthEnd->format('Y-m-d'));
    $this->info("Today: " . $today->format('Y-m-d'));
    $this->newLine();

    // Check transactions by type for current month
    $incomeTransactions = Transaction::where('user_id', $userId)
        ->where('type', 'income')
        ->whereBetween('transaction_date', [$currentMonth, $currentMonthEnd])
        ->get();

    $expenseTransactions = Transaction::where('user_id', $userId)
        ->where('type', 'expense')
        ->whereBetween('transaction_date', [$currentMonth, $currentMonthEnd])
        ->get();

    $this->info('=== INCOME TRANSACTIONS (Current Month) ===');
    foreach ($incomeTransactions as $transaction) {
        $this->line("- {$transaction->title}: Rp " . number_format($transaction->amount, 0, ',', '.'));
        $this->line("  Date: {$transaction->transaction_date}");
        $this->line("  Type: {$transaction->type}");
        $this->newLine();
    }

    $this->info('=== EXPENSE TRANSACTIONS (Current Month) ===');
    foreach ($expenseTransactions as $transaction) {
        $this->line("- {$transaction->title}: Rp " . number_format($transaction->amount, 0, ',', '.'));
        $this->line("  Date: {$transaction->transaction_date}");
        $this->line("  Type: {$transaction->type}");
        $this->newLine();
    }

    // Calculate totals
    $monthlyIncome = $incomeTransactions->sum('amount');
    $monthlyExpense = $expenseTransactions->sum('amount');

    $this->info('=== MONTHLY TOTALS ===');
    $this->line("Monthly Income: Rp " . number_format($monthlyIncome, 0, ',', '.'));
    $this->line("Monthly Expense: Rp " . number_format($monthlyExpense, 0, ',', '.'));
    $this->line("Net Income: Rp " . number_format($monthlyIncome - $monthlyExpense, 0, ',', '.'));
    $this->newLine();

    // Check previous month for balance growth
    $previousMonth = Carbon::now()->subMonth()->startOfMonth();
    $previousMonthEnd = Carbon::now()->subMonth()->endOfMonth();

    $this->line("Previous Month: " . $previousMonth->format('Y-m-d') . " to " . $previousMonthEnd->format('Y-m-d'));

    $previousIncomeTransactions = Transaction::where('user_id', $userId)
        ->where('type', 'income')
        ->whereBetween('transaction_date', [$previousMonth, $previousMonthEnd])
        ->get();

    $previousExpenseTransactions = Transaction::where('user_id', $userId)
        ->where('type', 'expense')
        ->whereBetween('transaction_date', [$previousMonth, $previousMonthEnd])
        ->get();

    $previousIncome = $previousIncomeTransactions->sum('amount');
    $previousExpense = $previousExpenseTransactions->sum('amount');

    $this->info('=== PREVIOUS MONTH TOTALS ===');
    $this->line("Previous Income: Rp " . number_format($previousIncome, 0, ',', '.'));
    $this->line("Previous Expense: Rp " . number_format($previousExpense, 0, ',', '.'));
    $this->line("Previous Net: Rp " . number_format($previousIncome - $previousExpense, 0, ',', '.'));
    $this->newLine();

    // Calculate balance growth
    $currentNet = $monthlyIncome - $monthlyExpense;
    $previousNet = $previousIncome - $previousExpense;

    $this->info('=== BALANCE GROWTH CALCULATION ===');
    $this->line("Current Net: Rp " . number_format($currentNet, 0, ',', '.'));
    $this->line("Previous Net: Rp " . number_format($previousNet, 0, ',', '.'));

    if ($previousNet == 0) {
        $balanceGrowth = $currentNet > 0 ? 100 : 0;
        $this->line("Balance Growth: {$balanceGrowth}% (Previous net was 0)");
    } else {
        $balanceGrowth = round((($currentNet - $previousNet) / abs($previousNet)) * 100, 1);
        $this->line("Balance Growth: {$balanceGrowth}%");
    }

    // Check today's transactions
    $todayTransactions = Transaction::where('user_id', $userId)
        ->whereDate('transaction_date', $today)
        ->get();

    $this->newLine();
    $this->info('=== TODAY\'S TRANSACTIONS ===');
    $this->line("Count: " . $todayTransactions->count());
    foreach ($todayTransactions as $transaction) {
        $this->line("- {$transaction->title}: Rp " . number_format($transaction->amount, 0, ',', '.') . " ({$transaction->type})");
    }

    $this->newLine();
    $this->info('=== ISSUE ANALYSIS ===');
    if ($monthlyIncome == 0) {
        $this->error("❌ NO INCOME found for current month! This might be the issue.");
    } else {
        $this->line("✅ Income found: Rp " . number_format($monthlyIncome, 0, ',', '.'));
    }

    if ($balanceGrowth == -100) {
        $this->error("❌ Balance growth shows -100%, this indicates previous month had income but current month has none.");
    } else {
        $this->line("✅ Balance growth looks normal: {$balanceGrowth}%");
    }
})->purpose('Debug income and balance growth calculation');
