@extends('layouts.dashboard')

@section('title', 'Pengaturan Notifikasi')

@section('page-title', 'Pengaturan Notifikasi')

@push('styles')
<style>
.notification-item {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    cursor: pointer;
}

.notification-item:hover {
    background-color: var(--bs-light);
    transform: translateX(5px);
}

.notification-item.unread {
    background-color: rgba(13, 110, 253, 0.05);
    border-left-color: var(--bs-primary);
}

.notification-item.important {
    border-left-color: var(--bs-danger);
}

.notification-badge {
    position: relative;
    display: inline-block;
}

.notification-badge::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: var(--bs-danger);
    border-radius: 50%;
    border: 2px solid white;
}

.settings-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #007bff;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.notification-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
}

.realtime-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    background: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.notification-center {
    max-height: 600px;
    overflow-y: auto;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.notification-filters {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.filter-btn {
    border: none;
    background: transparent;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn.active {
    background: #007bff;
    color: white;
}

.notification-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Flash Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <!-- Notification Center -->
        <div class="col-lg-8 mb-4">
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card settings-card notification-stats">
                        <div class="card-body text-center">
                            <i class="fas fa-bell fa-2x mb-2"></i>
                            <h4 class="mb-1" id="total-notifications">0</h4>
                            <small>Total Notifikasi</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card settings-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white;">
                        <div class="card-body text-center">
                            <i class="fas fa-envelope-open fa-2x mb-2"></i>
                            <h4 class="mb-1" id="unread-notifications">0</h4>
                            <small>Belum Dibaca</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card settings-card" style="background: linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%); color: white;">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <h4 class="mb-1" id="important-notifications">0</h4>
                            <small>Penting</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card settings-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <div class="card-body text-center">
                            <span class="realtime-indicator me-2"></span>
                            <div>
                                <small>Status Realtime</small>
                                <div id="connection-status">Terhubung</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Center -->
            <div class="card settings-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        Pusat Notifikasi
                    </h5>
                    <div>
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="markAllAsRead()">
                            <i class="fas fa-check-double me-1"></i>Tandai Semua Terbaca
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="clearAllNotifications()">
                            <i class="fas fa-trash me-1"></i>Hapus Semua
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="notification-filters">
                    <div class="d-flex flex-wrap">
                        <button class="filter-btn active" data-filter="all">
                            <i class="fas fa-list me-1"></i>Semua
                        </button>
                        <button class="filter-btn" data-filter="unread">
                            <i class="fas fa-envelope me-1"></i>Belum Dibaca
                        </button>
                        <button class="filter-btn" data-filter="important">
                            <i class="fas fa-star me-1"></i>Penting
                        </button>
                        <button class="filter-btn" data-filter="success">
                            <i class="fas fa-check-circle me-1"></i>Sukses
                        </button>
                        <button class="filter-btn" data-filter="warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>Peringatan
                        </button>
                        <button class="filter-btn" data-filter="error">
                            <i class="fas fa-times-circle me-1"></i>Error
                        </button>
                    </div>
                </div>

                <div class="card-body p-0">
                    <div class="notification-center" id="notification-list">
                        <!-- Loading State -->
                        <div class="text-center p-4" id="loading-state">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Memuat notifikasi...</p>
                        </div>
                        
                        <!-- Empty State -->
                        <div class="empty-state" id="empty-state" style="display: none;">
                            <i class="fas fa-bell-slash fa-3x mb-3"></i>
                            <h5>Tidak ada notifikasi</h5>
                            <p class="text-muted">Semua notifikasi akan muncul di sini</p>
                        </div>
                        
                        <!-- Error State -->
                        <div class="text-center p-4" id="error-state" style="display: none;">
                            <i class="fas fa-exclamation-triangle fa-3x mb-3 text-warning"></i>
                            <h5>Gagal memuat notifikasi</h5>
                            <p class="text-muted">Terjadi kesalahan saat memuat notifikasi</p>
                            <button class="btn btn-outline-primary" onclick="notificationSystem.loadNotifications()">
                                <i class="fas fa-sync-alt me-1"></i>Coba Lagi
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Panel -->
        <div class="col-lg-4">
            <form method="POST" action="{{ route('settings.notifications.update') }}">
                @csrf
                @method('PUT')

                <!-- Email Notifications -->
                <div class="card settings-card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-envelope me-2"></i>
                            Notifikasi Email
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <label class="form-label mb-0">Aktifkan Email</label>
                                <label class="switch">
                                    <input type="checkbox" name="email_enabled" value="1" 
                                           {{ $notificationSetting->email_enabled ? 'checked' : '' }}>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="email-settings">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Login Alerts</label>
                                    <label class="switch">
                                        <input type="checkbox" name="email_login_alerts" value="1"
                                               {{ $notificationSetting->email_login_alerts ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Security Alerts</label>
                                    <label class="switch">
                                        <input type="checkbox" name="email_security_alerts" value="1"
                                               {{ $notificationSetting->email_security_alerts ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Transaction Alerts</label>
                                    <label class="switch">
                                        <input type="checkbox" name="email_transaction_alerts" value="1"
                                               {{ $notificationSetting->email_transaction_alerts ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Budget Alerts</label>
                                    <label class="switch">
                                        <input type="checkbox" name="email_budget_alerts" value="1"
                                               {{ $notificationSetting->email_budget_alerts ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Weekly Summary</label>
                                    <label class="switch">
                                        <input type="checkbox" name="email_weekly_summary" value="1"
                                               {{ $notificationSetting->email_weekly_summary ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Monthly Report</label>
                                    <label class="switch">
                                        <input type="checkbox" name="email_monthly_report" value="1"
                                               {{ $notificationSetting->email_monthly_report ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Web Notifications -->
                <div class="card settings-card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-desktop me-2"></i>
                            Notifikasi Web
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <label class="form-label mb-0">Aktifkan Web</label>
                                <label class="switch">
                                    <input type="checkbox" name="web_enabled" value="1"
                                           {{ $notificationSetting->web_enabled ? 'checked' : '' }}>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="web-settings">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Transaction Alerts</label>
                                    <label class="switch">
                                        <input type="checkbox" name="web_transaction_alerts" value="1"
                                               {{ $notificationSetting->web_transaction_alerts ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Budget Warnings</label>
                                    <label class="switch">
                                        <input type="checkbox" name="web_budget_warnings" value="1"
                                               {{ $notificationSetting->web_budget_warnings ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Goal Progress</label>
                                    <label class="switch">
                                        <input type="checkbox" name="web_goal_progress" value="1"
                                               {{ $notificationSetting->web_goal_progress ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-0">Reminders</label>
                                    <label class="switch">
                                        <input type="checkbox" name="web_reminders" value="1"
                                               {{ $notificationSetting->web_reminders ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="requestNotificationPermission()">
                                <i class="fas fa-bell me-1"></i>
                                Izinkan Notifikasi Browser
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Simpan Pengaturan
                    </button>
                </div>
            </form>

            <!-- Test Notifications -->
            <div class="card settings-card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-vial me-2"></i>
                        Test Notifikasi
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="testNotification('success')">
                            <i class="fas fa-check-circle me-1"></i>Test Success
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="testNotification('warning')">
                            <i class="fas fa-exclamation-triangle me-1"></i>Test Warning
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="testNotification('error')">
                            <i class="fas fa-times-circle me-1"></i>Test Error
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="testNotification('info')">
                            <i class="fas fa-info-circle me-1"></i>Test Info
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Include SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Notification System
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.currentFilter = 'all';
        this.initializeEventListeners();
        this.startRealtimeConnection();
        this.loadNotifications();
    }

    initializeEventListeners() {
        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.setFilter(btn.dataset.filter);
            });
        });
    }

    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update active button
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeBtn = document.querySelector(`[data-filter="${filter}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
        
        this.renderNotifications();
    }

    loadNotifications() {
        this.showLoadingState();
        
        fetch('/api/notifications', {
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.notifications = data.notifications || [];
                    this.updateStats();
                    this.renderNotifications();
                    this.hideLoadingState();
                } else {
                    throw new Error('API returned error');
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                this.showErrorState();
                // Fallback to sample data if API fails
                this.loadSampleData();
            });
    }

    showLoadingState() {
        document.getElementById('loading-state').style.display = 'block';
        document.getElementById('empty-state').style.display = 'none';
        document.getElementById('error-state').style.display = 'none';
        
        // Clear existing notifications
        const existingNotifications = document.querySelectorAll('.notification-item');
        existingNotifications.forEach(item => item.remove());
    }

    hideLoadingState() {
        document.getElementById('loading-state').style.display = 'none';
    }

    showErrorState() {
        document.getElementById('loading-state').style.display = 'none';
        document.getElementById('empty-state').style.display = 'none';
        document.getElementById('error-state').style.display = 'block';
    }

    loadSampleData() {
        // Fallback sample data if API is not available
        this.notifications = [
            {
                id: 1,
                type: 'success',
                title: 'Transaksi Berhasil',
                message: 'Pembayaran Rp 50.000 berhasil diproses',
                created_at: new Date().toISOString(),
                read_at: null,
                is_important: false,
                action_url: null
            },
            {
                id: 2,
                type: 'warning',
                title: 'Budget Hampir Habis',
                message: 'Budget makanan bulan ini sudah 90% terpakai',
                created_at: new Date(Date.now() - 3600000).toISOString(),
                read_at: null,
                is_important: true,
                action_url: '/budget'
            }
        ];
        
        this.updateStats();
        this.renderNotifications();
        this.hideLoadingState();
    }

    addNotification(notification) {
        this.notifications.unshift(notification);
        this.updateStats();
        this.renderNotifications();
        
        // Show browser notification if enabled
        if (this.shouldShowBrowserNotification(notification)) {
            this.showBrowserNotification(notification);
        }
        
        // Play sound
        this.playNotificationSound(notification.type);
    }

    renderNotifications() {
        const container = document.getElementById('notification-list');
        const emptyState = document.getElementById('empty-state');
        const loadingState = document.getElementById('loading-state');
        const errorState = document.getElementById('error-state');
        
        // Remove existing notification items
        const existingItems = container.querySelectorAll('.notification-item');
        existingItems.forEach(item => item.remove());
        
        let filteredNotifications = this.notifications;
        
        // Apply filter
        if (this.currentFilter !== 'all') {
            filteredNotifications = this.notifications.filter(notification => {
                switch (this.currentFilter) {
                    case 'unread':
                        return !notification.read_at;
                    case 'important':
                        return notification.is_important;
                    default:
                        return notification.type === this.currentFilter;
                }
            });
        }
        
        if (filteredNotifications.length === 0) {
            emptyState.style.display = 'block';
            loadingState.style.display = 'none';
            errorState.style.display = 'none';
            return;
        }
        
        emptyState.style.display = 'none';
        loadingState.style.display = 'none';
        errorState.style.display = 'none';
        
        filteredNotifications.forEach(notification => {
            const notificationElement = this.createNotificationElement(notification);
            container.appendChild(notificationElement);
        });
    }

    createNotificationElement(notification) {
        const isUnread = !notification.read_at;
        const timeAgo = this.formatTimeAgo(notification.created_at);
        
        const div = document.createElement('div');
        div.className = `notification-item ${isUnread ? 'unread' : ''} ${notification.is_important ? 'important' : ''} p-3 border-bottom`;
        div.dataset.id = notification.id;
        div.onclick = () => this.showNotificationDetail(notification);
        
        div.innerHTML = `
            <div class="d-flex">
                <div class="me-3">
                    <div class="notification-badge">
                        <i class="${notification.icon || this.getDefaultIcon(notification.type)} text-${this.getTypeColor(notification.type)}"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${notification.title}</h6>
                            <p class="mb-1 text-muted">${notification.message}</p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>${timeAgo}
                                ${notification.is_important ? '<span class="badge bg-danger ms-2">Penting</span>' : ''}
                            </small>
                        </div>
                        <div class="notification-actions">
                            ${isUnread ? `<button class="btn btn-sm btn-outline-primary me-1" onclick="event.stopPropagation(); notificationSystem.markAsRead(${notification.id})">
                                <i class="fas fa-check"></i>
                            </button>` : ''}
                            <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); notificationSystem.deleteNotification(${notification.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    ${notification.action_url ? `
                        <div class="mt-2">
                            <a href="${notification.action_url}" class="btn btn-sm btn-primary me-2" onclick="event.stopPropagation()">
                                <i class="fas fa-external-link-alt me-1"></i>Lihat Detail
                            </a>
                            <a href="/notifications/${notification.id}" class="btn btn-sm btn-outline-info" onclick="event.stopPropagation()">
                                <i class="fas fa-eye me-1"></i>Detail Lengkap
                            </a>
                        </div>
                    ` : `
                        <div class="mt-2">
                            <a href="/notifications/${notification.id}" class="btn btn-sm btn-outline-info" onclick="event.stopPropagation()">
                                <i class="fas fa-eye me-1"></i>Lihat Detail
                            </a>
                        </div>
                    `}
                </div>
            </div>
        `;
        
        return div;
    }

    showNotificationDetail(notification) {
        Swal.fire({
            title: notification.title,
            html: `
                <div class="text-start">
                    <div class="mb-3">
                        <span class="badge bg-${this.getTypeColor(notification.type)} me-2">${notification.type.toUpperCase()}</span>
                        ${notification.is_important ? '<span class="badge bg-danger">PENTING</span>' : ''}
                    </div>
                    <p class="mb-3">${notification.message}</p>
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        ${this.formatTimeAgo(notification.created_at)}
                    </small>
                    ${notification.data ? `
                        <hr>
                        <h6>Detail Tambahan:</h6>
                        <pre class="bg-light p-2 rounded">${JSON.stringify(notification.data, null, 2)}</pre>
                    ` : ''}
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: notification.action_url ? 'Lihat Detail' : 'OK',
            cancelButtonText: 'Tutup',
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
        }).then((result) => {
            if (result.isConfirmed && notification.action_url) {
                window.location.href = notification.action_url;
            }
        });

        // Mark as read when viewing detail
        if (!notification.read_at) {
            this.markAsRead(notification.id);
        }
    }

    updateStats() {
        const total = this.notifications.length;
        const unread = this.notifications.filter(n => !n.read_at).length;
        const important = this.notifications.filter(n => n.is_important && !n.read_at).length;
        
        document.getElementById('total-notifications').textContent = total;
        document.getElementById('unread-notifications').textContent = unread;
        document.getElementById('important-notifications').textContent = important;
        
        // Update page title badge
        if (unread > 0) {
            document.title = `(${unread}) Notifikasi - MyMoney`;
        } else {
            document.title = 'Notifikasi - MyMoney';
        }
    }

    markAsRead(id) {
        fetch(`/api/notifications/${id}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const notification = this.notifications.find(n => n.id === id);
                if (notification) {
                    notification.read_at = new Date().toISOString();
                    this.updateStats();
                    this.renderNotifications();
                }
            }
        })
        .catch(error => console.error('Error marking notification as read:', error));
    }

    deleteNotification(id) {
        Swal.fire({
            title: 'Hapus Notifikasi?',
            text: 'Apakah Anda yakin ingin menghapus notifikasi ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/api/notifications/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.notifications = this.notifications.filter(n => n.id !== id);
                        this.updateStats();
                        this.renderNotifications();
                        
                        Swal.fire(
                            'Terhapus!',
                            'Notifikasi berhasil dihapus.',
                            'success'
                        );
                    }
                })
                .catch(error => {
                    console.error('Error deleting notification:', error);
                    Swal.fire(
                        'Error!',
                        'Gagal menghapus notifikasi.',
                        'error'
                    );
                });
            }
        });
    }

    startRealtimeConnection() {
        // Real-time connection with polling
        setInterval(() => {
            this.checkForNewNotifications();
        }, 5000); // Check every 5 seconds
        
        this.updateConnectionStatus(true);
    }

    checkForNewNotifications() {
        const lastCheck = localStorage.getItem('lastNotificationCheck') || new Date(Date.now() - 300000).toISOString(); // 5 minutes ago as default
        
        fetch(`/api/notifications/recent?since=${lastCheck}`, {
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.notifications && data.notifications.length > 0) {
                    data.notifications.forEach(notification => {
                        this.addNotification(notification);
                    });
                }
                localStorage.setItem('lastNotificationCheck', new Date().toISOString());
                this.updateConnectionStatus(true);
            })
            .catch(error => {
                console.error('Error checking for new notifications:', error);
                this.updateConnectionStatus(false);
            });
    }

    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        const indicator = document.querySelector('.realtime-indicator');
        
        if (connected) {
            statusElement.textContent = 'Terhubung';
            indicator.style.background = '#28a745';
        } else {
            statusElement.textContent = 'Terputus';
            indicator.style.background = '#dc3545';
        }
    }

    shouldShowBrowserNotification(notification) {
        return 'Notification' in window && 
               Notification.permission === 'granted' && 
               (notification.type === 'error' || notification.is_important);
    }

    showBrowserNotification(notification) {
        if (!this.shouldShowBrowserNotification(notification)) return;
        
        const browserNotification = new Notification(notification.title, {
            body: notification.message,
            icon: '/favicon.ico'
        });
        
        setTimeout(() => {
            browserNotification.close();
        }, 5000);
    }

    requestNotificationPermission() {
        if (!('Notification' in window)) {
            Swal.fire('Error', 'Browser ini tidak mendukung notifikasi desktop', 'error');
            return;
        }
        
        if (Notification.permission === 'granted') {
            this.showTestBrowserNotification();
            return;
        }
        
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                this.showTestBrowserNotification();
            }
        });
    }

    showTestBrowserNotification() {
        new Notification('MyMoney - Test Notifikasi', {
            body: 'Notifikasi browser berhasil diaktifkan!',
            icon: '/favicon.ico'
        });
    }

    playNotificationSound(type) {
        // Simple beep sound for notifications
        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBTOF0fTPdCQFKnfG7+ONOTE5');
        audio.volume = 0.3;
        audio.play().catch(e => console.log('Could not play sound:', e));
    }

    getDefaultIcon(type) {
        const icons = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        };
        return icons[type] || 'fas fa-bell';
    }

    getTypeColor(type) {
        const colors = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'info'
        };
        return colors[type] || 'secondary';
    }

    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Baru saja';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit yang lalu`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam yang lalu`;
        return `${Math.floor(diffInSeconds / 86400)} hari yang lalu`;
    }
}

// Global functions
function markAllAsRead() {
    Swal.fire({
        title: 'Tandai Semua Terbaca?',
        text: 'Semua notifikasi akan ditandai sebagai telah dibaca.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Tandai Semua!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('/api/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    notificationSystem.notifications.forEach(n => n.read_at = new Date().toISOString());
                    notificationSystem.updateStats();
                    notificationSystem.renderNotifications();
                    
                    Swal.fire(
                        'Berhasil!',
                        'Semua notifikasi telah ditandai sebagai terbaca.',
                        'success'
                    );
                }
            })
            .catch(error => {
                console.error('Error marking all as read:', error);
                Swal.fire(
                    'Error!',
                    'Gagal menandai semua notifikasi sebagai terbaca.',
                    'error'
                );
            });
        }
    });
}

function clearAllNotifications() {
    Swal.fire({
        title: 'Hapus Semua Notifikasi?',
        text: 'Semua notifikasi akan dihapus permanen. Aksi ini tidak dapat dibatalkan!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus Semua!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('/api/notifications/clear-all', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    notificationSystem.notifications = [];
                    notificationSystem.updateStats();
                    notificationSystem.renderNotifications();
                    
                    Swal.fire(
                        'Terhapus!',
                        'Semua notifikasi berhasil dihapus.',
                        'success'
                    );
                }
            })
            .catch(error => {
                console.error('Error clearing notifications:', error);
                Swal.fire(
                    'Error!',
                    'Gagal menghapus semua notifikasi.',
                    'error'
                );
            });
        }
    });
}

function testNotification(type) {
    const messages = {
        'success': { title: 'Test Berhasil!', message: 'Ini adalah notifikasi sukses untuk pengujian.' },
        'warning': { title: 'Peringatan Test', message: 'Ini adalah notifikasi peringatan untuk pengujian.' },
        'error': { title: 'Error Test', message: 'Ini adalah notifikasi error untuk pengujian.' },
        'info': { title: 'Info Test', message: 'Ini adalah notifikasi info untuk pengujian.' }
    };
    
    fetch('/api/notifications/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            type: type,
            title: messages[type].title,
            message: messages[type].message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            notificationSystem.addNotification(data.notification);
            
            Swal.fire({
                title: 'Test Berhasil!',
                text: `Notifikasi ${type} berhasil dibuat.`,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
    })
    .catch(error => {
        console.error('Error creating test notification:', error);
        Swal.fire(
            'Error!',
            'Gagal membuat notifikasi test.',
            'error'
        );
    });
}

function requestNotificationPermission() {
    notificationSystem.requestNotificationPermission();
}

// Initialize notification system when page loads
let notificationSystem;
document.addEventListener('DOMContentLoaded', function() {
    notificationSystem = new NotificationSystem();
});
</script>
@endpush
