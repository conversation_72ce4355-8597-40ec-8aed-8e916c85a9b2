<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'currency')) {
                $table->string('currency', 10)->default('IDR')->after('theme');
            }
            if (!Schema::hasColumn('users', 'date_format')) {
                $table->string('date_format', 20)->default('d/m/Y')->after('currency');
            }
            if (!Schema::hasColumn('users', 'time_format')) {
                $table->string('time_format', 5)->default('24')->after('date_format');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['currency', 'date_format', 'time_format']);
        });
    }
};
