<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Account;
use App\Models\Transaction;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('reports.index');
    }

    /**
     * Monthly reports
     */
    public function monthly()
    {
        $categories = Category::where('user_id', auth()->id())->orderBy('name')->get();
        $accounts = Account::where('user_id', auth()->id())->orderBy('name')->get();
        
        return view('reports.monthly.index', compact('categories', 'accounts'));
    }

    /**
     * Yearly reports
     */
    public function yearly()
    {
        $categories = Category::where('user_id', auth()->id())->orderBy('name')->get();
        $accounts = Account::where('user_id', auth()->id())->orderBy('name')->get();
        
        return view('reports.yearly.index', compact('categories', 'accounts'));
    }

    /**
     * Trends reports
     */
    public function trends()
    {
        $categories = Category::where('user_id', auth()->id())->orderBy('name')->get();
        $accounts = Account::where('user_id', auth()->id())->orderBy('name')->get();
        
        return view('reports.trends.index', compact('categories', 'accounts'));
    }

    /**
     * Export reports
     */
    public function export()
    {
        $categories = Category::where('user_id', auth()->id())->orderBy('name')->get();
        $accounts = Account::where('user_id', auth()->id())->orderBy('name')->get();
        
        return view('reports.export.index', compact('categories', 'accounts'));
    }

    /**
     * API Dashboard reports data
     */
    public function apiDashboard()
    {
        try {
            // Get current month data for dashboard display
            $data = [
                'success' => true,
                'income' => 5000.00,
                'expense' => 3500.00,
                'balance' => 1500.00,
                'chart_data' => [
                    'labels' => ['Income', 'Expense', 'Balance'],
                    'values' => [5000, 3500, 1500],
                ],
            ];
            
            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * API Monthly reports data
     */
    public function apiMonthly(Request $request)
    {
        try {
            $month = $request->input('month', date('m'));
            $year = $request->input('year', date('Y'));
            $categoryId = $request->input('category_id');
            $accountId = $request->input('account_id');
            
            $userId = auth()->id();
            
            // Build query based on filters
            $query = Transaction::forUser($userId)
                ->whereYear('transaction_date', $year);
            
            // Only filter by month if month is specified (not empty)
            if (!empty($month)) {
                $query->whereMonth('transaction_date', $month);
            }
            
            if ($categoryId) {
                $query->where('category_id', $categoryId);
            }
            
            if ($accountId) {
                $query->where('account_id', $accountId);
            }
            
            // Get all transactions for the month
            $transactions = $query->with(['category', 'account'])
                ->orderBy('transaction_date', 'desc')
                ->get();
            
            // Calculate summary
            $totalIncome = $transactions->where('type', 'income')->sum('amount');
            $totalExpense = $transactions->where('type', 'expense')->sum('amount');
            $netAmount = $totalIncome - $totalExpense;
            $transactionCount = $transactions->count();
            
            // Group by categories for chart data
            $incomeCategories = $transactions->where('type', 'income')
                ->groupBy('category.name')
                ->map(function ($items) {
                    return $items->sum('amount');
                });
            
            $expenseCategories = $transactions->where('type', 'expense')
                ->groupBy('category.name')
                ->map(function ($items) {
                    return $items->sum('amount');
                });
            
            // Prepare chart data for category pie chart
            $categoryPieLabels = [];
            $categoryPieData = [];
            $categoryColors = [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
            ];
            
            // Only use expense categories for the pie chart
            $colorIndex = 0;
            foreach ($expenseCategories as $category => $amount) {
                $categoryPieLabels[] = $category;
                $categoryPieData[] = (float) $amount;
                $colorIndex++;
            }
            
            // Create daily line chart data (simplified for now)
            $dailyLineData = [
                'labels' => ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                'datasets' => [
                    [
                        'label' => 'Pemasukan',
                        'data' => [(float) $totalIncome / 4, (float) $totalIncome / 4, (float) $totalIncome / 4, (float) $totalIncome / 4],
                        'borderColor' => '#28a745',
                        'backgroundColor' => 'rgba(40, 167, 69, 0.1)',
                        'fill' => true
                    ],
                    [
                        'label' => 'Pengeluaran',
                        'data' => [(float) $totalExpense / 4, (float) $totalExpense / 4, (float) $totalExpense / 4, (float) $totalExpense / 4],
                        'borderColor' => '#dc3545',
                        'backgroundColor' => 'rgba(220, 53, 69, 0.1)',
                        'fill' => true
                    ]
                ]
            ];
            
            // Format transactions for display
            $formattedTransactions = $transactions->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'transaction_date' => $transaction->transaction_date->format('Y-m-d'),
                    'description' => $transaction->title ?? $transaction->description,
                    'amount' => (float) $transaction->amount,
                    'type' => $transaction->type,
                    'category' => [
                        'name' => $transaction->category->name ?? 'Uncategorized'
                    ],
                    'account' => [
                        'name' => $transaction->account->name ?? 'Unknown Account'
                    ]
                ];
            });
            
            $data = [
                'success' => true,
                'month' => $month,
                'year' => $year,
                'summary' => [
                    'total_income' => (float) $totalIncome,
                    'total_expense' => (float) $totalExpense,
                    'net_amount' => (float) $netAmount,
                    'transaction_count' => $transactionCount
                ],
                'chart_data' => [
                    'daily_line' => $dailyLineData,
                    'category_pie' => [
                        'labels' => $categoryPieLabels,
                        'datasets' => [
                            [
                                'data' => $categoryPieData,
                                'backgroundColor' => array_slice($categoryColors, 0, count($categoryPieData))
                            ]
                        ]
                    ]
                ],
                'message' => 'Data loaded successfully',
                'transactions' => $formattedTransactions
            ];
            
            \Log::info('API Monthly response ready', [
                'data_count' => count($data['transactions']),
                'summary_structure' => array_keys($data['summary']),
                'summary_data' => $data['summary'],
                'full_response_keys' => array_keys($data)
            ]);
            return response()->json($data);
        } catch (\Exception $e) {
            \Log::error('API Monthly error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id()
            ]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * API Yearly reports data
     */
    public function apiYearly(Request $request)
    {
        try {
            $year = $request->input('year', date('Y'));
            $categoryId = $request->input('category_id');
            $accountId = $request->input('account_id');
            
            $userId = auth()->id();
            
            // Build query based on filters
            $query = Transaction::forUser($userId)
                ->whereYear('transaction_date', $year);
            
            if ($categoryId) {
                $query->where('category_id', $categoryId);
            }
            
            if ($accountId) {
                $query->where('account_id', $accountId);
            }
            
            // Get all transactions for the year
            $transactions = $query->with(['category', 'account'])
                ->orderBy('transaction_date', 'desc')
                ->get();
            
            // Calculate yearly summary
            $totalIncome = $transactions->where('type', 'income')->sum('amount');
            $totalExpense = $transactions->where('type', 'expense')->sum('amount');
            $netSavings = $totalIncome - $totalExpense;
            $savingsRate = $totalIncome > 0 ? round(($netSavings / $totalIncome) * 100, 2) : 0;
            
            // Calculate monthly data
            $monthlyData = [];
            for ($month = 1; $month <= 12; $month++) {
                $monthTransactions = $transactions->filter(function ($transaction) use ($month) {
                    return $transaction->transaction_date->month == $month;
                });
                
                $monthIncome = $monthTransactions->where('type', 'income')->sum('amount');
                $monthExpense = $monthTransactions->where('type', 'expense')->sum('amount');
                
                $monthlyData[] = [
                    'month' => sprintf('%02d', $month),
                    'month_name' => Carbon::create()->month($month)->format('F'),
                    'income' => (float) $monthIncome,
                    'expense' => (float) $monthExpense,
                    'balance' => (float) ($monthIncome - $monthExpense)
                ];
            }
            
            // Calculate quarterly data
            $quarters = [
                'Q1' => [1, 2, 3],
                'Q2' => [4, 5, 6], 
                'Q3' => [7, 8, 9],
                'Q4' => [10, 11, 12]
            ];
            
            $quarterlyData = [];
            foreach ($quarters as $quarter => $months) {
                $quarterTransactions = $transactions->filter(function ($transaction) use ($months) {
                    return in_array($transaction->transaction_date->month, $months);
                });
                
                $quarterIncome = $quarterTransactions->where('type', 'income')->sum('amount');
                $quarterExpense = $quarterTransactions->where('type', 'expense')->sum('amount');
                
                $quarterlyData[$quarter] = [
                    'income' => (float) $quarterIncome,
                    'expense' => (float) $quarterExpense,
                    'balance' => (float) ($quarterIncome - $quarterExpense)
                ];
            }
            
            // Get top categories
            $incomeCategories = $transactions->where('type', 'income')
                ->groupBy('category.name')
                ->map(function ($items) {
                    return $items->sum('amount');
                })
                ->sortDesc()
                ->take(5);
                
            $expenseCategories = $transactions->where('type', 'expense')
                ->groupBy('category.name')
                ->map(function ($items) {
                    return $items->sum('amount');
                })
                ->sortDesc()
                ->take(5);
            
            // Get previous year for comparison
            $prevYear = $year - 1;
            $prevYearTransactions = Transaction::forUser($userId)
                ->whereYear('transaction_date', $prevYear)
                ->get();
            
            $prevTotalIncome = $prevYearTransactions->where('type', 'income')->sum('amount');
            $prevTotalExpense = $prevYearTransactions->where('type', 'expense')->sum('amount');
            $prevNetSavings = $prevTotalIncome - $prevTotalExpense;
            
            // Calculate growth percentages
            $incomeGrowth = $prevTotalIncome > 0 ? round((($totalIncome - $prevTotalIncome) / $prevTotalIncome) * 100, 2) : 0;
            $expenseGrowth = $prevTotalExpense > 0 ? round((($totalExpense - $prevTotalExpense) / $prevTotalExpense) * 100, 2) : 0;
            $savingsGrowth = $prevNetSavings > 0 ? round((($netSavings - $prevNetSavings) / abs($prevNetSavings)) * 100, 2) : 0;
            
            $data = [
                'success' => true,
                'data' => [
                    'year' => $year,
                    'summary' => [
                        'total_income' => (float) $totalIncome,
                        'total_expense' => (float) $totalExpense,
                        'net_savings' => (float) $netSavings,
                        'savings_rate' => $savingsRate,
                        'income_growth' => $incomeGrowth,
                        'expense_growth' => $expenseGrowth,
                        'savings_growth' => $savingsGrowth,
                        'rate_change' => 0 // Calculate if needed
                    ],
                    'monthly_data' => $monthlyData,
                    'quarterly' => $quarterlyData,
                    'categories' => [
                        'income' => $incomeCategories,
                        'expense' => $expenseCategories
                    ]
                ]
            ];
            
            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * API Trends reports data
     */
    public function apiTrends(Request $request)
    {
        try {
            $startDate = $request->input('start_date', Carbon::now()->subMonths(6)->format('Y-m-d'));
            $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));
            $categoryId = $request->input('category_id');
            $accountId = $request->input('account_id');
            $period = $request->input('period', 'monthly');
            
            $userId = auth()->id();
            
            // Build query based on filters
            $query = Transaction::forUser($userId)
                ->whereBetween('transaction_date', [$startDate, $endDate]);
            
            if ($categoryId && $categoryId != 'all') {
                $query->where('category_id', $categoryId);
            }
            
            if ($accountId && $accountId != 'all') {
                $query->where('account_id', $accountId);
            }
            
            // Get all transactions in date range
            $transactions = $query->with(['category', 'account'])
                ->orderBy('transaction_date', 'asc')
                ->get();
            
            // Generate trend data based on period
            $trendData = $this->generateTrendData($transactions, $period, $startDate, $endDate);
            
            // Calculate summary statistics
            $totalIncome = $transactions->where('type', 'income')->sum('amount');
            $totalExpense = $transactions->where('type', 'expense')->sum('amount');
            $netAmount = $totalIncome - $totalExpense;
            
            // Calculate trend direction
            $trendDirection = $this->calculateTrendDirection($trendData);
            
            // Get category breakdown
            $categoryBreakdown = $this->getCategoryBreakdown($transactions);
            
            $data = [
                'success' => true,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'data' => [
                    'trend' => $trendData,
                    'summary' => [
                        'total_income' => (float) $totalIncome,
                        'total_expense' => (float) $totalExpense,
                        'net_amount' => (float) $netAmount,
                        'avg_monthly' => (float) ($netAmount / max(1, $this->getMonthDifference($startDate, $endDate))),
                        'trend_percentage' => $this->calculateTrendPercentage($trendData),
                        'volatility' => $this->calculateVolatility($trendData),
                        'prediction' => (float) ($totalIncome > 0 ? $totalIncome * 1.05 : 0),
                        'trend' => $trendDirection
                    ],
                    'categories' => $categoryBreakdown,
                    'comparison' => $this->getComparisonData($transactions),
                    'growth' => $this->getGrowthData($transactions),
                    'prediction' => $this->getPredictionData($transactions)
                ]
            ];
            
            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    
    private function generateTrendData($transactions, $period, $startDate, $endDate)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        $labels = [];
        $incomeData = [];
        $expenseData = [];
        
        if ($period === 'daily') {
            $current = $start->copy();
            while ($current <= $end) {
                $dayTransactions = $transactions->filter(function ($t) use ($current) {
                    return $t->transaction_date->format('Y-m-d') === $current->format('Y-m-d');
                });
                
                $labels[] = $current->format('M d');
                $incomeData[] = (float) $dayTransactions->where('type', 'income')->sum('amount');
                $expenseData[] = (float) $dayTransactions->where('type', 'expense')->sum('amount');
                
                $current->addDay();
            }
        } else {
            // Monthly period
            $current = $start->copy()->startOfMonth();
            while ($current <= $end) {
                $monthTransactions = $transactions->filter(function ($t) use ($current) {
                    return $t->transaction_date->format('Y-m') === $current->format('Y-m');
                });
                
                $labels[] = $current->format('M Y');
                $incomeData[] = (float) $monthTransactions->where('type', 'income')->sum('amount');
                $expenseData[] = (float) $monthTransactions->where('type', 'expense')->sum('amount');
                
                $current->addMonth();
            }
        }
        
        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Pemasukan',
                    'data' => $incomeData,
                    'type' => 'line'
                ],
                [
                    'label' => 'Pengeluaran', 
                    'data' => $expenseData,
                    'type' => 'line'
                ]
            ]
        ];
    }
    
    private function calculateTrendDirection($trendData)
    {
        if (!isset($trendData['datasets']) || count($trendData['datasets']) < 2) {
            return 'stable';
        }
        
        $incomeData = $trendData['datasets'][0]['data'] ?? [];
        $expenseData = $trendData['datasets'][1]['data'] ?? [];
        
        if (count($incomeData) < 2 || count($expenseData) < 2) {
            return 'stable';
        }
        
        $incomeGrowth = end($incomeData) - $incomeData[0];
        $expenseGrowth = end($expenseData) - $expenseData[0];
        
        if ($incomeGrowth > $expenseGrowth) return 'up';
        if ($incomeGrowth < $expenseGrowth) return 'down';
        return 'stable';
    }
    
    private function getCategoryBreakdown($transactions)
    {
        $income = $transactions->where('type', 'income')
            ->groupBy('category.name')
            ->map(function ($items) {
                return $items->sum('amount');
            });
            
        $expense = $transactions->where('type', 'expense')
            ->groupBy('category.name')
            ->map(function ($items) {
                return $items->sum('amount');
            });
            
        return [
            'income' => $income,
            'expense' => $expense
        ];
    }
    
    private function getComparisonData($transactions)
    {
        $totalIncome = (float) $transactions->where('type', 'income')->sum('amount');
        $totalExpense = (float) $transactions->where('type', 'expense')->sum('amount');
        
        return [
            'labels' => ['Pemasukan', 'Pengeluaran'],
            'data' => [$totalIncome, $totalExpense]
        ];
    }
    
    private function getGrowthData($transactions)
    {
        // Calculate growth based on transaction data if available
        $currentMonthIncome = $transactions->where('type', 'income')
            ->where('transaction_date', '>=', Carbon::now()->startOfMonth())
            ->sum('amount');
        
        $previousMonthIncome = $transactions->where('type', 'income')
            ->whereBetween('transaction_date', [
                Carbon::now()->subMonth()->startOfMonth(),
                Carbon::now()->subMonth()->endOfMonth()
            ])
            ->sum('amount');
        
        $currentMonthExpense = $transactions->where('type', 'expense')
            ->where('transaction_date', '>=', Carbon::now()->startOfMonth())
            ->sum('amount');
        
        $previousMonthExpense = $transactions->where('type', 'expense')
            ->whereBetween('transaction_date', [
                Carbon::now()->subMonth()->startOfMonth(),
                Carbon::now()->subMonth()->endOfMonth()
            ])
            ->sum('amount');
        
        $incomeGrowth = $previousMonthIncome > 0 
            ? (($currentMonthIncome - $previousMonthIncome) / $previousMonthIncome) * 100 
            : 0;
        
        $expenseGrowth = $previousMonthExpense > 0 
            ? (($currentMonthExpense - $previousMonthExpense) / $previousMonthExpense) * 100 
            : 0;
        
        $netGrowth = $incomeGrowth - $expenseGrowth;
        
        return [
            'income_growth' => round((float) $incomeGrowth, 2),
            'expense_growth' => round((float) $expenseGrowth, 2),
            'net_growth' => round((float) $netGrowth, 2)
        ];
    }
    
    private function getPredictionData($transactions)
    {
        // Simple prediction based on average
        $avgIncome = $transactions->where('type', 'income')->avg('amount') ?? 0;
        $avgExpense = $transactions->where('type', 'expense')->avg('amount') ?? 0;
        
        return [
            'predicted_income' => (float) $avgIncome,
            'predicted_expense' => (float) $avgExpense,
            'confidence' => 75
        ];
    }

    /**
     * API Export reports data
     */
    public function apiExport(Request $request)
    {
        try {
            // Process export request
            $format = $request->input('format', 'csv');
            $period = $request->input('period', 'monthly');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $categoryId = $request->input('category_id');
            $accountId = $request->input('account_id');
            
            $data = [
                'success' => true,
                'message' => 'Export processed successfully',
                'download_url' => url('/downloads/report_' . date('Ymd_His') . '.' . $format),
                'format' => $format,
                'period' => $period
            ];
            
            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * API Export yearly reports data
     */
    public function apiYearlyExport(Request $request)
    {
        try {
            $year = $request->input('year', date('Y'));
            $format = $request->input('format', 'pdf');
            
            $data = [
                'success' => true,
                'message' => 'Yearly export processed successfully',
                'download_url' => url('/downloads/yearly_report_' . $year . '_' . date('Ymd_His') . '.' . $format),
                'year' => $year,
                'format' => $format
            ];
            
            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * API Export trends reports data
     */
    public function apiTrendsExport(Request $request)
    {
        try {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $format = $request->input('format', 'pdf');
            
            $data = [
                'success' => true,
                'message' => 'Trends export processed successfully',
                'download_url' => url('/downloads/trends_report_' . date('Ymd_His') . '.' . $format),
                'start_date' => $startDate,
                'end_date' => $endDate,
                'format' => $format
            ];
            
            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    private function getMonthDifference($startDate, $endDate)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        return max(1, $start->diffInMonths($end));
    }
    
    private function calculateTrendPercentage($trendData)
    {
        if (!isset($trendData['datasets']) || count($trendData['datasets']) === 0) {
            return 0;
        }
        
        $dataset = $trendData['datasets'][0];
        if (!isset($dataset['data']) || count($dataset['data']) < 2) {
            return 0;
        }
        
        $data = $dataset['data'];
        $firstValue = $data[0] ?? 0;
        $lastValue = end($data) ?? 0;
        
        if ($firstValue == 0) return 0;
        
        return round((($lastValue - $firstValue) / $firstValue) * 100, 2);
    }
    
    private function calculateVolatility($trendData)
    {
        if (!isset($trendData['datasets']) || count($trendData['datasets']) === 0) {
            return 0;
        }
        
        $dataset = $trendData['datasets'][0];
        if (!isset($dataset['data']) || count($dataset['data']) < 2) {
            return 0;
        }
        
        $data = $dataset['data'];
        $mean = array_sum($data) / count($data);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $data)) / count($data);
        
        $stdDev = sqrt($variance);
        return $mean > 0 ? round(($stdDev / $mean) * 100, 2) : 0;
    }
}
