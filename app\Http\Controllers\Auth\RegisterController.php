<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SecurityLog;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/dashboard';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */    protected function validator(array $data)
    {        return Validator::make($data, [
            'name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
            'username' => [
                'required', 
                'string', 
                'max:255', 
                'unique:users',
                'regex:/^[a-zA-Z0-9_]+$/', // Only alphanumeric and underscore
                'min:3',
                'max:20'
            ],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => [
                'required', 
                'string', 
                'min:12', // Increased from 8 to 12
                'max:128',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&^#()])[A-Za-z\d@$!%*?&^#()]+$/',
                'confirmed'
            ],
        ], [
            'name.required' => 'Nama lengkap wajib diisi.',
            'name.regex' => 'Nama hanya boleh mengandung huruf dan spasi.',
            'username.required' => 'Username wajib diisi.',
            'username.unique' => 'Username sudah digunakan.',
            'username.regex' => 'Username hanya boleh mengandung huruf, angka, dan underscore.',
            'username.min' => 'Username minimal 3 karakter.',
            'username.max' => 'Username maksimal 20 karakter.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.unique' => 'Email sudah terdaftar.',
            'password.required' => 'Password wajib diisi.',
            'password.min' => 'Password minimal 12 karakter.',
            'password.max' => 'Password maksimal 128 karakter.',
            'password.regex' => 'Password harus mengandung: huruf besar, huruf kecil, angka, dan karakter khusus (@$!%*?&^#()).',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',
        ]);
    }    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\Models\User
     */
    protected function create(array $data)
    {
        $user = User::create([
            'name' => $data['name'],
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'verification_token' => Str::random(64),
            'is_active' => false, // Will be activated after email verification
            'timezone' => 'Asia/Jakarta',
            'language' => 'id',
            'theme' => 'light',
            'password_changed_at' => now(), // Track password creation
            'last_activity' => now(),
        ]);

        // Generate backup codes for new user
        $user->generateBackupCodes();

        // Log registration event
        SecurityLog::logEvent(
            $user->id,
            'user_registered',
            'User baru terdaftar: ' . $user->username,
            'low',
            [
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]
        );

        return $user;
    }    /**
     * Handle a registration request for the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */    public function register(Request $request)
    {
        $this->validator($request->all())->validate();

        // Check for suspicious registration patterns
        $this->checkSuspiciousRegistration($request);

        event(new Registered($user = $this->create($request->all())));

        // Don't auto-login, require email verification first
        return redirect()->route('login')
            ->with('success', 'Pendaftaran berhasil! Silakan cek email untuk aktivasi akun Anda.');
    }    /**
     * Check for suspicious registration patterns
     */
    private function checkSuspiciousRegistration(Request $request): void
    {
        $ip = $request->ip();
        
        // Check for multiple registrations from same IP
        $recentRegistrations = SecurityLog::where('ip_address', $ip)
            ->where('event_type', 'user_registered')
            ->where('created_at', '>=', now()->subHour())
            ->count();

        if ($recentRegistrations >= 3) {
            SecurityLog::logEvent(
                null,
                'suspicious_registration',
                'Multiple registrations from same IP',
                'high',
                [
                    'ip' => $ip,
                    'registration_count' => $recentRegistrations,
                    'user_agent' => $request->userAgent()
                ]
            );

            throw ValidationException::withMessages([
                'email' => ['Terlalu banyak pendaftaran dari IP ini. Silakan coba lagi nanti.'],
            ]);
        }

        // Check for suspicious email patterns
        $email = $request->email;
        $emailDomain = explode('@', $email)[1] ?? '';
        
        // List of suspicious/temporary email providers
        $suspiciousDomains = [
            'tempmail.org', '10minutemail.com', 'guerrillamail.com', 'mailinator.com',
            'throwaway.email', 'temp-mail.org', 'fakeinbox.com', 'maildrop.cc'
        ];
        
        if (in_array($emailDomain, $suspiciousDomains)) {
            SecurityLog::logEvent(
                null,
                'suspicious_email_domain',
                'Registration with suspicious email domain',
                'medium',
                [
                    'ip' => $ip,
                    'email_domain' => $emailDomain,
                    'user_agent' => $request->userAgent()
                ]
            );            throw ValidationException::withMessages([
                'email' => ['Email domain tidak diizinkan. Gunakan email yang valid.'],
            ]);
        }
        
        // Check for suspicious username patterns
        $username = $request->username;
        $suspiciousPatterns = [
            '/admin/i', '/root/i', '/test/i', '/demo/i', '/null/i', '/undefined/i',
            '/bot/i', '/crawler/i', '/spider/i', '/hack/i', '/exploit/i'
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $username)) {                SecurityLog::logEvent(
                    null,
                    'suspicious_username',
                    'Registration with suspicious username pattern',
                    'medium',
                    [
                        'ip' => $ip,
                        'username' => $username,
                        'pattern' => $pattern
                    ]
                );
                
                throw ValidationException::withMessages([
                    'username' => ['Username tidak diizinkan. Pilih username yang berbeda.'],
                ]);
            }
        }
    }
}
