<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Enable security middleware with detailed logging for debugging
        $middleware->web(append: [
            \App\Http\Middleware\SecurityMiddleware::class,
            \App\Http\Middleware\AdvancedSecurityMiddleware::class,
            \App\Http\Middleware\BypassWafMiddleware::class,
            \App\Http\Middleware\PageTransitionMiddleware::class,
        ]);
        
        // Add middleware aliases
        $middleware->alias([
            'security' => \App\Http\Middleware\SecurityMiddleware::class,
            'advanced.security' => \App\Http\Middleware\AdvancedSecurityMiddleware::class,
            'bypass.waf' => \App\Http\Middleware\BypassWafMiddleware::class,
            'page.transition' => \App\Http\Middleware\PageTransitionMiddleware::class,
            '2fa' => \App\Http\Middleware\TwoFactorAuthMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
