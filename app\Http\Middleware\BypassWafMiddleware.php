<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BypassWafMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip WAF bypass for API routes
        if ($request->is('*/api/*') || $request->is('api/*') || 
            $request->is('reports/api/*') || $request->is('emergency/*') ||
            $request->is('test-reports-*')) {
            return $next($request);
        }
        
        // Sanitize request data to prevent WAF false positives
        if ($request->isMethod('POST') || $request->isMethod('PUT')) {
            $input = $request->all();
            
            foreach ($input as $key => $value) {
                if (is_string($value)) {
                    // Remove or encode characters that might trigger WAF
                    $sanitized = htmlspecialchars($value, ENT_QUOTES, 'UTF-8', false);
                    $sanitized = preg_replace('/[<>"\';{}()]/', '', $sanitized);
                    $request->merge([$key => $sanitized]);
                }
            }
        }
        
        // Add headers to help bypass WAF
        $response = $next($request);
        
        if (method_exists($response, 'header')) {
            $response->header('X-Content-Type-Options', 'nosniff');
            $response->header('X-Frame-Options', 'SAMEORIGIN');
            $response->header('X-XSS-Protection', '1; mode=block');
        }
        
        return $response;
    }
}
