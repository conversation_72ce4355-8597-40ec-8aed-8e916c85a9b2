<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Auth\Events\Verified;
use App\Models\User;

class CustomVerificationController extends Controller
{
    /**
     * Show the email verification notice.
     */
    public function show()
    {
        return auth()->user()->hasVerifiedEmail()
            ? redirect()->route('dashboard')
            : view('auth.verify');
    }

    /**
     * Mark the authenticated user's email address as verified.
     */
    public function verify(Request $request)
    {
        $user = User::find($request->route('id'));

        if (!$user) {
            return redirect()->route('login')->with('error', 'User tidak ditemukan.');
        }

        if (!hash_equals((string) $request->route('hash'), sha1($user->getEmailForVerification()))) {
            return redirect()->route('login')->with('error', 'Link verifikasi tidak valid.');
        }

        if ($user->hasVerifiedEmail()) {
            return redirect()->route('login')->with('info', 'Email sudah terverifikasi sebelumnya.');
        }        if ($user->markEmailAsVerified()) {
            // Activate the account when email is verified
            $user->update(['is_active' => true]);
            event(new Verified($user));
        }

        return redirect()->route('login')->with('success', 'Email berhasil diverifikasi! Akun Anda telah diaktifkan. Silakan login.');
    }

    /**
     * Resend the email verification notification.
     */
    public function resend(Request $request)
    {
        // Check if user is authenticated for resend from verify page
        if (auth()->check()) {
            $user = auth()->user();
            
            if ($user->hasVerifiedEmail()) {
                return redirect()->route('dashboard');
            }
            
            $user->sendEmailVerificationNotification();
            
            return back()->with('success', 'Link verifikasi telah dikirim ulang ke email Anda!');
        }
        
        // For resend from login page (verify form)
        $request->validate([
            'email' => 'required|email'
        ]);
        
        $user = User::where('email', $request->email)->first();
        
        if (!$user) {
            return redirect()->route('login')->with('warning', 'Email tidak ditemukan dalam sistem kami.');
        }
        
        if ($user->hasVerifiedEmail()) {
            return redirect()->route('login')->with('info', 'Email tersebut sudah terverifikasi. Silakan login.');
        }
        
        $user->sendEmailVerificationNotification();
        
        return redirect()->route('login')->with('success', 'Link verifikasi telah dikirim ke email Anda!');
    }
}
