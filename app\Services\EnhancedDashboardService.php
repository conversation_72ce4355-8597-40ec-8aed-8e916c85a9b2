<?php

namespace App\Services;

use Pusher\Pusher;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Transaction;
use App\Models\Account;
use App\Models\Category;
use Carbon\Carbon;

class EnhancedDashboardService
{
    protected $pusher;

    public function __construct()
    {
        if (config('broadcasting.connections.pusher.key')) {
            $this->pusher = new Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id'),
                [
                    'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                    'useTLS' => true
                ]
            );
        }
    }

    /**
     * Get monthly history for income and expense
     */
    public function getMonthlyHistory($userId = null, $months = 6)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return [];
        }
        
        $history = [];
        
        for ($i = 0; $i < $months; $i++) {
            $month = Carbon::now()->subMonths($i);
            $monthStart = $month->copy()->startOfMonth();
            $monthEnd = $month->copy()->endOfMonth();
            
            $income = Transaction::where('user_id', $userId)
                ->where('type', 'income')
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->sum('amount');
                
            $expense = Transaction::where('user_id', $userId)
                ->where('type', 'expense')
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->sum('amount');
            
            $history[] = [
                'month' => $month->format('M Y'),
                'month_full' => $month->format('F Y'),
                'income' => $income,
                'expense' => $expense,
                'net' => $income - $expense,
                'is_current' => $i === 0
            ];
        }
        
        return array_reverse($history); // Oldest first
    }

    /**
     * Get transaction statistics by month
     */
    public function getMonthlyTransactionStats($userId = null, $months = 3)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return [];
        }
        
        $stats = [];
        
        for ($i = 0; $i < $months; $i++) {
            $month = Carbon::now()->subMonths($i);
            $monthStart = $month->copy()->startOfMonth();
            $monthEnd = $month->copy()->endOfMonth();
            
            $transactionCount = Transaction::where('user_id', $userId)
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->count();
                
            $incomeTransactions = Transaction::where('user_id', $userId)
                ->where('type', 'income')
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->count();
                
            $expenseTransactions = Transaction::where('user_id', $userId)
                ->where('type', 'expense')
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->count();
            
            $stats[] = [
                'month' => $month->format('M Y'),
                'month_full' => $month->format('F Y'),
                'total_transactions' => $transactionCount,
                'income_transactions' => $incomeTransactions,
                'expense_transactions' => $expenseTransactions,
                'is_current' => $i === 0
            ];
        }
        
        return array_reverse($stats); // Oldest first
    }
}
