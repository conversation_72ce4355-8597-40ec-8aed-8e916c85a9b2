<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recurring_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('account_id');
            $table->unsignedBigInteger('category_id');
            $table->unsignedBigInteger('to_account_id')->nullable();
            $table->string('type')->default('expense'); // expense, income, transfer
            $table->decimal('amount', 15, 2);
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('frequency'); // daily, weekly, monthly, yearly
            $table->integer('interval_count')->default(1); // every X days/weeks/months/years
            $table->json('frequency_details')->nullable(); // for complex schedules
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->integer('max_occurrences')->nullable();
            $table->date('next_due_date');
            $table->date('last_generated_date')->nullable();
            $table->string('payment_method')->nullable();
            $table->json('tags')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('auto_generate')->default(true);
            $table->integer('days_before_reminder')->default(1);
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'next_due_date']);
            $table->index(['is_active', 'auto_generate']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recurring_transactions');
    }
};
