<?php

if (!function_exists('formatRupiah')) {
    /**
     * Format amount to Indonesian Rupiah format
     * 
     * @param float|int $amount
     * @param bool $showSymbol Whether to show "Rp" symbol
     * @return string
     */
    function formatRupiah($amount, $showSymbol = true) {
        $formatted = number_format($amount, 0, ',', '.');
        return $showSymbol ? 'Rp ' . $formatted : $formatted;
    }
}

if (!function_exists('formatRupiahJS')) {
    /**
     * Format amount to Indonesian Rupiah format for JavaScript
     * 
     * @param float|int $amount
     * @param bool $showSymbol Whether to show "Rp" symbol
     * @return string
     */
    function formatRupiahJS($amount, $showSymbol = true) {
        return "new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format($amount)";
    }
}

if (!function_exists('parseRupiah')) {
    /**
     * Parse Indonesian Rupiah format to number
     * 
     * @param string $rupiahString
     * @return float
     */
    function parseRupiah($rupiahString) {
        // Remove "Rp", spaces, and dots, then convert commas to dots for decimal
        $cleaned = str_replace(['Rp', ' ', '.'], '', $rupiahString);
        $cleaned = str_replace(',', '.', $cleaned);
        return (float) $cleaned;
    }
}
