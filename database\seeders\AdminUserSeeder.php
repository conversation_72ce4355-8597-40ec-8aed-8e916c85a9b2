<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */    public function run(): void
    {
        \App\Models\User::create([
            'name' => 'Administrator',
            'username' => 'admin',
            'full_name' => 'Administrator My Money',
            'email' => '<EMAIL>',
            'password' => bcrypt('Admin123!'),
            'email_verified_at' => now(),
            'is_active' => true,
            'timezone' => 'Asia/Jakarta',
            'language' => 'id',
            'theme' => 'light',
        ]);
        
        \App\Models\User::create([
            'name' => 'Test User',
            'username' => 'testuser',
            'full_name' => 'Test User My Money',
            'email' => '<EMAIL>',
            'password' => bcrypt('Test123!'),
            'email_verified_at' => now(),
            'is_active' => true,
            'timezone' => 'Asia/Jakarta',
            'language' => 'id',
            'theme' => 'light',
        ]);
    }
}
