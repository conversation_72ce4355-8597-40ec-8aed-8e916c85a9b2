<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'My Money') }} - Dashboard</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Bootstrap & Custom CSS -->
    @vite(['resources/sass/app.scss'])
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Figtree', sans-serif;
        }
        
        .dashboard-container {
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .welcome-section h1 {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .welcome-section p {
            opacity: 0.9;
            margin: 0;
        }
        
        .user-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-logout {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .dashboard-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .card-subtitle {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .card-content {
            color: #555;
            line-height: 1.6;
        }
        
        .finance-icon {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .security-icon {
            background: linear-gradient(45deg, #11998e, #38ef7d);
            color: white;
        }
        
        .stats-icon {
            background: linear-gradient(45deg, #fa709a, #fee140);
            color: white;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0 0 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .quick-actions h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
            display: block;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        /* Theme Switcher */
        .theme-switcher-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .theme-switcher {
            position: relative;
        }
        
        .theme-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .theme-icon {
            font-size: 1rem;
        }
        
        .dropdown-arrow {
            font-size: 0.7rem;
            transition: transform 0.3s ease;
        }
        
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
            
            .welcome-section h1 {
                font-size: 1.5rem;
            }
            
            .dashboard-content {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
        }
        
        /* Theme Variations */
        /* Solarized Dark Theme */
        body[data-theme="solarized-dark"] {
            background: linear-gradient(135deg, #002b36 0%, #073642 100%);
        }
        
        body[data-theme="solarized-dark"] .dashboard-card {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .quick-actions {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .card-title {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .card-subtitle {
            color: #657b83;
        }
        
        body[data-theme="solarized-dark"] .card-content {
            color: #839496;
        }
        
        body[data-theme="solarized-dark"] .action-btn {
            background: linear-gradient(45deg, #268bd2, #2aa198);
        }
        
        /* Synth Wave Theme */
        body[data-theme="synth-wave"] {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a0033 50%, #000000 100%);
        }
        
        body[data-theme="synth-wave"] .dashboard-card {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .quick-actions {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .card-title {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .card-subtitle {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .card-content {
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .action-btn {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.4);
        }
        
        body[data-theme="synth-wave"] .quick-actions h3 {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
    </style>
</head>
<body>
    <!-- Theme Switcher -->
    <div class="theme-switcher-container">
        <div class="theme-switcher">
            <button class="theme-toggle" id="themeToggle" onclick="toggleThemeDropdown()">
                <i class="theme-icon" id="themeIcon">🌞</i>
                <span class="theme-text" id="themeText">Light</span>
                <i class="dropdown-arrow">▼</i>
            </button>
            <div class="theme-dropdown" id="themeDropdown">
                <div class="theme-option" onclick="setTheme('light')">
                    <i class="theme-option-icon">🌞</i>
                    <span>Light</span>
                </div>
                <div class="theme-option" onclick="setTheme('solarized-dark')">
                    <i class="theme-option-icon">🌙</i>
                    <span>Solarized Dark</span>
                </div>
                <div class="theme-option" onclick="setTheme('synth-wave')">
                    <i class="theme-option-icon">🌆</i>
                    <span>Synth Wave</span>
                </div>
            </div>
        </div>
    </div>

    <div class="dashboard-container">
        <!-- Header -->
        <div class="dashboard-header">
            <div class="welcome-section">
                <h1>Selamat datang, {{ Auth::user()->name }}!</h1>
                <p>Kelola keuangan Anda dengan mudah dan efisien</p>
            </div>            <div class="user-actions">
                <form id="logoutForm" method="POST" action="{{ route('logout') }}" style="display: none;">
                    @csrf
                </form>
                <button class="btn-logout" onclick="confirmLogout()">
                    🚪 Logout
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="dashboard-content">
            <!-- Financial Overview Card -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon finance-icon">💰</div>
                    <div>
                        <h3 class="card-title">Ringkasan Keuangan</h3>
                        <p class="card-subtitle">Gambaran keuangan Anda saat ini</p>
                    </div>
                </div>
                <div class="card-content">
                    <p>Selamat datang di dashboard keuangan pribadi Anda! Dari sini Anda dapat mengelola semua aspek keuangan dengan mudah.</p>
                    <ul class="feature-list">
                        <li>Catat pemasukan dan pengeluaran</li>
                        <li>Analisis pola pengeluaran</li>
                        <li>Buat anggaran bulanan</li>
                        <li>Pantau target tabungan</li>
                    </ul>
                </div>
            </div>

            <!-- Security Card -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon security-icon">🔒</div>
                    <div>
                        <h3 class="card-title">Keamanan Akun</h3>
                        <p class="card-subtitle">Status keamanan akun Anda</p>
                    </div>
                </div>
                <div class="card-content">
                    <p>Akun Anda terlindungi dengan sistem keamanan terdepan untuk menjaga privasi data keuangan Anda.</p>
                    <ul class="feature-list">
                        <li>Email terverifikasi ✓</li>
                        <li>Login dengan enkripsi</li>
                        <li>Sistem captcha aktif</li>
                        <li>Autentikasi multi-faktor</li>
                    </ul>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon stats-icon">📊</div>
                    <div>
                        <h3 class="card-title">Statistik & Laporan</h3>
                        <p class="card-subtitle">Analisis mendalam keuangan Anda</p>
                    </div>
                </div>
                <div class="card-content">
                    <p>Dapatkan wawasan mendalam tentang pola keuangan Anda dengan berbagai laporan dan grafik interaktif.</p>
                    <ul class="feature-list">
                        <li>Grafik pemasukan vs pengeluaran</li>
                        <li>Kategorisasi otomatis</li>
                        <li>Laporan bulanan/tahunan</li>
                        <li>Prediksi keuangan</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3>🚀 Aksi Cepat</h3>
            <div class="action-buttons">
                <a href="#" class="action-btn">💰 Tambah Pemasukan</a>
                <a href="#" class="action-btn">💸 Catat Pengeluaran</a>
                <a href="#" class="action-btn">📊 Lihat Laporan</a>
                <a href="#" class="action-btn">⚙️ Pengaturan</a>
                <a href="#" class="action-btn">👤 Profil</a>
                <a href="#" class="action-btn">❓ Bantuan</a>
            </div>
        </div>
    </div>
    
    @vite(['resources/js/app.js'])
    
    <script>
        // Theme functionality
        function toggleThemeDropdown() {
            const dropdown = document.getElementById('themeDropdown');
            dropdown.classList.toggle('show');
        }
        
        function setTheme(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');
            
            switch(theme) {
                case 'light':
                    themeIcon.textContent = '🌞';
                    themeText.textContent = 'Light';
                    break;
                case 'solarized-dark':
                    themeIcon.textContent = '🌙';
                    themeText.textContent = 'Solarized Dark';
                    break;
                case 'synth-wave':
                    themeIcon.textContent = '🌆';
                    themeText.textContent = 'Synth Wave';
                    break;
            }
            
            document.getElementById('themeDropdown').classList.remove('show');
        }
        
        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            setTheme(savedTheme);
            
            // Show welcome greeting only once per session
            const hasShownGreeting = sessionStorage.getItem('hasShownGreeting');
            if (!hasShownGreeting) {
                setTimeout(() => {
                    showWelcomeGreeting();
                    sessionStorage.setItem('hasShownGreeting', 'true');
                }, 1000);
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const themeSwitcher = document.querySelector('.theme-switcher');
            if (!themeSwitcher.contains(event.target)) {
                document.getElementById('themeDropdown').classList.remove('show');
            }
        });
        
        // Welcome greeting function
        function showWelcomeGreeting() {
            const userName = '{{ Auth::user()->name }}';
            const currentHour = new Date().getHours();
            let greeting = '';
            let icon = '';
            
            if (currentHour < 12) {
                greeting = 'Selamat Pagi';
                icon = '🌅';
            } else if (currentHour < 17) {
                greeting = 'Selamat Siang';
                icon = '☀️';
            } else if (currentHour < 21) {
                greeting = 'Selamat Sore';
                icon = '🌇';
            } else {
                greeting = 'Selamat Malam';
                icon = '🌙';
            }
            
            Swal.fire({
                title: `${icon} ${greeting}, ${userName}!`,
                text: 'Selamat datang kembali di My Money. Siap mengelola keuangan Anda hari ini?',
                icon: 'success',
                confirmButtonText: 'Mulai Sekarang!',
                confirmButtonColor: '#667eea',
                timer: 5000,
                timerProgressBar: true,
                allowOutsideClick: false,
                customClass: {
                    popup: 'animated bounceIn'
                }
            });
        }
          // Logout confirmation
        function confirmLogout() {
            Swal.fire({
                title: '🚪 Logout Konfirmasi',
                text: 'Apakah Anda yakin ingin keluar dari akun?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#dc3545',
                confirmButtonText: 'Ya, Logout',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show logout success message
                    Swal.fire({
                        title: '👋 Sampai Jumpa!',
                        text: 'Anda telah berhasil logout. Terima kasih telah menggunakan My Money!',
                        icon: 'success',
                        timer: 2000,
                        timerProgressBar: true,
                        showConfirmButton: false
                    }).then(() => {
                        // Clear session storage
                        sessionStorage.clear();
                        // Submit logout form
                        document.getElementById('logoutForm').submit();
                    });
                }
            });
        }
        
        // Flash Messages with SweetAlert2
        @if(session('status'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('status') }}',
                confirmButtonColor: '#667eea',
                timer: 3000,
                timerProgressBar: true
            });
        @endif

        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                confirmButtonColor: '#667eea',
                timer: 3000,
                timerProgressBar: true
            });
        @endif

        @if(session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Terjadi Kesalahan!',
                text: '{{ session('error') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        @if(session('warning'))
            Swal.fire({
                icon: 'warning',
                title: 'Peringatan!',
                text: '{{ session('warning') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        @if(session('info'))
            Swal.fire({
                icon: 'info',
                title: 'Informasi',
                text: '{{ session('info') }}',
                confirmButtonColor: '#667eea'
            });
        @endif
    </script>
</body>
</html>
