@extends('layouts.dashboard')

@section('title', 'Setup Two-Factor Authentication')

@section('page-title', '🔒 Setup Two-Factor Authentication')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Setup Two-Factor Authentication
                    </h5>
                </div>
                <div class="card-body p-4">
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Langkah Setup 2FA:</strong>
                        <ol class="mb-0 mt-2">
                            <li>Install aplikasi authenticator (Google Authenticator, Authy, Microsoft Authenticator)</li>
                            <li>Scan QR code di bawah atau masukkan secret key manual</li>
                            <li>Masukkan kode 6 digit dari aplikasi untuk verifikasi</li>
                        </ol>
                    </div>

                    <div class="row">
                        <!-- QR Code Section -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-qrcode me-2"></i>
                                Scan QR Code
                            </h6>
                            
                            <div class="text-center p-3 border rounded">
                                @if($qrCodeImage)
                                    <!-- Server-generated QR Code -->
                                    <img src="{{ $qrCodeImage }}" alt="QR Code" class="border rounded" style="width: 200px; height: 200px;">
                                    <small class="text-muted d-block mt-2">
                                        Scan dengan aplikasi authenticator Anda
                                    </small>
                                @elseif($qrCodeUrl)
                                    <!-- JavaScript-generated QR Code fallback -->
                                    <div id="qrcode-container">
                                        <!-- QR Code will be generated here -->
                                    </div>
                                    <small class="text-muted d-block mt-2">
                                        Scan dengan aplikasi authenticator Anda
                                    </small>
                                    <!-- Debug info -->
                                    <div class="mt-2">
                                        <small class="text-info">Debug: Using JS generation ({{ strlen($qrCodeUrl) }} chars)</small>
                                    </div>
                                @else
                                    <div class="text-danger">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Error generating QR code - QR URL is null or empty
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Manual Entry Section -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-key me-2"></i>
                                Manual Entry
                            </h6>
                            
                            <div class="mb-3">
                                <label class="form-label">Secret Key:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control font-monospace" 
                                           value="{{ $secret }}" readonly id="secret-key">
                                    <button class="btn btn-outline-secondary" type="button" 
                                            onclick="copyToClipboard('secret-key')" title="Copy">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                <small class="text-muted">
                                    Masukkan key ini secara manual jika tidak bisa scan QR code
                                </small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Account Name:</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->email }}" readonly>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Issuer:</label>
                                <input type="text" class="form-control" value="MyMoney" readonly>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Verification Form -->
                    <form method="POST" action="{{ route('settings.2fa.enable') }}">
                        @csrf
                        
                        <h6 class="fw-bold mb-3">
                            <i class="fas fa-check-circle me-2"></i>
                            Verifikasi Setup
                        </h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="verification_code" class="form-label">
                                        Kode Verifikasi (6 digit)
                                    </label>
                                    <input type="text" 
                                           class="form-control @error('verification_code') is-invalid @enderror" 
                                           id="verification_code" 
                                           name="verification_code" 
                                           placeholder="123456"
                                           maxlength="6"
                                           pattern="[0-9]{6}"
                                           autocomplete="off"
                                           required>
                                    @error('verification_code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">
                                        Masukkan kode 6 digit dari aplikasi authenticator Anda
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2 mt-4">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>
                                Aktifkan 2FA
                            </button>
                            <a href="{{ route('settings.security') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                Batal
                            </a>
                        </div>
                    </form>

                </div>
            </div>

            <!-- Help Card -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        Aplikasi Authenticator yang Direkomendasikan
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <i class="fab fa-google fa-2x text-primary mb-2"></i>
                                <h6>Google Authenticator</h6>
                                <small class="text-muted">iOS & Android</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <i class="fab fa-microsoft fa-2x text-info mb-2"></i>
                                <h6>Microsoft Authenticator</h6>
                                <small class="text-muted">iOS & Android</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                <h6>Authy</h6>
                                <small class="text-muted">iOS, Android, Desktop</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <i class="fas fa-lock fa-2x text-warning mb-2"></i>
                                <h6>FreeOTP</h6>
                                <small class="text-muted">Open Source</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode-generator/1.4.4/qrcode.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js" 
        onerror="console.error('Failed to load QRCode library from jsdelivr')"></script>
<script>
// Wait for library to load
function waitForQRCode(callback) {
    if (typeof QRCode !== 'undefined') {
        callback();
    } else {
        setTimeout(() => waitForQRCode(callback), 100);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Generate QR Code only if server-side generation failed
    @if($qrCodeUrl && !$qrCodeImage)
    const qrCodeUrl = @json($qrCodeUrl);
    const qrContainer = document.getElementById('qrcode-container');
    
    console.log('QR Code URL:', qrCodeUrl);
    console.log('QR Container:', qrContainer);
    
    // Wait for QRCode library to load
    waitForQRCode(function() {
        console.log('QRCode library loaded successfully');
        
        QRCode.toCanvas(qrCodeUrl, {
            width: 200,
            height: 200,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        }, function(error, canvas) {
            if (error) {
                console.error('QR Code generation error:', error);
                qrContainer.innerHTML = '<div class="text-danger">Error generating QR code: ' + error.message + '</div>';
            } else {
                console.log('QR Code generated successfully');
                canvas.className = 'border rounded';
                qrContainer.appendChild(canvas);
            }
        });
    });
    @else
        console.log('Server-generated QR code is being used or no QR Code URL provided');
    @endif
    
    // Auto-format verification code input
    const codeInput = document.getElementById('verification_code');
    codeInput.addEventListener('input', function(e) {
        // Only allow numbers
        this.value = this.value.replace(/[^0-9]/g, '');
    });
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show feedback
    const button = element.nextElementSibling;
    const originalIcon = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check text-success"></i>';
    setTimeout(() => {
        button.innerHTML = originalIcon;
    }, 2000);
}
</script>
@endpush
@endsection
