@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center min-vh-100 align-items-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Two-Factor Authentication
                    </h4>
                </div>
                <div class="card-body p-4">
                    
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                        </div>
                        <h5>Verifikas<PERSON> Keamanan</h5>
                        <p class="text-muted">
                            Masukkan kode 6 digit dari aplikasi authenticator Anda
                            atau gunakan backup code 8 karakter.
                        </p>
                    </div>

                    @if(session('warning'))
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ session('warning') }}
                        </div>
                    @endif

                    <form method="POST" action="{{ route('2fa.verify.post') }}">
                        @csrf
                        
                        <div class="form-group mb-4">
                            <label for="code" class="form-label fw-bold">
                                Kode Verifikasi
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg text-center @error('code') is-invalid @enderror" 
                                   id="code" 
                                   name="code" 
                                   placeholder="123456"
                                   autocomplete="off"
                                   autofocus
                                   required>
                            @error('code')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted mt-2">
                                <i class="fas fa-info-circle me-1"></i>
                                Kode berubah setiap 30 detik
                            </small>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-check me-2"></i>
                                Verifikasi
                            </button>
                        </div>
                    </form>

                    <hr>

                    <!-- Help Section -->
                    <div class="text-center">
                        <h6 class="fw-bold mb-3">Butuh Bantuan?</h6>
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="p-2">
                                    <i class="fas fa-clock text-info fa-2x mb-2"></i>
                                    <br>
                                    <small class="text-muted">
                                        <strong>Kode TOTP</strong><br>
                                        6 digit dari app
                                    </small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-2">
                                    <i class="fas fa-key text-warning fa-2x mb-2"></i>
                                    <br>
                                    <small class="text-muted">
                                        <strong>Backup Code</strong><br>
                                        8 karakter
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                            @csrf
                            <button type="submit" class="btn btn-link text-muted">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                Logout
                            </button>
                        </form>
                    </div>

                </div>
            </div>

            <!-- Additional Info Card -->
            <div class="card mt-3">
                <div class="card-body text-center">
                    <small class="text-muted">
                        <i class="fas fa-lock me-1"></i>
                        Koneksi Anda aman dan terenkripsi
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const codeInput = document.getElementById('code');
    
    // Auto-format input
    codeInput.addEventListener('input', function(e) {
        let value = this.value.replace(/[^0-9A-Za-z]/g, '');
        
        // If 6 digits, likely TOTP (numbers only)
        if (value.length <= 6) {
            value = value.replace(/[^0-9]/g, '');
        }
        
        this.value = value;
        
        // Auto-submit if 6 digits
        if (value.length === 6 && /^\d{6}$/.test(value)) {
            setTimeout(() => {
                this.form.submit();
            }, 500);
        }
    });
    
    // Focus on input
    codeInput.focus();
});
</script>
@endpush
@endsection
