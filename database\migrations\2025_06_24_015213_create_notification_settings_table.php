<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            
            // Email Notifications
            $table->boolean('email_enabled')->default(true);
            $table->boolean('email_login_alerts')->default(true);
            $table->boolean('email_security_alerts')->default(true);
            $table->boolean('email_transaction_alerts')->default(true);
            $table->boolean('email_budget_alerts')->default(true);
            $table->boolean('email_weekly_summary')->default(true);
            $table->boolean('email_monthly_report')->default(true);
            $table->boolean('email_marketing')->default(false);
            
            // Browser/Web Notifications
            $table->boolean('web_enabled')->default(true);
            $table->boolean('web_transaction_alerts')->default(true);
            $table->boolean('web_budget_warnings')->default(true);
            $table->boolean('web_goal_progress')->default(true);
            $table->boolean('web_reminders')->default(true);
            
            // Mobile Push Notifications (for future mobile app)
            $table->boolean('push_enabled')->default(true);
            $table->boolean('push_transaction_alerts')->default(true);
            $table->boolean('push_security_alerts')->default(true);
            $table->boolean('push_budget_warnings')->default(true);
            
            // SMS Notifications (for future SMS integration)
            $table->boolean('sms_enabled')->default(false);
            $table->boolean('sms_security_alerts')->default(false);
            $table->boolean('sms_critical_alerts')->default(false);
            $table->string('sms_phone')->nullable();
            
            // Notification Timing
            $table->time('quiet_hours_start')->default('22:00');
            $table->time('quiet_hours_end')->default('08:00');
            $table->boolean('weekend_notifications')->default(true);
            
            // Frequency Settings
            $table->enum('summary_frequency', ['daily', 'weekly', 'monthly'])->default('weekly');
            $table->enum('budget_alert_frequency', ['instant', 'daily', 'weekly'])->default('instant');
            
            // Advanced Settings
            $table->json('custom_keywords')->nullable(); // Keywords to watch for in transactions
            $table->decimal('large_transaction_threshold', 15, 2)->default(1000000); // IDR
            $table->json('notification_channels')->nullable(); // Slack, Discord, etc.
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_settings');
    }
};
