<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SecurityLog;
use Illuminate\Support\Facades\Storage;

class SecurityLogMonitor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'security:monitor {--live : Show live logs} {--last=10 : Number of last logs to show}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor security logs in real-time';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('live')) {
            $this->info('🔍 Security Log Monitor - Live Mode (Press Ctrl+C to stop)');
            $this->info('='.str_repeat('=', 60));
            
            $lastId = SecurityLog::max('id') ?? 0;
            
            while (true) {
                $newLogs = SecurityLog::where('id', '>', $lastId)
                    ->orderBy('created_at', 'desc')
                    ->get();
                    
                foreach ($newLogs as $log) {
                    $this->displayLog($log);
                    $lastId = $log->id;
                }
                
                sleep(2); // Check every 2 seconds
            }
        } else {
            $count = $this->option('last');
            $this->info("🔍 Security Logs - Last {$count} entries");
            $this->info('='.str_repeat('=', 60));
            
            $logs = SecurityLog::orderBy('created_at', 'desc')
                ->take($count)
                ->get();
                
            foreach ($logs as $log) {
                $this->displayLog($log);
            }
        }
        
        // Also show Laravel logs for additional context
        $this->newLine();
        $this->info('📋 Laravel Error Logs (Last 5):');
        $this->info('-'.str_repeat('-', 40));
        
        $logContent = Storage::disk('local')->exists('../storage/logs/laravel.log') 
            ? Storage::disk('local')->get('../storage/logs/laravel.log') 
            : '';
            
        if ($logContent) {
            $lines = array_slice(explode("\n", $logContent), -10);
            foreach ($lines as $line) {
                if (stripos($line, 'error') !== false || stripos($line, 'exception') !== false) {
                    $this->error(substr($line, 0, 120));
                }
            }
        } else {
            $this->comment('No Laravel log file found.');
        }
    }
    
    private function displayLog($log)
    {
        $riskColors = [
            'low' => 'info',
            'medium' => 'comment', 
            'high' => 'warn',
            'critical' => 'error'
        ];
        
        $color = $riskColors[$log->risk_level] ?? 'line';
        
        $this->newLine();
        $this->{$color}(sprintf(
            '[%s] %s - %s',
            $log->created_at->format('H:i:s'),
            strtoupper($log->risk_level),
            $log->event_type
        ));
        
        $this->line("Message: {$log->message}");
        
        if ($log->details) {
            $details = is_string($log->details) ? json_decode($log->details, true) : $log->details;
            if (is_array($details)) {
                foreach ($details as $key => $value) {
                    if (is_string($value) && strlen($value) < 100) {
                        $this->line("  {$key}: {$value}");
                    }
                }
            }
        }
    }
}
