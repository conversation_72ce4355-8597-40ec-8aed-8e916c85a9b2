<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\ReportController;
use Illuminate\Http\Request;

class TestMonthlyApi extends Command
{
    protected $signature = 'test:monthly-api {month?} {year?}';
    protected $description = 'Test monthly API response';

    public function handle()
    {
        $month = $this->argument('month');
        $year = $this->argument('year') ?? '2025';
        
        // If month is empty string or null, don't include it
        $requestData = ['year' => $year];
        if (!empty($month)) {
            $requestData['month'] = $month;
        }
        
        // Simulate authentication
        auth()->loginUsingId(2);
        
        $request = new Request($requestData);
        
        $controller = new ReportController();
        $response = $controller->apiMonthly($request);
        
        $monthText = empty($month) ? 'All Months' : $month;
        $this->info("Monthly API Response for {$monthText}/{$year}:");
        $this->line($response->getContent());
    }
}
