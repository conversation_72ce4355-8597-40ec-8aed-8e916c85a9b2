@extends('layouts.dashboard')

@section('title', 'Detail Notifikasi')

@section('page-title', 'Detail Notifikasi')

@push('styles')
<style>
.notification-detail-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.notification-type-badge {
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.notification-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #4a5568;
}

.notification-metadata {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.action-buttons .btn {
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.timeline-item {
    border-left: 3px solid #e2e8f0;
    padding-left: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0;
    width: 9px;
    height: 9px;
    background: #007bff;
    border-radius: 50%;
}

.timeline-item.important::before {
    background: #dc3545;
}

.back-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    border-radius: 8px;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.back-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Back Button -->
    <div class="mb-4">
        <a href="{{ route('settings.notifications') }}" class="btn back-button">
            <i class="fas fa-arrow-left me-2"></i>Kembali ke Notifikasi
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Notification Detail Card -->
            <div class="card notification-detail-card">
                <div class="card-header border-0 bg-white pb-0">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h4 class="card-title mb-2">{{ $notification->title }}</h4>
                            <div class="mb-3">
                                <span class="badge notification-type-badge bg-{{ $typeColor }} me-2">
                                    {{ strtoupper($notification->type) }}
                                </span>
                                @if($notification->is_important)
                                    <span class="badge notification-type-badge bg-danger">
                                        <i class="fas fa-star me-1"></i>PENTING
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ $notification->created_at->format('d M Y, H:i') }}
                            </small>
                            @if(!$notification->read_at)
                                <div class="mt-1">
                                    <span class="badge bg-primary">Belum Dibaca</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Notification Content -->
                    <div class="notification-content mb-4">
                        {{ $notification->message }}
                    </div>

                    <!-- Additional Data -->
                    @if($notification->data && is_array($notification->data) && count($notification->data) > 0)
                        <div class="notification-metadata mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-info-circle me-2"></i>Detail Tambahan
                            </h6>
                            <div class="row">
                                @foreach($notification->data as $key => $value)
                                    <div class="col-md-6 mb-2">
                                        <strong>{{ ucfirst(str_replace('_', ' ', $key)) }}:</strong>
                                        @if(is_array($value))
                                            <pre class="bg-light p-2 rounded mt-1">{{ json_encode($value, JSON_PRETTY_PRINT) }}</pre>
                                        @else
                                            <span class="ms-2">{{ $value }}</span>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Timeline if related notifications exist -->
                    @if($relatedNotifications && $relatedNotifications->count() > 0)
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-history me-2"></i>Notifikasi Terkait
                            </h6>
                            <div class="timeline">
                                @foreach($relatedNotifications as $related)
                                    <div class="timeline-item {{ $related->is_important ? 'important' : '' }}">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <strong>{{ $related->title }}</strong>
                                                <p class="mb-1 text-muted">{{ $related->message }}</p>
                                                <small class="text-muted">
                                                    {{ $related->created_at->format('d M Y, H:i') }}
                                                </small>
                                            </div>
                                            <span class="badge bg-{{ $related->type === 'success' ? 'success' : ($related->type === 'error' ? 'danger' : ($related->type === 'warning' ? 'warning' : 'info')) }}">
                                                {{ strtoupper($related->type) }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Action Buttons -->
                    <div class="action-buttons d-flex flex-wrap gap-2">
                        @if($notification->action_url)
                            <a href="{{ $notification->action_url }}" class="btn btn-primary">
                                <i class="fas fa-external-link-alt me-2"></i>Lihat Detail Lengkap
                            </a>
                        @endif

                        @if(!$notification->read_at)
                            <form method="POST" action="{{ route('api.notifications.read', $notification->id) }}" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-outline-success">
                                    <i class="fas fa-check me-2"></i>Tandai Sudah Dibaca
                                </button>
                            </form>
                        @endif

                        <form method="POST" action="{{ route('api.notifications.delete', $notification->id) }}" class="d-inline" onsubmit="return confirmDeleteDetail(event)">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger">
                                <i class="fas fa-trash me-2"></i>Hapus Notifikasi
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Additional Actions Card -->
            <div class="card notification-detail-card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-cog me-2"></i>Pengaturan Notifikasi
                    </h6>
                    <p class="text-muted mb-3">Kelola pengaturan notifikasi untuk jenis seperti ini</p>
                    
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{{ route('settings.notifications') }}" class="btn btn-outline-primary">
                            <i class="fas fa-bell me-2"></i>Pengaturan Notifikasi
                        </a>
                        
                        <button type="button" class="btn btn-outline-secondary" onclick="shareNotification()">
                            <i class="fas fa-share me-2"></i>Bagikan
                        </button>
                        
                        <button type="button" class="btn btn-outline-info" onclick="printNotification()">
                            <i class="fas fa-print me-2"></i>Cetak
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Include SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
function confirmDeleteDetail(event) {
    event.preventDefault();
    
    Swal.fire({
        title: 'Hapus Notifikasi?',
        text: 'Apakah Anda yakin ingin menghapus notifikasi ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            event.target.submit();
        }
    });
    
    return false;
}

function shareNotification() {
    if (navigator.share) {
        navigator.share({
            title: '{{ $notification->title }}',
            text: '{{ $notification->message }}',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            Swal.fire({
                title: 'Berhasil!',
                text: 'Link notifikasi berhasil disalin ke clipboard!',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        });
    }
}

function printNotification() {
    window.print();
}

// Auto-mark as read when page loads
@if(!$notification->read_at)
document.addEventListener('DOMContentLoaded', function() {
    // Mark as read after 3 seconds of viewing
    setTimeout(() => {
        fetch('{{ route("api.notifications.read", $notification->id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        }).then(response => {
            if (response.ok) {
                // Update UI to show as read
                const badge = document.querySelector('.badge.bg-primary');
                if (badge && badge.textContent === 'Belum Dibaca') {
                    badge.remove();
                }
                
                const markReadButton = document.querySelector('button:contains("Tandai Sudah Dibaca")');
                if (markReadButton) {
                    markReadButton.style.display = 'none';
                }
            }
        });
    }, 3000);
});
@endif
</script>
@endpush
