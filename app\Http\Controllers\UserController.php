<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    public function updateProfile(Request $request)
    {
        // Log the request untuk debugging
        Log::info('UserController updateProfile called', [
            'url' => $request->url(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'field_count' => count($request->all()),
            'has_name' => $request->has('name'),
            'has_email' => $request->has('email')
        ]);
        
        $user = Auth::user();
        
        if (!$user) {
            Log::warning('No authenticated user found');
            return response()->json([
                'success' => false, 
                'message' => 'User tidak ditemukan'
            ], 401);
        }
        
        try {
            // Validasi input
            $validatedData = $request->validate([
                'name' => 'required|string|max:100',
                'username' => 'nullable|string|max:50|unique:users,username,' . $user->id,
                'email' => 'required|email|max:100|unique:users,email,' . $user->id,
                'phone' => 'nullable|string|max:20',
                'gender' => 'nullable|in:male,female',
                'birth_date' => 'nullable|date|before:today',
                'city' => 'nullable|string|max:50',
                'postal_code' => 'nullable|string|max:10',
                'address' => 'nullable|string|max:500',
                'bio' => 'nullable|string|max:1000',
                'language' => 'nullable|string|max:10',
                'timezone' => 'nullable|string|max:50'
            ]);
            
            // Sanitasi data
            foreach ($validatedData as $key => $value) {
                if (is_string($value)) {
                    $validatedData[$key] = strip_tags(trim($value));
                }
            }
            
            // Update user
            $user->update($validatedData);
            
            Log::info('Profile updated successfully', [
                'user_id' => $user->id,
                'updated_fields' => array_keys($validatedData)
            ]);
            
            // Response untuk AJAX
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Profil berhasil diperbarui!'
                ]);
            }
            
            // Response untuk form biasa
            return redirect()->back()->with('success', 'Profil berhasil diperbarui!');
            
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Validation failed', ['errors' => $e->errors()]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid',
                    'errors' => $e->errors()
                ], 422);
            }
            
            return redirect()->back()->withErrors($e->errors())->withInput();
            
        } catch (\Exception $e) {
            Log::error('Profile update failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat menyimpan profil'
                ], 500);
            }
            
            return redirect()->back()->with('error', 'Terjadi kesalahan saat menyimpan profil')->withInput();
        }
    }
}
