<?php

namespace App\Services;

use Pusher\Pusher;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Transaction;
use App\Models\Account;
use App\Models\Category;
use Carbon\Carbon;

class DashboardRealtimeService
{
    protected $pusher;

    public function __construct()
    {
        if (config('broadcasting.connections.pusher.key')) {
            $this->pusher = new Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id'),
                [
                    'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                    'useTLS' => true
                ]
            );
        }
    }

    /**
     * Broadcast new transaction event
     */
    public function broadcastNewTransaction($transaction)
    {
        if (!$this->pusher) {
            return false;
        }

        try {
            $this->pusher->trigger('financial-updates', 'new-transaction', [
                'id' => $transaction['id'] ?? uniqid(),
                'title' => $transaction['title'],
                'amount' => $transaction['amount'],
                'type' => $transaction['type'], // 'income' or 'expense'
                'category' => $transaction['category'] ?? 'Lainnya',
                'created_at' => now()->toISOString(),
                'user_id' => auth()->id()
            ]);

            Log::info('Transaction broadcasted successfully', $transaction);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to broadcast transaction: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Broadcast balance update event
     */
    public function broadcastBalanceUpdate($newBalance, $userId = null)
    {
        if (!$this->pusher) {
            return false;
        }

        try {
            $this->pusher->trigger('financial-updates', 'balance-updated', [
                'newBalance' => $newBalance,
                'updated_at' => now()->toISOString(),
                'user_id' => $userId ?? auth()->id()
            ]);

            Log::info('Balance update broadcasted successfully', ['newBalance' => $newBalance]);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to broadcast balance update: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Broadcast dashboard statistics update
     */
    public function broadcastStatsUpdate($stats)
    {
        if (!$this->pusher) {
            return false;
        }

        try {
            $this->pusher->trigger('financial-updates', 'stats-updated', [
                'totalBalance' => $stats['totalBalance'] ?? 0,
                'monthlyIncome' => $stats['monthlyIncome'] ?? 0,
                'monthlyExpense' => $stats['monthlyExpense'] ?? 0,
                'todayTransactions' => is_array($stats['todayTransactions']) ? count($stats['todayTransactions']) : ($stats['todayTransactions'] ?? 0),
                'activeAccounts' => $stats['activeAccounts'] ?? 0,
                'balanceGrowth' => $stats['balanceGrowth'] ?? 0,
                'recentActivities' => $stats['recentActivities'] ?? [],
                'updated_at' => now()->toISOString(),
                'user_id' => auth()->id()
            ]);

            Log::info('Dashboard stats broadcasted successfully with recent activities');
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to broadcast dashboard stats: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Broadcast account update event
     */
    public function broadcastAccountUpdate($userId = null)
    {
        if (!$this->pusher) {
            return false;
        }

        try {
            $this->pusher->trigger('financial-updates', 'account-updated', [
                'updated_at' => now()->toISOString(),
                'user_id' => $userId ?? auth()->id()
            ]);

            Log::info('Account update broadcasted successfully');
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to broadcast account update: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get current dashboard statistics
     */
    public function getDashboardStats($userId = null)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return $this->getEmptyStats();
        }
        
        // Get current month start and end
        $currentMonth = Carbon::now()->startOfMonth();
        $currentMonthEnd = Carbon::now()->endOfMonth();
        $today = Carbon::today();
        
        // Calculate total balance from active accounts that are included in total
        $totalBalance = Account::where('user_id', $userId)
            ->where('is_active', true)
            ->where('include_in_total', true)
            ->sum('current_balance');
        
        // Get monthly income and expense
        $monthlyIncome = Transaction::where('user_id', $userId)
            ->where('type', 'income')
            ->whereBetween('transaction_date', [$currentMonth, $currentMonthEnd])
            ->sum('amount');
            
        $monthlyExpense = Transaction::where('user_id', $userId)
            ->where('type', 'expense')
            ->whereBetween('transaction_date', [$currentMonth, $currentMonthEnd])
            ->sum('amount');
        
        // Get today's transactions count
        $todayTransactionsCount = Transaction::where('user_id', $userId)
            ->whereDate('transaction_date', $today)
            ->count();
        
        // Get active accounts count
        $activeAccounts = Account::where('user_id', $userId)
            ->where('is_active', true)
            ->count();
        
        // Get recent activities dengan prioritas transaksi terbaru
        $totalRecentTransactions = Transaction::where('user_id', $userId)
            ->with(['category', 'account'])
            ->orderBy('created_at', 'desc')
            ->limit(50) // Ambil lebih banyak untuk pagination/rotation
            ->get();
            
        // Prioritaskan transaksi terbaru (hari ini) kemudian lakukan rotasi untuk yang lama
        $todayTransactionsForActivities = $totalRecentTransactions->filter(function($transaction) {
            return $transaction->created_at->isToday();
        });
        
        $olderTransactions = $totalRecentTransactions->filter(function($transaction) {
            return !$transaction->created_at->isToday();
        });
        
        $maxDisplay = 15; // Ambil lebih banyak untuk pagination (akan dibagi 3 per halaman di frontend)
        
        // Jika ada transaksi hari ini, prioritaskan mereka
        if ($todayTransactionsForActivities->count() > 0) {
            $recentActivities = $todayTransactionsForActivities->take($maxDisplay);
            
            // Jika masih ada ruang, tambahkan transaksi lama
            $remainingSlots = $maxDisplay - $todayTransactionsForActivities->count();
            if ($remainingSlots > 0 && $olderTransactions->count() > 0) {
                $recentActivities = $recentActivities->merge($olderTransactions->take($remainingSlots));
            }
        } else {
            // Jika tidak ada transaksi hari ini, ambil semua transaksi terbaru
            $recentActivities = $totalRecentTransactions->take($maxDisplay);
        }
        
        $recentActivities = $recentActivities->map(function($transaction) {
            return [
                'id' => $transaction->id,
                'icon' => $this->getTransactionIcon($transaction),
                'title' => $transaction->title,
                'time' => $this->getTimeAgo($transaction->created_at),
                'amount' => $transaction->type === 'expense' ? -$transaction->amount : $transaction->amount,
                'type' => $transaction->type,
                'category' => $transaction->category ? $transaction->category->name : 'Lainnya',
                'account' => optional($transaction->account)->name ?? 'Tidak diketahui'
            ];
        });

        
        return [
            'totalBalance' => $totalBalance,
            'monthlyIncome' => $monthlyIncome,
            'monthlyExpense' => $monthlyExpense,
            'netIncome' => $monthlyIncome - $monthlyExpense,
            'todayTransactions' => $todayTransactionsCount,
            'activeAccounts' => $activeAccounts,
            'recentActivities' => $recentActivities->toArray(),
            'balanceGrowth' => $this->calculateBalanceGrowth($userId),
            'last_updated' => now()->toISOString()
        ];
    }
    
    /**
     * Get empty stats for unauthenticated users
     */
    private function getEmptyStats()
    {
        return [
            'totalBalance' => 0,
            'monthlyIncome' => 0,
            'monthlyExpense' => 0,
            'netIncome' => 0,
            'todayTransactions' => 0,
            'activeAccounts' => 0,
            'recentActivities' => [],
            'balanceGrowth' => 0,
            'last_updated' => now()->toISOString()
        ];
    }
    
    /**
     * Calculate balance growth percentage
     */
    private function calculateBalanceGrowth($userId)
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $previousMonth = Carbon::now()->subMonth()->startOfMonth();
        $previousMonthEnd = Carbon::now()->subMonth()->endOfMonth();
        
        $currentIncome = Transaction::where('user_id', $userId)
            ->where('type', 'income')
            ->whereBetween('transaction_date', [$currentMonth, now()])
            ->sum('amount');
            
        $currentExpense = Transaction::where('user_id', $userId)
            ->where('type', 'expense')
            ->whereBetween('transaction_date', [$currentMonth, now()])
            ->sum('amount');
            
        $previousIncome = Transaction::where('user_id', $userId)
            ->where('type', 'income')
            ->whereBetween('transaction_date', [$previousMonth, $previousMonthEnd])
            ->sum('amount');
            
        $previousExpense = Transaction::where('user_id', $userId)
            ->where('type', 'expense')
            ->whereBetween('transaction_date', [$previousMonth, $previousMonthEnd])
            ->sum('amount');
        
        $currentNet = $currentIncome - $currentExpense;
        $previousNet = $previousIncome - $previousExpense;
        
        if ($previousNet == 0) {
            return $currentNet > 0 ? 100 : 0;
        }
        
        return round((($currentNet - $previousNet) / abs($previousNet)) * 100, 1);
    }
    
    /**
     * Get transaction icon based on category
     */
    private function getTransactionIcon($transaction)
    {
        $categoryIcons = [
            'makanan' => '🍕',
            'transportasi' => '🚗',
            'belanja' => '🛒',
            'hiburan' => '🎮',
            'kesehatan' => '🏥',
            'pendidikan' => '📚',
            'gaji' => '💼',
            'bonus' => '🎁',
            'investasi' => '�',
            'transfer' => '💳',
        ];
        
        $categoryName = strtolower($transaction->category ? $transaction->category->name : '');
        
        foreach ($categoryIcons as $key => $icon) {
            if (strpos($categoryName, $key) !== false) {
                return $icon;
            }
        }
        
        // Default icons based on transaction type
        switch ($transaction->type) {
            case 'income':
                return '💰';
            case 'expense':
                return '💸';
            case 'transfer':
                return '🔄';
            default:
                return '💳';
        }
    }
    
    /**
     * Get human readable time ago
     */
    private function getTimeAgo($datetime)
    {
        $carbon = Carbon::parse($datetime);
        
        if ($carbon->isToday()) {
            $diffInMinutes = intval($carbon->diffInMinutes()); // Bulatkan ke integer
            if ($diffInMinutes < 1) {
                return 'Baru saja';
            } elseif ($diffInMinutes < 60) {
                return $diffInMinutes . ' menit yang lalu';
            } else {
                $diffInHours = intval($carbon->diffInHours()); // Bulatkan ke integer
                return $diffInHours . ' jam yang lalu';
            }
        } elseif ($carbon->isYesterday()) {
            return 'Kemarin';
        } else {
            $diffInDays = intval($carbon->diffInDays()); // Bulatkan ke integer
            if ($diffInDays == 1) {
                return '1 hari yang lalu';
            } elseif ($diffInDays < 7) {
                return $diffInDays . ' hari yang lalu';
            } elseif ($diffInDays < 30) {
                $weeks = intval($diffInDays / 7);
                return $weeks . ' minggu yang lalu';
            } else {
                return $carbon->format('d M Y');
            }
        }
    }

    /**
     * Simulate new transaction - creates real transaction for testing
     */
    public function simulateNewTransaction()
    {
        try {
            $user = auth()->user();
            if (!$user) {
                return false;
            }

            // Get first account for testing
            $account = \App\Models\Account::where('user_id', $user->id)->first();
            if (!$account) {
                return false;
            }

            // Get first category for testing  
            $category = \App\Models\Category::where('user_id', $user->id)->where('is_active', 1)->first();
            if (!$category) {
                return false;
            }

            // Create real transaction
            $transaction = \App\Models\Transaction::create([
                'user_id' => $user->id,
                'account_id' => $account->id,
                'category_id' => $category->id,
                'type' => 'expense',
                'amount' => rand(10000, 100000),
                'title' => 'TEST SIMULATION - ' . now()->format('H:i:s'),
                'description' => 'Test transaction created via simulation',
                'transaction_date' => now()->toDateString(),
                'currency' => 'IDR',
                'status' => 'completed',
                'processed_at' => now(),
            ]);

            // Update account balance
            $account->balance -= $transaction->amount;
            $account->save();

            // Trigger Pusher event
            try {
                $pusher = new \Pusher\Pusher(
                    config('broadcasting.connections.pusher.key'),
                    config('broadcasting.connections.pusher.secret'),
                    config('broadcasting.connections.pusher.app_id'),
                    [
                        'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                        'useTLS' => true
                    ]
                );

                $eventData = [
                    'user_id' => $user->id,
                    'transaction' => [
                        'id' => $transaction->id,
                        'type' => $transaction->type,
                        'amount' => $transaction->amount,
                        'title' => $transaction->title,
                        'category' => $category->name,
                        'account' => $account->name,
                        'time' => 'Baru saja'
                    ]
                ];

                $pusher->trigger('financial-updates', 'new-transaction', $eventData);
                \Log::info('Pusher event triggered for simulated transaction', $eventData);

            } catch (\Exception $pusherError) {
                \Log::error('Failed to trigger Pusher event for simulation: ' . $pusherError->getMessage());
            }

            return true;

        } catch (\Exception $e) {
            \Log::error('Failed to simulate transaction: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get monthly history for income and expense
     */
    public function getMonthlyHistory($userId = null, $months = 6)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return [];
        }
        
        $history = [];
        
        for ($i = 0; $i < $months; $i++) {
            $month = Carbon::now()->subMonths($i);
            $monthStart = $month->copy()->startOfMonth();
            $monthEnd = $month->copy()->endOfMonth();
            
            $income = Transaction::where('user_id', $userId)
                ->where('type', 'income')
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->sum('amount');
                
            $expense = Transaction::where('user_id', $userId)
                ->where('type', 'expense')
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->sum('amount');
            
            $history[] = [
                'month' => $month->format('M Y'),
                'month_full' => $month->format('F Y'),
                'income' => $income,
                'expense' => $expense,
                'net' => $income - $expense,
                'is_current' => $i === 0
            ];
        }
        
        return array_reverse($history); // Oldest first
    }

    /**
     * Get enhanced dashboard statistics with monthly history
     */
    public function getEnhancedDashboardStats($userId = null)
    {
        $basicStats = $this->getDashboardStats($userId);
        $monthlyHistory = $this->getMonthlyHistory($userId, 6);
        $monthlyTransactionStats = $this->getMonthlyTransactionStats($userId);
        
        return array_merge($basicStats, [
            'monthlyHistory' => $monthlyHistory,
            'monthlyTransactionStats' => $monthlyTransactionStats
        ]);
    }

    /**
     * Get transaction statistics by month
     */
    public function getMonthlyTransactionStats($userId = null, $months = 3)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return [];
        }
        
        $stats = [];
        
        for ($i = 0; $i < $months; $i++) {
            $month = Carbon::now()->subMonths($i);
            $monthStart = $month->copy()->startOfMonth();
            $monthEnd = $month->copy()->endOfMonth();
            
            $transactionCount = Transaction::where('user_id', $userId)
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->count();
                
            $incomeTransactions = Transaction::where('user_id', $userId)
                ->where('type', 'income')
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->count();
                
            $expenseTransactions = Transaction::where('user_id', $userId)
                ->where('type', 'expense')
                ->whereBetween('transaction_date', [$monthStart, $monthEnd])
                ->count();
            
            $stats[] = [
                'month' => $month->format('M Y'),
                'month_full' => $month->format('F Y'),
                'total_transactions' => $transactionCount,
                'income_transactions' => $incomeTransactions,
                'expense_transactions' => $expenseTransactions,
                'is_current' => $i === 0
            ];
        }
        
        return array_reverse($stats); // Oldest first
    }
}
