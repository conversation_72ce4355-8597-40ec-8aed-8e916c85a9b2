<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="csrf-token" content="test-token">
    <title>Debug Theme</title>
</head>
<body>
    <h1>Debug Theme Test</h1>
    <button onclick="debugSetTheme('light')">Test Light Theme</button>
    <button onclick="debugSetTheme('solarized-dark')">Test Dark Theme</button>
    
    <div id="output"></div>

    <script>
        function debugSetTheme(theme) {
            console.log('=== DEBUG START ===');
            console.log('Theme to set:', theme);
            
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            console.log('CSRF meta tag found:', csrfToken !== null);
            console.log('CSRF token value:', csrfToken ? csrfToken.getAttribute('content') : 'NULL');
            
            const url = 'http://127.0.0.1:8000/settings/theme';
            console.log('URL:', url);
            
            const payload = {
                theme: theme
            };
            console.log('Payload:', payload);
            
            const headers = {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : 'test-token'
            };
            console.log('Headers:', headers);
            
            fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(payload)
            })
            .then(response => {
                console.log('=== RESPONSE ===');
                console.log('Status:', response.status);
                console.log('Status text:', response.statusText);
                console.log('Headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Response text:', text);
                document.getElementById('output').innerHTML = '<pre>' + text + '</pre>';
                
                try {
                    const data = JSON.parse(text);
                    console.log('Parsed JSON:', data);
                } catch (e) {
                    console.log('Not JSON response');
                }
            })
            .catch(error => {
                console.error('=== ERROR ===');
                console.error('Error:', error);
                document.getElementById('output').innerHTML = '<pre style="color: red;">Error: ' + error + '</pre>';
            });
        }
    </script>
</body>
</html>
