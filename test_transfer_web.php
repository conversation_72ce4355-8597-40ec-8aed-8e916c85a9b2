<?php

/**
 * Script untuk testing transfer melalui web interface
 * Jalankan dengan: php test_transfer_web.php
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Account;
use App\Models\Transaction;
use App\Http\Controllers\TransactionController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

echo "=== TESTING TRANSFER MELALUI WEB INTERFACE ===\n\n";

// Login sebagai user pertama
$user = User::first();
if (!$user) {
    echo "❌ Tidak ada user untuk testing.\n";
    exit;
}

Auth::login($user);
echo "👤 Login sebagai: {$user->name}\n\n";

// Ambil 2 akun untuk testing
$accounts = Account::where('user_id', $user->id)->limit(2)->get();
if ($accounts->count() < 2) {
    echo "❌ Perlu minimal 2 akun untuk testing.\n";
    exit;
}

$fromAccount = $accounts[0];
$toAccount = $accounts[1];

echo "💳 Transfer dari: {$fromAccount->name} (Saldo: Rp " . number_format($fromAccount->current_balance, 0, ',', '.') . ")\n";
echo "💳 Transfer ke: {$toAccount->name} (Saldo: Rp " . number_format($toAccount->current_balance, 0, ',', '.') . ")\n\n";

// Simpan saldo awal
$saldoAwal1 = $fromAccount->current_balance;
$saldoAwal2 = $toAccount->current_balance;
$totalTransaksiBefore = Transaction::where('user_id', $user->id)->count();

// Simulasi request transfer
$request = new Request([
    'type' => 'transfer',
    'account_id' => $fromAccount->id,
    'to_account_id' => $toAccount->id,
    'amount' => 50000,
    'title' => 'Test Transfer Web',
    'description' => 'Testing transfer melalui web interface',
    'transaction_date' => now()->format('Y-m-d'),
    'payment_method' => 'transfer',
]);

echo "🔄 Melakukan transfer Rp 50.000 melalui TransactionController...\n";

try {
    $controller = new TransactionController();
    $response = $controller->store($request);
    
    echo "✅ Transfer berhasil diproses\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit;
}

// Refresh data
$fromAccount->refresh();
$toAccount->refresh();
$totalTransaksiAfter = Transaction::where('user_id', $user->id)->count();

echo "💰 Saldo setelah transfer:\n";
echo "   - {$fromAccount->name}: Rp " . number_format($fromAccount->current_balance, 0, ',', '.') . "\n";
echo "   - {$toAccount->name}: Rp " . number_format($toAccount->current_balance, 0, ',', '.') . "\n\n";

// Validasi
$selisih1 = $saldoAwal1 - $fromAccount->current_balance;
$selisih2 = $toAccount->current_balance - $saldoAwal2;
$penambahanTransaksi = $totalTransaksiAfter - $totalTransaksiBefore;

echo "🔍 VALIDASI HASIL:\n";

// Test 1: Saldo akun asal berkurang
if ($selisih1 == 50000) {
    echo "✅ Test 1 PASSED: Saldo akun asal berkurang Rp 50.000\n";
} else {
    echo "❌ Test 1 FAILED: Saldo akun asal berkurang Rp " . number_format($selisih1, 0, ',', '.') . " (seharusnya Rp 50.000)\n";
}

// Test 2: Saldo akun tujuan bertambah
if ($selisih2 == 50000) {
    echo "✅ Test 2 PASSED: Saldo akun tujuan bertambah Rp 50.000\n";
} else {
    echo "❌ Test 2 FAILED: Saldo akun tujuan bertambah Rp " . number_format($selisih2, 0, ',', '.') . " (seharusnya Rp 50.000)\n";
}

// Test 3: Hanya menambah 1 transaksi
if ($penambahanTransaksi == 1) {
    echo "✅ Test 3 PASSED: Hanya menambah 1 transaksi di database\n";
} else {
    echo "❌ Test 3 FAILED: Menambah {$penambahanTransaksi} transaksi (seharusnya 1)\n";
}

// Test 4: Cek transaksi yang dibuat
$transferTransaksi = Transaction::where('user_id', $user->id)
    ->where('title', 'Test Transfer Web')
    ->get();

if ($transferTransaksi->count() == 1) {
    $transaksi = $transferTransaksi->first();
    echo "✅ Test 4 PASSED: Transaksi transfer tersimpan dengan benar\n";
    echo "   - ID: {$transaksi->id}\n";
    echo "   - Type: {$transaksi->type}\n";
    echo "   - From Account: {$transaksi->account_id}\n";
    echo "   - To Account: {$transaksi->to_account_id}\n";
    echo "   - Amount: Rp " . number_format($transaksi->amount, 0, ',', '.') . "\n";
} else {
    echo "❌ Test 4 FAILED: Ditemukan {$transferTransaksi->count()} transaksi dengan title 'Test Transfer Web'\n";
}

echo "\n=== TESTING WEB INTERFACE SELESAI ===\n";

// Cleanup
echo "\n🧹 Membersihkan data test...\n";
try {
    DB::beginTransaction();
    
    // Revert saldo
    $fromAccount->increment('current_balance', 50000);
    $toAccount->decrement('current_balance', 50000);
    
    // Hapus transaksi test
    Transaction::where('title', 'Test Transfer Web')->delete();
    
    DB::commit();
    echo "✅ Data test berhasil dibersihkan\n";
    
} catch (Exception $e) {
    DB::rollBack();
    echo "❌ Error saat cleanup: " . $e->getMessage() . "\n";
}
