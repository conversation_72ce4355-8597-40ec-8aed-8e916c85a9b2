<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use App\Models\SecurityLog;
use App\Models\User;

class SecurityMiddleware
{
    /**
     * Handle an incoming request with basic security checks
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Get client IP (considering proxies)
        $clientIp = $this->getClientIp($request);
        
        // 1. Basic Rate Limiting (less strict)
        $this->handleBasicRateLimit($request, $clientIp);
        
        // 2. Only check very obvious attacks
        $this->checkObviousAttacks($request);
        
        // 3. Basic session security
        $this->basicSessionSecurity($request);
        
        // Process request
        $response = $next($request);
        
        // 4. Add basic security headers
        $this->addBasicSecurityHeaders($response);
        
        return $response;
    }
    
    /**
     * Get real client IP address
     */
    private function getClientIp(Request $request): string
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Direct connection
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $request->ip();
    }
    
    /**
     * Handle basic rate limiting (more lenient)
     */
    private function handleBasicRateLimit(Request $request, string $clientIp): void
    {
        $key = 'basic_rate_limit:' . $clientIp;
        
        // More lenient: 200 requests per minute
        if (RateLimiter::tooManyAttempts($key, 200)) {
            SecurityLog::logEvent(
                null,
                'rate_limit_exceeded',
                "Basic rate limit exceeded for IP: {$clientIp}",
                'medium',
                ['ip' => $clientIp, 'user_agent' => $request->userAgent()]
            );
            
            abort(429, 'Too Many Requests');
        }
        
        RateLimiter::hit($key, 60);
        
        // More lenient for auth routes: 30 requests per minute
        if ($request->is('login*') || $request->is('register*')) {
            $authKey = 'auth_basic_rate_limit:' . $clientIp;
            
            if (RateLimiter::tooManyAttempts($authKey, 30)) {
                SecurityLog::logEvent(
                    null,
                    'auth_rate_limit_exceeded',
                    "Auth rate limit exceeded for IP: {$clientIp}",
                    'high',
                    ['ip' => $clientIp, 'path' => $request->path()]
                );
                
                abort(429, 'Too Many Authentication Attempts');
            }
            
            RateLimiter::hit($authKey, 60);
        }
    }
    
    /**
     * Check for only very obvious attacks (less false positives)
     */
    private function checkObviousAttacks(Request $request): void
    {
        // Only check for very obvious SQL injection patterns
        $obviousSqlPatterns = [
            // Only very obvious union attacks
            '/(\s|^)union\s+select\s/i',
            // Only obvious comment attacks
            '/(\s|^)--\s*$/i',
            // Only obvious drop/delete attacks
            '/(\s|^)(drop|truncate)\s+(table|database)/i',
        ];
        
        // Only check for very obvious XSS patterns
        $obviousXssPatterns = [
            // Only obvious script tags
            '/<script[^>]*>.*?<\/script>/is',
            // Only obvious javascript: protocol
            '/javascript\s*:\s*alert\s*\(/i',
            // Only obvious iframe attacks
            '/<iframe[^>]*src\s*=\s*["\']?javascript:/i',
        ];
        
        $allInput = array_merge($request->all(), [$request->getRequestUri()]);
        
        foreach ($allInput as $key => $value) {
            if (is_string($value)) {
                // Check obvious SQL injection
                foreach ($obviousSqlPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $this->logAndBlock($request, 'obvious_sql_injection', "Obvious SQL injection in {$key}");
                    }
                }
                
                // Check obvious XSS
                foreach ($obviousXssPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $this->logAndBlock($request, 'obvious_xss', "Obvious XSS in {$key}");
                    }
                }
            }
        }
    }
    
    /**
     * Basic session security
     */
    private function basicSessionSecurity(Request $request): void
    {
        // Regenerate session ID periodically (only for authenticated users)
        if (Auth::check()) {
            $lastRegeneration = session('last_regeneration', 0);
            
            if (time() - $lastRegeneration > 1800) { // 30 minutes (more lenient)
                $request->session()->regenerate();
                session(['last_regeneration' => time()]);
            }
        }
    }
    
    /**
     * Add basic security headers
     */
    private function addBasicSecurityHeaders($response): void
    {
        $headers = [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'SAMEORIGIN', // Less strict than DENY
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
        ];
        
        foreach ($headers as $header => $value) {
            $response->header($header, $value);
        }
    }
    
    /**
     * Log and block obvious attacks
     */
    private function logAndBlock(Request $request, string $type, string $details): void
    {
        $clientIp = $this->getClientIp($request);
        
        SecurityLog::logEvent(
            Auth::id(),
            $type,
            $details,
            'high',
            [
                'ip' => $clientIp,
                'user_agent' => $request->userAgent(),
                'path' => $request->path(),
                'method' => $request->method()
            ]
        );
        
        abort(403, 'Access Denied - Suspicious Activity Detected');
    }
}
