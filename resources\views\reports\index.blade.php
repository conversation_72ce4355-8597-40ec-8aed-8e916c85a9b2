@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON><PERSON>')

@section('page-title', 'Keuangan')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800"><PERSON><PERSON><PERSON></h1>
                    <p class="text-muted">Analisis mendalam keuangan Anda dengan berbagai jenis laporan</p>
                </div>
                <div>
                    <a href="{{ route('reports.export') }}" class="btn btn-success">
                        <i class="fas fa-download"></i> Export Laporan
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Pemasukan Bulan Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="currentMonthIncome">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                            <div class="text-xs text-muted mt-1" id="incomeChange">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Pengeluaran Bulan Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="currentMonthExpense">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                            <div class="text-xs text-muted mt-1" id="expenseChange">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Net Bulan Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="currentMonthNet">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                            <div class="text-xs text-muted mt-1" id="netChange">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Savings Rate
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="savingsRate">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                            <div class="text-xs text-muted mt-1">
                                Persentase tabungan bulan ini
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-piggy-bank fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Menu -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Menu Laporan</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('reports.monthly') }}" class="text-decoration-none">
                                <div class="card report-menu-card h-100 border-left-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-calendar-alt fa-3x text-primary mb-3"></i>
                                        <h5 class="card-title">Laporan Bulanan</h5>
                                        <p class="card-text text-muted">Analisis detail transaksi per bulan</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('reports.yearly') }}" class="text-decoration-none">
                                <div class="card report-menu-card h-100 border-left-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-bar fa-3x text-success mb-3"></i>
                                        <h5 class="card-title">Laporan Tahunan</h5>
                                        <p class="card-text text-muted">Ringkasan keuangan sepanjang tahun</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('reports.trends') }}" class="text-decoration-none">
                                <div class="card report-menu-card h-100 border-left-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-3x text-warning mb-3"></i>
                                        <h5 class="card-title">Analisis Tren</h5>
                                        <p class="card-text text-muted">Tren dan pola keuangan jangka panjang</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('reports.export') }}" class="text-decoration-none">
                                <div class="card report-menu-card h-100 border-left-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-download fa-3x text-info mb-3"></i>
                                        <h5 class="card-title">Export Data</h5>
                                        <p class="card-text text-muted">Export laporan dalam berbagai format</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Monthly Trend Chart -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Tren 6 Bulan Terakhir</h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="#" onclick="updateChart('income')">Hanya Pemasukan</a>
                            <a class="dropdown-item" href="#" onclick="updateChart('expense')">Hanya Pengeluaran</a>
                            <a class="dropdown-item" href="#" onclick="updateChart('both')">Keduanya</a>
                            <a class="dropdown-item" href="#" onclick="updateChart('net')">Net Cashflow</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="monthlyTrendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Aktivitas Terbaru</h6>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    <div id="recentActivity">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <p class="mt-2">Memuat aktivitas terbaru...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.report-menu-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
}

.report-menu-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    border-color: #4e73df;
}

.activity-item {
    padding: 12px;
    border-left: 3px solid #e3e6f0;
    margin-bottom: 12px;
    background: #f8f9fc;
    border-radius: 0 5px 5px 0;
    transition: all 0.3s ease;
}

.activity-item:hover {
    border-left-color: #4e73df;
    background: #fff;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.activity-item.income {
    border-left-color: #1cc88a;
}

.activity-item.expense {
    border-left-color: #e74a3b;
}

.activity-amount {
    font-size: 1.1em;
    font-weight: 600;
}

.activity-category {
    font-size: 0.9em;
    color: #6c757d;
}

.activity-date {
    font-size: 0.8em;
    color: #858796;
}

.change-indicator {
    font-size: 0.8em;
}

.change-indicator.positive {
    color: #1cc88a;
}

.change-indicator.negative {
    color: #e74a3b;
}

.change-indicator.neutral {
    color: #36b9cc;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    let monthlyChart = null;
    let chartData = null;
    
    // Load initial data
    loadDashboardData();
    
    function loadDashboardData() {
        $.ajax({
            url: '{{ route("reports.api.dashboard") }}',
            method: 'GET',
            success: function(response) {
                updateStats(response.stats);
                updateRecentActivity(response.recent_transactions);
                updateMonthlyChart(response.monthly_trend);
                chartData = response.monthly_trend;
            },
            error: function(xhr) {
                console.error('Error loading reports dashboard data:', xhr);
                showErrorState();
            }
        });
    }

    function updateStats(stats) {
        $('#currentMonthIncome').text(formatCurrency(stats.current_month_income));
        $('#currentMonthExpense').text(formatCurrency(stats.current_month_expense));
        $('#currentMonthNet').text(formatCurrency(stats.current_month_net));
        
        // Calculate savings rate
        const savingsRate = stats.current_month_income > 0 ? 
            Math.round(((stats.current_month_income - stats.current_month_expense) / stats.current_month_income) * 100) : 0;
        $('#savingsRate').text(savingsRate + '%');

        // Update change indicators
        updateChangeIndicator('incomeChange', stats.income_change, 'vs bulan lalu');
        updateChangeIndicator('expenseChange', stats.expense_change, 'vs bulan lalu');
        
        const netChange = stats.last_month_net > 0 ? 
            Math.round(((stats.current_month_net - stats.last_month_net) / Math.abs(stats.last_month_net)) * 100) : 0;
        updateChangeIndicator('netChange', netChange, 'vs bulan lalu');
    }

    function updateChangeIndicator(elementId, change, suffix) {
        const element = $('#' + elementId);
        const isPositive = change > 0;
        const isNeutral = change === 0;
        
        let className = isNeutral ? 'neutral' : (isPositive ? 'positive' : 'negative');
        let icon = isNeutral ? 'fas fa-minus' : (isPositive ? 'fas fa-arrow-up' : 'fas fa-arrow-down');
        
        element.html(`
            <span class="change-indicator ${className}">
                <i class="${icon}"></i> ${Math.abs(change)}% ${suffix}
            </span>
        `);
    }

    function updateRecentActivity(transactions) {
        if (transactions.length === 0) {
            $('#recentActivity').html(`
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-3"></i>
                    <p>Tidak ada aktivitas terbaru</p>
                </div>
            `);
            return;
        }

        let html = '';
        transactions.forEach(transaction => {
            const typeClass = transaction.type === 'income' ? 'income' : 'expense';
            const icon = transaction.type === 'income' ? 'fas fa-arrow-up' : 'fas fa-arrow-down';
            
            html += `
                <div class="activity-item ${typeClass}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="activity-category">
                                <i class="${icon}"></i> ${transaction.category ? transaction.category.name : 'Tanpa Kategori'}
                            </div>
                            <div class="activity-date">${formatDate(transaction.transaction_date)}</div>
                            <small class="text-muted">${transaction.account ? transaction.account.name : ''}</small>
                        </div>
                        <div class="text-right">
                            <div class="activity-amount text-${transaction.type === 'income' ? 'success' : 'danger'}">
                                ${transaction.type === 'income' ? '+' : '-'}${formatCurrency(transaction.amount)}
                            </div>
                        </div>
                    </div>
                    ${transaction.description ? `<div class="mt-1"><small class="text-muted">${transaction.description}</small></div>` : ''}
                </div>
            `;
        });

        $('#recentActivity').html(html);
    }

    function updateMonthlyChart(monthlyTrend) {
        if (monthlyChart) monthlyChart.destroy();

        const ctx = document.getElementById('monthlyTrendChart').getContext('2d');
        const labels = monthlyTrend.map(item => item.month);
        const incomeData = monthlyTrend.map(item => item.income);
        const expenseData = monthlyTrend.map(item => item.expense);
        const netData = monthlyTrend.map(item => item.net);

        monthlyChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Pemasukan',
                        data: incomeData,
                        borderColor: '#1cc88a',
                        backgroundColor: 'rgba(28, 200, 138, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Pengeluaran',
                        data: expenseData,
                        borderColor: '#e74a3b',
                        backgroundColor: 'rgba(231, 74, 59, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + value.toLocaleString('id-ID');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': Rp ' + 
                                       context.parsed.y.toLocaleString('id-ID');
                            }
                        }
                    }
                }
            }
        });
    }

    function showErrorState() {
        $('#recentActivity').html(`
            <div class="text-center text-danger py-4">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <p>Terjadi kesalahan saat memuat data</p>
                <button class="btn btn-primary btn-sm" onclick="loadDashboardData()">
                    <i class="fas fa-refresh"></i> Muat Ulang
                </button>
            </div>
        `);
    }

    function formatCurrency(amount) {
        return 'Rp ' + parseInt(amount).toLocaleString('id-ID');
    }

    function formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
        });
    }

    // Global function for chart updates
    window.updateChart = function(type) {
        if (!monthlyChart || !chartData) return;

        const labels = chartData.map(item => item.month);
        let datasets = [];

        switch(type) {
            case 'income':
                datasets = [{
                    label: 'Pemasukan',
                    data: chartData.map(item => item.income),
                    borderColor: '#1cc88a',
                    backgroundColor: 'rgba(28, 200, 138, 0.1)',
                    tension: 0.4
                }];
                break;
            case 'expense':
                datasets = [{
                    label: 'Pengeluaran',
                    data: chartData.map(item => item.expense),
                    borderColor: '#e74a3b',
                    backgroundColor: 'rgba(231, 74, 59, 0.1)',
                    tension: 0.4
                }];
                break;
            case 'net':
                datasets = [{
                    label: 'Net Cashflow',
                    data: chartData.map(item => item.net),
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    tension: 0.4
                }];
                break;
            default: // both
                datasets = [
                    {
                        label: 'Pemasukan',
                        data: chartData.map(item => item.income),
                        borderColor: '#1cc88a',
                        backgroundColor: 'rgba(28, 200, 138, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Pengeluaran',
                        data: chartData.map(item => item.expense),
                        borderColor: '#e74a3b',
                        backgroundColor: 'rgba(231, 74, 59, 0.1)',
                        tension: 0.4
                    }
                ];
        }

        monthlyChart.data.datasets = datasets;
        monthlyChart.update();
    };

    // Auto refresh every 60 seconds
    setInterval(loadDashboardData, 60000);

    // Make loadDashboardData globally accessible
    window.loadDashboardData = loadDashboardData;
});
</script>
@endpush
@endsection
