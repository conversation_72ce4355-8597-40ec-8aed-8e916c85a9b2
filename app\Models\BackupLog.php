<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class BackupLog extends Model
{
    protected $fillable = [
        'user_id',
        'backup_type',
        'backup_format',
        'file_name',
        'file_path',
        'file_size',
        'description',
        'backup_metadata',
        'status',
        'error_message',
        'started_at',
        'completed_at',
        'expires_at',
        'download_count',
        'last_downloaded_at',
        'backup_settings',
        'checksum',
    ];

    protected $casts = [
        'backup_metadata' => 'array',
        'backup_settings' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'expires_at' => 'datetime',
        'last_downloaded_at' => 'datetime',
        'file_size' => 'integer',
        'download_count' => 'integer',
    ];

    /**
     * Get the user that owns the backup log.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if backup file exists.
     */
    public function fileExists(): bool
    {
        return Storage::disk('private')->exists($this->file_path);
    }

    /**
     * Get human readable file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if backup has expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Get backup type label.
     */
    public function getBackupTypeLabelAttribute(): string
    {
        return match($this->backup_type) {
            'full' => 'Backup Lengkap',
            'partial' => 'Backup Sebagian',
            'transactions' => 'Backup Transaksi',
            'settings' => 'Backup Pengaturan',
            default => 'Unknown',
        };
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending' => 'Menunggu',
            'processing' => 'Memproses',
            'completed' => 'Selesai',
            'failed' => 'Gagal',
            'expired' => 'Kedaluwarsa',
            default => 'Unknown',
        };
    }

    /**
     * Get status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'processing' => 'info',
            'completed' => 'success',
            'failed' => 'danger',
            'expired' => 'secondary',
            default => 'secondary',
        };
    }

    /**
     * Mark backup as downloaded.
     */
    public function markAsDownloaded(): void
    {
        $this->increment('download_count');
        $this->update(['last_downloaded_at' => now()]);
    }

    /**
     * Delete backup file from storage.
     */
    public function deleteFile(): bool
    {
        if ($this->fileExists()) {
            return Storage::disk('private')->delete($this->file_path);
        }
        
        return true;
    }

    /**
     * Scope for active backups.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'completed')
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope for expired backups.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }
}
