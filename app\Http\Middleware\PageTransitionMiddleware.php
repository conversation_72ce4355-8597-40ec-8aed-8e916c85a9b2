<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PageTransitionMiddleware
{
    /**
     * Handle an incoming request untuk page transitions.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        // Jika request adalah AJAX untuk page transition
        if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            
            // Skip jika response bukan HTML atau ada error
            if (!$response->isSuccessful() || 
                !str_contains($response->headers->get('Content-Type', ''), 'text/html')) {
                return $response;
            }
            
            // Skip untuk API routes atau file downloads
            if ($request->is('api/*') || 
                $request->is('storage/*') || 
                $response->headers->get('Content-Disposition')) {
                return $response;
            }
            
            $content = $response->getContent();
            
            // Extract hanya main content untuk AJAX response
            if ($this->shouldExtractMainContent($request)) {
                $extractedContent = $this->extractMainContent($content);
                if ($extractedContent) {
                    $response->setContent($extractedContent);
                }
            }
        }
        
        return $response;
    }
    
    /**
     * Check apakah harus extract main content
     */
    private function shouldExtractMainContent(Request $request): bool
    {
        // Extract main content untuk page transitions
        return $request->header('X-Page-Transition') === 'true' ||
               $request->has('page-transition');
    }
    
    /**
     * Extract main content dari full HTML response
     */
    private function extractMainContent(string $content): ?string
    {
        // Parse HTML content
        $dom = new \DOMDocument();
        $dom->loadHTML($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        
        // Cari main content element
        $xpath = new \DOMXPath($dom);
        
        // Prioritas: main-content > dashboard-container > profile-edit-container
        $selectors = [
            '//main[@class="main-content"]',
            '//*[contains(@class, "main-content")]',
            '//*[contains(@class, "dashboard-container")]',
            '//*[contains(@class, "profile-edit-container")]'
        ];
        
        foreach ($selectors as $selector) {
            $nodes = $xpath->query($selector);
            if ($nodes->length > 0) {
                $mainContent = $nodes->item(0);
                
                // Return HTML content dari main element
                $html = '';
                foreach ($mainContent->childNodes as $childNode) {
                    $html .= $dom->saveHTML($childNode);
                }
                
                // Wrap dalam main-content jika belum ada
                if (!str_contains($html, 'class="main-content"')) {
                    $html = '<main class="main-content">' . $html . '</main>';
                }
                
                return $html;
            }
        }
        
        // Fallback: return full content jika tidak bisa extract
        return null;
    }
}
