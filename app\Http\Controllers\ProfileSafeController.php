<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProfileSafeController extends Controller
{
    public function view()
    {
        $user = Auth::user();
        return view('profile.safe', compact('user'));
    }    public function save(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Simple validation without complex rules
            $data = $request->only([
                'full_name', 'username', 'email', 'phone', 
                'birth_date', 'gender', 'address', 'bio', 
                'city', 'postal_code', 'currency', 'date_format', 
                'time_format', 'language', 'timezone'
            ]);
            
            // Basic sanitization
            foreach ($data as $key => $value) {
                if (is_string($value)) {
                    $data[$key] = trim($value);
                }
            }
            
            // Update name field with full_name
            if (!empty($data['full_name'])) {
                $data['name'] = $data['full_name'];
            }
            
            // Set defaults for required fields
            $data['currency'] = $data['currency'] ?? 'IDR';
            $data['date_format'] = $data['date_format'] ?? 'd/m/Y';
            $data['time_format'] = $data['time_format'] ?? '24';
            $data['language'] = $data['language'] ?? 'id';
            $data['timezone'] = $data['timezone'] ?? 'Asia/Jakarta';
            
            // Update user using DB query to avoid Eloquent hooks
            DB::table('users')->where('id', $user->id)->update($data);
            
            return redirect()->route('settings.profile')
                ->with('success', 'Data berhasil disimpan!');
                
        } catch (\Exception $e) {
            return redirect()->route('settings.profile')
                ->with('error', 'Gagal menyimpan data. Silakan coba lagi.');
        }
    }
}
