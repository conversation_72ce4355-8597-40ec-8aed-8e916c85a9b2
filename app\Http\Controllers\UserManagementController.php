<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\SecurityLog;
use Illuminate\Validation\Rule;

class UserManagementController extends Controller
{
    /**
     * Display a listing of all users
     */
    public function index(Request $request)
    {
        $query = User::query();
        
        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('username', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%")
                  ->orWhere('phone', 'LIKE', "%{$search}%")
                  ->orWhere('city', 'LIKE', "%{$search}%");
            });
        }
        
        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status);
        }
        
        // Filter by gender
        if ($request->has('gender') && $request->gender !== '') {
            $query->where('gender', $request->gender);
        }
        
        // Filter by verification status
        if ($request->has('verified') && $request->verified !== '') {
            if ($request->verified == '1') {
                $query->whereNotNull('email_verified_at');
            } else {
                $query->whereNull('email_verified_at');
            }
        }
        
        // Sort functionality
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = [
            'name', 'username', 'email', 'created_at', 
            'last_login_at', 'is_active', 'city'
        ];
        
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }
        
        // Pagination
        $users = $query->paginate(15)->withQueryString();
        
        // Statistics
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', 1)->count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'logged_in_today' => User::whereDate('last_login_at', today())->count(),
        ];
        
        return view('users.index', compact('users', 'stats', 'request'));
    }
    
    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        return view('users.create');
    }
    
    /**
     * Store a newly created user
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'gender' => 'nullable|in:L,P',
            'birth_date' => 'nullable|date|before:today',
            'city' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:10',
            'address' => 'nullable|string|max:1000',
            'bio' => 'nullable|string|max:1000',
            'language' => 'nullable|string|in:id,en',
            'timezone' => 'nullable|string|max:255',
            'theme' => 'nullable|in:light,solarized-dark,synth-wave',
            'currency' => 'nullable|string|max:10',
            'is_active' => 'boolean',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);
        
        try {
            DB::beginTransaction();
            
            // Handle avatar upload
            if ($request->hasFile('avatar')) {
                $avatarPath = $request->file('avatar')->store('avatars', 'public');
                $validated['avatar'] = $avatarPath;
            }
            
            // Hash password
            $validated['password'] = Hash::make($validated['password']);
            
            // Set defaults
            $validated['language'] = $validated['language'] ?? 'id';
            $validated['timezone'] = $validated['timezone'] ?? 'Asia/Jakarta';
            $validated['theme'] = $validated['theme'] ?? 'light';
            $validated['currency'] = $validated['currency'] ?? 'IDR';
            $validated['country'] = 'Indonesia';
            $validated['date_format'] = 'd/m/Y';
            $validated['time_format'] = '24';
            $validated['is_active'] = $validated['is_active'] ?? true;
            
            $user = User::create($validated);
            
            // Log security event
            SecurityLog::create([
                'user_id' => Auth::id(),
                'event_type' => 'user_created',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'description' => "User {$user->name} ({$user->email}) created by " . Auth::user()->name,
                'metadata' => json_encode([
                    'created_user_id' => $user->id,
                    'admin_user_id' => Auth::id()
                ])
            ]);
            
            DB::commit();
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'User berhasil dibuat!',
                    'user' => $user
                ]);
            }
            
            return redirect()->route('users.index')->with('success', 'User berhasil dibuat!');
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            \Log::error('User creation failed', [
                'error' => $e->getMessage(),
                'admin_user_id' => Auth::id()
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal membuat user: ' . $e->getMessage()
                ], 500);
            }
            
            return redirect()->back()->with('error', 'Gagal membuat user: ' . $e->getMessage())->withInput();
        }
    }
    
    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        // Get user's security logs
        $securityLogs = SecurityLog::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
        
        // Calculate user statistics
        $userStats = [
            'account_age' => $user->created_at->diffForHumans(),
            'last_login' => $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never',
            'login_attempts' => $user->login_attempts,
            'failed_attempts' => $user->failed_login_attempts,
            'is_locked' => $user->locked_until && $user->locked_until->isFuture(),
            'is_verified' => !is_null($user->email_verified_at),
            'two_factor_enabled' => $user->two_factor_enabled,
        ];
        
        return view('users.show', compact('user', 'securityLogs', 'userStats'));
    }
    
    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        return view('users.edit', compact('user'));
    }
    
    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => ['required', 'string', 'max:255', Rule::unique('users')->ignore($user->id)],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'gender' => 'nullable|in:L,P',
            'birth_date' => 'nullable|date|before:today',
            'city' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:10',
            'address' => 'nullable|string|max:1000',
            'bio' => 'nullable|string|max:1000',
            'language' => 'nullable|string|in:id,en',
            'timezone' => 'nullable|string|max:255',
            'theme' => 'nullable|in:light,solarized-dark,synth-wave',
            'currency' => 'nullable|string|max:10',
            'is_active' => 'boolean',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);
        
        try {
            DB::beginTransaction();
            
            // Handle avatar upload
            if ($request->hasFile('avatar')) {
                // Delete old avatar
                if ($user->avatar) {
                    Storage::disk('public')->delete($user->avatar);
                }
                
                $avatarPath = $request->file('avatar')->store('avatars', 'public');
                $validated['avatar'] = $avatarPath;
            }
            
            $originalData = $user->toArray();
            $user->update($validated);
            
            // Log security event
            SecurityLog::create([
                'user_id' => Auth::id(),
                'event_type' => 'user_updated',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'description' => "User {$user->name} ({$user->email}) updated by " . Auth::user()->name,
                'metadata' => json_encode([
                    'updated_user_id' => $user->id,
                    'admin_user_id' => Auth::id(),
                    'changes' => array_diff_assoc($validated, $originalData)
                ])
            ]);
            
            DB::commit();
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'User berhasil diperbarui!',
                    'user' => $user->fresh()
                ]);
            }
            
            return redirect()->route('users.show', $user)->with('success', 'User berhasil diperbarui!');
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            \Log::error('User update failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'admin_user_id' => Auth::id()
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal memperbarui user: ' . $e->getMessage()
                ], 500);
            }
            
            return redirect()->back()->with('error', 'Gagal memperbarui user: ' . $e->getMessage())->withInput();
        }
    }
    
    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        try {
            DB::beginTransaction();
            
            // Prevent deleting own account
            if ($user->id === Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus akun sendiri!'
                ], 403);
            }
            
            // Delete avatar file
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            
            // Log security event before deletion
            SecurityLog::create([
                'user_id' => Auth::id(),
                'event_type' => 'user_deleted',
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'description' => "User {$user->name} ({$user->email}) deleted by " . Auth::user()->name,
                'metadata' => json_encode([
                    'deleted_user_id' => $user->id,
                    'admin_user_id' => Auth::id(),
                    'user_data' => $user->toArray()
                ])
            ]);
            
            $user->delete();
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'User berhasil dihapus!'
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            \Log::error('User deletion failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'admin_user_id' => Auth::id()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus user: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Toggle user active status
     */
    public function toggleStatus(User $user)
    {
        try {
            $user->update(['is_active' => !$user->is_active]);
            
            SecurityLog::create([
                'user_id' => Auth::id(),
                'event_type' => 'user_status_changed',
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'description' => "User {$user->name} status changed to " . ($user->is_active ? 'active' : 'inactive') . " by " . Auth::user()->name,
                'metadata' => json_encode([
                    'target_user_id' => $user->id,
                    'admin_user_id' => Auth::id(),
                    'new_status' => $user->is_active
                ])
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Status user berhasil diubah!',
                'new_status' => $user->is_active
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengubah status user: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Reset user password
     */
    public function resetPassword(Request $request, User $user)
    {
        $request->validate([
            'password' => 'required|string|min:8|confirmed'
        ]);
        
        try {
            $user->update([
                'password' => Hash::make($request->password),
                'password_changed_at' => now()
            ]);
            
            SecurityLog::create([
                'user_id' => Auth::id(),
                'event_type' => 'password_reset_by_admin',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'description' => "Password for user {$user->name} was reset by " . Auth::user()->name,
                'metadata' => json_encode([
                    'target_user_id' => $user->id,
                    'admin_user_id' => Auth::id()
                ])
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Password berhasil direset!'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mereset password: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Export users data
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');
        
        $users = User::select([
            'id', 'name', 'username', 'email', 'phone', 'gender', 
            'birth_date', 'city', 'postal_code', 'address', 'bio',
            'language', 'timezone', 'is_active', 'email_verified_at',
            'last_login_at', 'created_at'
        ])->get();
        
        if ($format === 'json') {
            return response()->json($users);
        }
        
        // CSV Export
        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];
        
        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');
            
            // Header
            fputcsv($file, [
                'ID', 'Name', 'Username', 'Email', 'Phone', 'Gender',
                'Date of Birth', 'City', 'Postal Code', 'Address', 'Bio',
                'Language', 'Timezone', 'Active', 'Email Verified',
                'Last Login', 'Created At'
            ]);
            
            // Data
            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->username,
                    $user->email,
                    $user->phone,
                    $user->gender,
                    $user->birth_date,
                    $user->city,
                    $user->postal_code,
                    $user->address,
                    $user->bio,
                    $user->language,
                    $user->timezone,
                    $user->is_active ? 'Yes' : 'No',
                    $user->email_verified_at ? 'Yes' : 'No',
                    $user->last_login_at,
                    $user->created_at
                ]);
            }
            
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }
}
