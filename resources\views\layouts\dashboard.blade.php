<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://js.pusher.com https://cdnjs.cloudflare.com;
        style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.bunny.net https://cdnjs.cloudflare.com https://cdn.jsdelivr.net;
        font-src 'self' https://fonts.googleapis.com https://fonts.bunny.net https://cdnjs.cloudflare.com https://cdn.jsdelivr.net;
        img-src 'self' data: blob:;
        connect-src 'self' ws: wss:;
    ">
    <title>@yield('title', 'My Money') - {{ config('app.name', 'My Money') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Bootstrap & Custom CSS -->
    @vite(['resources/sass/app.scss'])
    
    <!-- Bootstrap CDN as fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- FontAwesome dari CDN yang diizinkan -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <!-- Global Themes CSS -->
    <link rel="stylesheet" href="{{ asset('css/themes.css') }}">
    <!-- Page Transitions CSS -->
    <link rel="stylesheet" href="{{ asset('css/page-transitions.css') }}">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
      <!-- Pusher -->
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    
    @stack('styles')
      <style>        /* Ensure FontAwesome icons are loaded properly */
        .fas, .far, .fab {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro" !important;
            font-weight: 900;
        }
        
        .far {
            font-weight: 400;
        }
        
        /* Force icon visibility with fallback */
        .menu-icon.fas::before,
        .submenu-icon.fas::before,
        .menu-arrow.fas::before {
            display: inline-block !important;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
        }
        
        /* Fallback untuk icon yang tidak ter-load */
        .menu-icon {
            position: relative;
        }
        
        .menu-icon.fa-home::before { content: "🏠"; }
        .menu-icon.fa-exchange-alt::before { content: "💱"; }
        .menu-icon.fa-tags::before { content: "🏷️"; }
        .menu-icon.fa-wallet::before { content: "👛"; }
        .menu-icon.fa-clipboard-list::before { content: "📋"; }
        .menu-icon.fa-chart-bar::before { content: "📊"; }
        .menu-icon.fa-cog::before { content: "⚙️"; }
        
        :root {
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            
            /* Profile Page Variables - Light Theme */
            --profile-bg-primary: #ffffff;
            --profile-bg-secondary: #f8f9fa;
            --profile-text-primary: #333333;
            --profile-text-secondary: #666666;
            --profile-text-muted: #999999;
            --profile-border: #e9ecef;
            --profile-accent: #667eea;
            --profile-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --profile-success: #d4edda;
            --profile-success-text: #155724;
            --profile-error: #f8d7da;
            --profile-error-text: #721c24;
            --profile-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        /* Profile Components Styling */
        .alert {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid var(--profile-border);
        }
        
        .alert-success {
            background: var(--profile-success);
            color: var(--profile-success-text);
        }
        
        .alert-error {
            background: var(--profile-error);
            color: var(--profile-error-text);
        }        .profile-header-section {
            background: var(--profile-gradient);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            color: white;
            position: relative;
            overflow: hidden;
            z-index: 1; /* Lower z-index to ensure dropdowns appear above */
        }

        .profile-header-bg-decoration {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .decoration-circle-1 {
            position: absolute;
            top: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }

        .decoration-circle-2 {
            position: absolute;
            bottom: -30px;
            left: -30px;
            width: 150px;
            height: 150px;
            background: rgba(255,255,255,0.05);
            border-radius: 50%;
        }

        .profile-header-content {
            display: flex;
            align-items: center;
            gap: 30px;
            position: relative;
            z-index: 1;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            font-weight: 600;
            border: 4px solid rgba(255,255,255,0.3);
            overflow: hidden;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .profile-header-info {
            flex: 1;
        }

        .profile-main-title {
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .profile-subtitle {
            margin: 0 0 5px 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .profile-badges {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .profile-badge {
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .profile-badge.status-badge.active {
            background: rgba(40, 167, 69, 0.8);
        }

        .profile-badge.status-badge.inactive {
            background: rgba(220, 53, 69, 0.8);
        }

        .profile-content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }

        .profile-left-column,
        .profile-right-column {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .profile-card {
            background: var(--profile-bg-primary);
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--profile-shadow);
            border: 1px solid var(--profile-border);
        }

        .profile-card-title {
            color: var(--profile-text-primary);
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.4rem;
        }

        .profile-card-icon {
            color: var(--profile-accent);
        }

        .profile-fields-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .profile-field {
            display: flex;
            flex-direction: column;
        }

        .profile-field.full-width {
            grid-column: 1 / -1;
        }

        .profile-field-label {
            font-weight: 600;
            color: var(--profile-text-secondary);
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        .profile-field-value {
            margin: 0;
            padding: 12px;
            background: var(--profile-bg-secondary);
            border-radius: 8px;
            color: var(--profile-text-primary);
            border: 1px solid var(--profile-border);
        }

        .profile-field-textarea {
            min-height: 50px;
        }

        .verification-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 8px;
        }

        .verification-badge.verified {
            background: var(--profile-success);
            color: var(--profile-success-text);
        }

        .verification-badge.unverified {
            background: var(--profile-error);
            color: var(--profile-error-text);
        }

        .theme-display {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .profile-status-list,
        .profile-security-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .profile-status-item,
        .security-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: var(--profile-bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--profile-border);
        }

        .status-label,
        .security-label {
            font-weight: 500;
            color: var(--profile-text-secondary);
        }

        .status-value,
        .security-value {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-value.active,
        .status-value.verified,
        .status-value.enabled {
            background: var(--profile-success);
            color: var(--profile-success-text);
        }

        .status-value.inactive,
        .status-value.unverified,
        .status-value.disabled {
            background: var(--profile-error);
            color: var(--profile-error-text);
        }

        .security-value.warning {
            color: #856404;
        }

        .security-item.locked {
            background: var(--profile-error);
            color: var(--profile-error-text);
        }

        .security-item.active {
            background: var(--profile-success);
            color: var(--profile-success-text);
        }

        .profile-timeline {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .timeline-item {
            display: flex;
            gap: 15px;
            padding-left: 10px;
            border-left: 3px solid var(--profile-accent);
            position: relative;
        }

        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }

        .timeline-icon.login {
            background: var(--profile-accent);
        }

        .timeline-icon.activity {
            background: #28a745;
        }

        .timeline-icon.password {
            background: #ffc107;
        }

        .timeline-icon.backup {
            background: #17a2b8;
        }

        .timeline-content h4 {
            margin: 0 0 5px 0;
            font-weight: 600;
            color: var(--profile-text-primary);
        }

        .timeline-date {
            margin: 0;
            color: var(--profile-text-secondary);
            font-size: 0.9rem;
        }

        .timeline-detail {
            margin: 2px 0 0 0;
            color: var(--profile-text-muted);
            font-size: 0.8rem;
        }

        .profile-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .profile-action-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .profile-action-btn.primary {
            background: var(--profile-gradient);
            color: white;
        }

        .profile-action-btn.secondary {
            background: var(--profile-bg-secondary);
            color: var(--profile-text-primary);
            border: 1px solid var(--profile-border);
        }

        .profile-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            text-decoration: none;
        }

        .profile-action-btn.primary:hover {
            color: white;
        }

        .profile-action-btn.secondary:hover {
            color: var(--profile-text-primary);
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Figtree', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .app-container {
            display: flex;
            min-height: 100vh;
        }
          /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
            overflow-x: hidden;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
          .sidebar-subtitle {
            font-size: 0.8rem;
            color: #666;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .sidebar-menu {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        
        /* Styling khusus untuk sidebar collapsed */
        .sidebar.collapsed .sidebar-menu {
            padding: 10px 0;
        }
        
        .sidebar.collapsed .menu-item {
            margin: 6px 0;
        }
        
        .sidebar.collapsed .menu-item:first-child {
            margin-top: 20px;
        }
        
        .menu-item {
            position: relative;
        }
          .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border-radius: 8px;
            margin: 2px 16px;
        }
          .menu-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            text-decoration: none;
            transform: translateX(2px);
        }
        
        .menu-link.active {
            background: var(--primary-color);
            color: white;
        }        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
            font-size: 1rem;
            color: inherit;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: margin-right 0.3s ease, font-size 0.3s ease;
        }
          .menu-text {
            flex: 1;
            font-weight: 500;
            transition: opacity 0.3s ease, visibility 0.3s ease, width 0.3s ease;
        }
        
        .menu-arrow {
            font-size: 0.8rem;
            transition: transform 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .menu-arrow.rotated {
            transform: rotate(90deg);
        }
        
        .submenu {
            list-style: none;
            margin: 0;
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.05);
        }
        
        .submenu.open {
            max-height: 300px;
        }
        
        .submenu-item {
            padding: 8px 20px 8px 52px;
        }
        
        .submenu-link {
            display: flex;
            align-items: center;
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .submenu-link:hover {
            color: var(--primary-color);
            text-decoration: none;
        }
          .submenu-icon {
            width: 16px;
            margin-right: 8px;
            text-align: center;
            font-size: 0.9rem;
            color: inherit;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
          /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            flex: 1;
            min-height: 100vh;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: calc(100% - var(--sidebar-width));
            max-width: calc(100% - var(--sidebar-width));
            display: flex;
            flex-direction: column;
        }
          .topbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            height: var(--topbar-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 100;
            order: 1;
        }
        
        .topbar-left h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }
        
        .topbar-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            overflow: hidden;
            position: relative;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }
        
        .user-avatar-text {
            font-size: 1rem;
            color: white;
            font-weight: 600;
        }
        
        .user-details h4 {
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .user-details p {
            margin: 0;
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .dashboard-container {
            padding: 30px;
            min-height: calc(100vh - var(--topbar-height));
            position: relative;
            z-index: 1;
            order: 2;
            flex: 1;
        }
          
        .dashboard-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            color: white;
        }
        
        .welcome-section h2 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .welcome-section p {
            opacity: 0.9;
            margin: 0;
        }
        
        .btn-logout {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .dashboard-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .card-subtitle {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .card-content {
            color: #555;
            line-height: 1.6;
        }
        
        .finance-icon {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .security-icon {
            background: linear-gradient(45deg, #11998e, #38ef7d);
            color: white;
        }
        
        .stats-icon {
            background: linear-gradient(45deg, #fa709a, #fee140);
            color: white;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0 0 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .quick-actions h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
            display: block;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }        /* Theme Switcher in Topbar */
        .theme-switcher {
            position: relative;
            z-index: 999999; /* Ensure the entire theme switcher is above everything */
        }
        
        .theme-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
          .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
            z-index: 999999; /* Increased z-index to ensure it's above profile header */
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
        
        /* User Info Dropdown */
        .user-info-dropdown {
            position: relative;
        }
        
        .user-info-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .user-info-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
          .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
            z-index: 999999; /* Increased z-index to match theme dropdown */
        }
        
        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .user-dropdown-item:hover {
            background: #f0f0f0;
        }
        
        .user-dropdown-icon {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }          /* Sidebar Collapse Button */
        .sidebar-collapse-btn {
            position: fixed;
            top: 20px;
            left: calc(var(--sidebar-width) - 15px);
            width: 30px;
            height: 30px;
            background: var(--primary-color);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
        }
        
        .sidebar-collapse-btn:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        /* Tombol saat sidebar collapsed - posisi berubah ke kanan */
        .sidebar.collapsed + .sidebar-collapse-btn {
            left: calc(80px - 15px);
        }.sidebar.collapsed {
            width: 80px;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
          .sidebar.collapsed .sidebar-header {
            padding: 12px 3px;
            text-align: center;
        }
        
        .sidebar.collapsed .sidebar-logo {
            font-size: 1.4rem;
            margin-bottom: 0;
        }
        
        .sidebar.collapsed .sidebar-subtitle {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        
        .sidebar.collapsed .menu-text {
            opacity: 0;
            visibility: hidden;
            width: 0;
            transition: opacity 0.2s ease, visibility 0.2s ease, width 0.2s ease;
        }
        
        .sidebar.collapsed .menu-arrow {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        
        .sidebar.collapsed .submenu {
            display: none;
        }        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 8px 4px;
            margin: 4px 6px;
            border-radius: 8px;
            position: relative;
            min-height: 36px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }          .sidebar.collapsed .menu-link:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.12);
            border-color: rgba(102, 126, 234, 0.15);
        }
          .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            border-color: transparent;        }.sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 1.1rem;
            color: inherit;
            width: auto;
            min-width: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
          /* Tooltip for collapsed sidebar */
        .sidebar.collapsed .menu-link::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 15px;
            z-index: 10000;
            pointer-events: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .sidebar.collapsed .menu-link:hover::after {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) translateX(5px);
        }        .main-content.sidebar-collapsed {
            margin-left: 70px;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: calc(100% - 70px);
            max-width: calc(100% - 70px);
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
          /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-collapse-btn {
                left: 15px;
                top: 15px;
            }
            
            .sidebar.collapsed + .sidebar-collapse-btn {
                left: 15px;
            }
            
            .topbar {
                padding: 0 15px;
            }
            
            .dashboard-container {
                padding: 15px;
            }
            
            .dashboard-content {
                grid-template-columns: 1fr;
            }
              .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .user-info-toggle .user-details h4 {
                display: none;
            }
            
            .user-info-toggle .user-details p {
                display: none;
            }
            
            .user-dropdown {
                min-width: 150px;
            }
        }
        
        .theme-icon {
            font-size: 1rem;
        }
          .dropdown-arrow {
            font-size: 0.7rem;
            transition: transform 0.3s ease;
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
          /* Responsive */
        @media (max-width: 768px) {
            .welcome-section h2 {
                font-size: 1.3rem;
            }
        }
          /* Theme Variations for Sidebar */
        /* Solarized Dark Theme */
        body[data-theme="solarized-dark"] {
            --profile-bg-primary: #002b36;
            --profile-bg-secondary: #073642;
            --profile-text-primary: #839496;
            --profile-text-secondary: #586e75;
            --profile-text-muted: #657b83;
            --profile-border: #073642;
            --profile-accent: #268bd2;
            --profile-gradient: linear-gradient(135deg, #268bd2 0%, #2aa198 100%);
            --profile-success: #073642;
            --profile-success-text: #859900;
            --profile-error: #073642;
            --profile-error-text: #dc322f;
            --profile-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }

        body[data-theme="solarized-dark"] .profile-header-section {
            box-shadow: 0 0 30px rgba(38, 139, 210, 0.3);
        }

        body[data-theme="solarized-dark"] .profile-avatar {
            box-shadow: 0 0 20px rgba(38, 139, 210, 0.5);
        }

        body[data-theme="solarized-dark"] .profile-main-title {
            text-shadow: 0 0 10px rgba(38, 139, 210, 0.8);
        }

        body[data-theme="solarized-dark"] .profile-card {
            box-shadow: 0 0 20px rgba(38, 139, 210, 0.2);
        }

        body[data-theme="solarized-dark"] .profile-card-title {
            text-shadow: 0 0 10px rgba(38, 139, 210, 0.6);
        }

        body[data-theme="solarized-dark"] .timeline-item {
            border-left-color: #268bd2;
        }

        /* Synth Wave Theme */
        body[data-theme="synth-wave"] {
            --profile-bg-primary: #1a1a2e;
            --profile-bg-secondary: #16213e;
            --profile-text-primary: #e94560;
            --profile-text-secondary: #f39c12;
            --profile-text-muted: #0f3460;
            --profile-border: #0f3460;
            --profile-accent: #ff00ff;
            --profile-gradient: linear-gradient(135deg, #ff00ff 0%, #00ffff 100%);
            --profile-success: #16213e;
            --profile-success-text: #00ff00;
            --profile-error: #16213e;
            --profile-error-text: #ff0000;
            --profile-shadow: 0 10px 30px rgba(255,0,255,0.3);
        }

        body[data-theme="synth-wave"] .profile-header-section {
            box-shadow: 0 0 30px rgba(255,0,255,0.5);
        }

        body[data-theme="synth-wave"] .profile-avatar {
            box-shadow: 0 0 20px rgba(255,0,255,0.7);
        }

        body[data-theme="synth-wave"] .profile-main-title {
            text-shadow: 0 0 10px rgba(255,0,255,0.8);
        }

        body[data-theme="synth-wave"] .profile-card:nth-child(1) {
            box-shadow: 0 0 20px rgba(255,0,255,0.3);
        }

        body[data-theme="synth-wave"] .profile-card:nth-child(2) {
            box-shadow: 0 0 20px rgba(0,255,255,0.3);
        }

        body[data-theme="synth-wave"] .profile-card:nth-child(3) {
            box-shadow: 0 0 20px rgba(255,106,53,0.3);
        }

        body[data-theme="synth-wave"] .profile-card-title {
            text-shadow: 0 0 10px rgba(255,0,255,0.8);
        }

        body[data-theme="synth-wave"] .profile-badge:nth-child(1) {
            border: 1px solid rgba(255,0,255,0.5);
        }

        body[data-theme="synth-wave"] .profile-badge:nth-child(2) {
            border: 1px solid rgba(0,255,255,0.5);
        }

        body[data-theme="synth-wave"] .theme-display {
            box-shadow: 0 0 10px rgba(255,0,255,0.5);
        }

        body[data-theme="synth-wave"] .timeline-item {
            border-left-color: #ff00ff;
        }

        body[data-theme="synth-wave"] .timeline-icon.login {
            box-shadow: 0 0 15px rgba(255,0,255,0.7);
        }

        body[data-theme="synth-wave"] .timeline-icon.activity {
            box-shadow: 0 0 15px rgba(0,255,0,0.7);
        }

        body[data-theme="synth-wave"] .timeline-icon.password {
            box-shadow: 0 0 15px rgba(255,255,0,0.7);
        }

        body[data-theme="synth-wave"] .timeline-icon.backup {
            box-shadow: 0 0 15px rgba(0,255,255,0.7);
        }

        /* Mobile Responsive for Profile */
        @media (max-width: 768px) {
            .profile-content-grid {
                grid-template-columns: 1fr;
            }
            
            .profile-header-content {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }
            
            .profile-fields-grid {
                grid-template-columns: 1fr;
            }
            
            .profile-badges {
                justify-content: center;
            }
            
            .profile-avatar {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }
            
            .profile-main-title {
                font-size: 2rem;
            }
        }

        /* SweetAlert2 Z-Index Fix */
        .swal2-container {
            z-index: 2147483647 !important; /* Maximum safe z-index value */
        }
        
        .swal2-popup {
            z-index: 2147483647 !important;
        }

        /* Additional SweetAlert2 styling for better appearance */
        .swal2-toast {
            z-index: 2147483647 !important;
        }
        
        /* Theme-specific styles for user avatar */
        body[data-theme="solarized-dark"] .user-avatar {
            background: rgba(38, 139, 210, 0.2);
            border: 2px solid rgba(38, 139, 210, 0.3);
        }
        
        body[data-theme="solarized-dark"] .user-avatar-text {
            color: #268bd2;
        }
        
        body[data-theme="synth-wave"] .user-avatar {
            background: rgba(255, 0, 255, 0.2);
            border: 2px solid rgba(255, 0, 255, 0.5);
            box-shadow: 0 0 10px rgba(255, 0, 255, 0.3);
        }
        
        body[data-theme="synth-wave"] .user-avatar-text {
            color: #ff00ff;
            text-shadow: 0 0 5px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="dark"] .user-avatar {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        body[data-theme="dark"] .user-avatar-text {
            color: #ffffff;
        }
    </style>
</head>
<body data-theme="{{ auth()->user()->theme ?? 'light' }}">    <div class="app-container">        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">My Money 💰</div>
            </div>
            
            <nav>
                <ul class="sidebar-menu">
                    @php
                        $menuItems = config('menu', []);
                        $currentRoute = request()->route()->getName();
                    @endphp
                    
                    @foreach($menuItems as $key => $menu)
                        <li class="menu-item">
                            <a href="{{ $menu['route'] === '#' ? 'javascript:void(0)' : route($menu['route']) }}" 
                               class="menu-link {{ $currentRoute === $menu['route'] ? 'active' : '' }}"
                               onclick="{{ !empty($menu['submenu']) ? 'toggleSubmenu(this)' : '' }}">
                                <i class="menu-icon {{ $menu['icon'] }}"></i>
                                <span class="menu-text">{{ $menu['name'] }}</span>
                                @if(!empty($menu['submenu']))
                                    <i class="menu-arrow fas fa-chevron-right"></i>
                                @endif
                            </a>
                            
                            @if(!empty($menu['submenu']))
                                <ul class="submenu">
                                    @foreach($menu['submenu'] as $subKey => $submenu)
                                        <li class="submenu-item">
                                            <a href="{{ route($submenu['route']) }}" class="submenu-link">
                                                <i class="submenu-icon {{ $submenu['icon'] }}"></i>
                                                <span>{{ $submenu['name'] }}</span>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                        </li>
                    @endforeach                </ul>
            </nav>
        </aside>
        
        <!-- Collapse Button -->
        <button class="sidebar-collapse-btn" id="sidebarCollapseBtn" onclick="toggleSidebarCollapse()">
            <i class="fas fa-chevron-left" id="collapseIcon"></i>
        </button>
        
        <!-- Main Content -->
        <main class="main-content">            <!-- Topbar -->
            <header class="topbar">
                <div class="topbar-left">
                    <h1>@yield('page-title', 'Dashboard')</h1>
                </div>
                  <div class="topbar-right">
                    <!-- Theme Switcher -->
                    <div class="theme-switcher">
                        <button class="theme-toggle" id="themeToggle" onclick="toggleThemeDropdown()">
                            <i class="theme-icon" id="themeIcon">�</i>
                            <span class="theme-text" id="themeText">Light</span>
                            <i class="dropdown-arrow">▼</i>
                        </button>
                        <div class="theme-dropdown" id="themeDropdown">
                            <div class="theme-option" onclick="setTheme('light')">
                                <i class="theme-option-icon">🌞</i>
                                <span>Light</span>
                            </div>
                            <div class="theme-option" onclick="setTheme('solarized-dark')">
                                <i class="theme-option-icon">🌙</i>
                                <span>Solarized Dark</span>
                            </div>
                            <!--<div class="theme-option" onclick="setTheme('synth-wave')">
                                <i class="theme-option-icon">🌆</i>
                                <span>Synth Wave</span>
                            </div>-->
                        </div>
                    </div>
                    <!-- User Info Dropdown -->
                    <div class="user-info-dropdown">
                        <button class="user-info-toggle" id="userInfoToggle" onclick="toggleUserDropdown()">
                            <div class="user-avatar">
                                @if(Auth::user()->avatar)
                                    <img src="{{ Storage::url(Auth::user()->avatar) }}" 
                                         alt="{{ Auth::user()->name }}" 
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="user-avatar-text" style="display: none;">
                                        {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                    </div>
                                @else
                                    <div class="user-avatar-text">
                                        {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                    </div>
                                @endif
                            </div>
                            <div class="user-details">
                                <h4>{{ Auth::user()->name }}</h4>
                            </div>
                            <i class="dropdown-arrow">▼</i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <div class="user-dropdown-item" onclick="goToProfile()">
                                <i class="user-dropdown-icon fas fa-user"></i>
                                <span>Profile</span>
                            </div>
                            <div class="user-dropdown-item" onclick="confirmLogout()">
                                <i class="user-dropdown-icon fas fa-sign-out-alt"></i>
                                <span>Logout</span>
                            </div>
                        </div>
                    </div>
                    
                    
                    <!-- Hidden Logout Form -->
                    <form id="logoutForm" method="POST" action="{{ route('logout') }}" style="display: none;">
                        @csrf
                    </form>
                </div></header>
              <!-- Page Content -->
            <div class="dashboard-container">
                @yield('content')
            </div>
        </main>
    </div>
    
    <!-- Bootstrap JS CDN as fallback -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery CDN - Required for many components -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
    
    @vite(['resources/js/app.js'])
    
    <script>
        // Ensure proper layout order
        document.addEventListener('DOMContentLoaded', function() {
            const mainContent = document.querySelector('.main-content');
            const topbar = document.querySelector('.topbar');
            const dashboardContainer = document.querySelector('.dashboard-container');
            
            // Ensure topbar comes first
            if (mainContent && topbar && dashboardContainer) {
                mainContent.insertBefore(topbar, dashboardContainer);
            }
        });
        
        // Sidebar functionality
        function toggleSidebarCollapse() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('.main-content');
            const collapseIcon = document.getElementById('collapseIcon');
            const menuLinks = document.querySelectorAll('.menu-link');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('sidebar-collapsed');
            
            // Toggle icon with smooth transition
            if (sidebar.classList.contains('collapsed')) {
                collapseIcon.className = 'fas fa-chevron-right';
                
                // Add tooltips to menu items
                menuLinks.forEach(link => {
                    const menuText = link.querySelector('.menu-text');
                    if (menuText) {
                        link.setAttribute('data-tooltip', menuText.textContent.trim());
                    }
                });
            } else {
                collapseIcon.className = 'fas fa-chevron-left';
                
                // Remove tooltips
                menuLinks.forEach(link => {
                    link.removeAttribute('data-tooltip');
                });
            }
            
            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }
        
        function toggleSubmenu(element) {
            const submenu = element.nextElementSibling;
            const arrow = element.querySelector('.menu-arrow');
            
            if (submenu) {
                submenu.classList.toggle('open');
                arrow.classList.toggle('rotated');
            }
        }        // Theme functionality
        function toggleThemeDropdown() {
            const dropdown = document.getElementById('themeDropdown');
            dropdown.classList.toggle('show');
        }
        
        // User dropdown functionality
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }
          function goToProfile() {
            window.location.href = '{{ route("settings.profile") }}';
        }
        
        // Function to apply theme without saving to database (used on page load)
        function applyThemeOnly(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');
            
            switch(theme) {
                case 'light':
                    themeIcon.textContent = '🌞';
                    themeText.textContent = 'Light';
                    break;
                case 'solarized-dark':
                    themeIcon.textContent = '🌙';
                    themeText.textContent = 'Solarized Dark';
                    break;
                case 'synth-wave':
                    themeIcon.textContent = '🌆';
                    themeText.textContent = 'Synth Wave';
                    break;
            }
        }
        
        // Function to set theme and save to database (used when user clicks theme option)
          function setTheme(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');
            
            // Update UI immediately
            switch(theme) {
                case 'light':
                    themeIcon.textContent = '🌞';
                    themeText.textContent = 'Light';
                    break;
                case 'solarized-dark':
                    themeIcon.textContent = '🌙';
                    themeText.textContent = 'Solarized Dark';
                    break;
                case 'synth-wave':
                    themeIcon.textContent = '🌆';
                    themeText.textContent = 'Synth Wave';
                    break;
            }
            
            document.getElementById('themeDropdown').classList.remove('show');
            
            // Trigger custom event for real-time theme update
            const themeChangeEvent = new CustomEvent('themeChanged', {
                detail: { 
                    theme: theme,
                    themeDisplayName: themeText.textContent
                }
            });
            window.dispatchEvent(themeChangeEvent);
            
            // Save to database
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            // Show loading notification
            const loadingToast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true
            });
            
            loadingToast.fire({
                icon: 'info',
                title: 'Menyimpan tema...'
            });
            
            fetch('/settings/theme', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    theme: theme
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Get theme display name
                    let themeDisplayName = '';
                    switch(theme) {
                        case 'light':
                            themeDisplayName = 'Light Mode';
                            break;
                        case 'solarized-dark':
                            themeDisplayName = 'Solarized Dark';
                            break;
                        case 'synth-wave':
                            themeDisplayName = 'Synth Wave';
                            break;
                    }
                    
                    // Show success notification
                    const successToast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true
                    });
                    
                    successToast.fire({
                        icon: 'success',
                        title: `✅ Tema berhasil diterapkan!`,
                        text: `Tema ${themeDisplayName} telah disimpan`
                    });
                } else {
                    throw new Error(data.message || 'Gagal menyimpan tema');
                }
            })
            .catch(error => {
                console.error('Error saving theme:', error);
                
                // Show error notification
                const errorToast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true
                });
                
                errorToast.fire({
                    icon: 'error',
                    title: '❌ Gagal menyimpan tema',
                    text: 'Tema telah diterapkan secara lokal, namun gagal disimpan ke database'
                });
            });
        }          // Load saved theme and sidebar state
        document.addEventListener('DOMContentLoaded', function() {
            // Load theme from database (user preference)
            const userTheme = '{{ Auth::user()->theme ?? "light" }}';
            const savedLocalTheme = localStorage.getItem('theme') || userTheme;
            
            // If database theme differs from localStorage, use database theme
            const themeToUse = userTheme !== 'light' ? userTheme : savedLocalTheme;
            
            // Apply theme without saving (to avoid showing notification on page load)
            applyThemeOnly(themeToUse);              // Load sidebar collapsed state
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                // Set collapsed state immediately for smooth transition
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.querySelector('.main-content');
                const collapseIcon = document.getElementById('collapseIcon');
                const menuLinks = document.querySelectorAll('.menu-link');
                
                sidebar.classList.add('collapsed');
                mainContent.classList.add('sidebar-collapsed');
                collapseIcon.className = 'fas fa-chevron-right';
                
                // Add tooltips to menu items
                menuLinks.forEach(link => {
                    const menuText = link.querySelector('.menu-text');
                    if (menuText) {
                        link.setAttribute('data-tooltip', menuText.textContent.trim());
                    }
                });
            }
            
            // Initialize real-time dashboard only on dashboard page
            const isDashboardPage = window.location.pathname === '/' || 
                                   window.location.pathname === '/dashboard' ||
                                   window.location.pathname.includes('dashboard');
            
            if (isDashboardPage) {
                initializeRealtimeDashboard();
            }
            
            // Show welcome greeting only once per session and only on dashboard
            const hasShownGreeting = sessionStorage.getItem('hasShownGreeting');
            if (!hasShownGreeting && isDashboardPage) {
                setTimeout(() => {
                    showWelcomeGreeting();
                    sessionStorage.setItem('hasShownGreeting', 'true');
                }, 1000);
            }
        });
        
        // Real-time Dashboard Functions
        function initializeRealtimeDashboard() {
            // Simulate real-time data updates
            updateDashboardData();
            
            // Update data every 30 seconds
            setInterval(updateDashboardData, 30000);
            
            // Initialize Pusher for real-time updates
            if (typeof Pusher !== 'undefined') {
                initializePusher();
            }
        }
        
        function updateDashboardData() {
            // Only update if dashboard elements exist
            const dashboardElements = ['totalBalance', 'monthlyIncome', 'monthlyExpense', 'todayTransactions', 'activeAccounts'];
            const elementsExist = dashboardElements.some(id => document.getElementById(id) !== null);
            
            if (!elementsExist) {
                return; // Exit if dashboard elements don't exist
            }
            
            // Simulate fetching new data with animation
            animateValue('totalBalance', 0, ********, 2000, 'currency');
            animateValue('monthlyIncome', 0, 8500000, 2000, 'currency');
            animateValue('monthlyExpense', 0, 3250000, 2000, 'currency');
            animateValue('todayTransactions', 0, 7, 1500);
            animateValue('activeAccounts', 0, 3, 1500);
            
            // Update recent activities with fade effect
            setTimeout(() => {
                updateRecentActivities();
            }, 1000);
        }
        
        function animateValue(elementId, start, end, duration, format = 'number') {
            const element = document.getElementById(elementId);
            
            // Check if element exists before proceeding
            if (!element) {
                console.warn(`Element with ID '${elementId}' not found`);
                return;
            }
            
            const startTime = performance.now();
            
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Easing function
                const easedProgress = 1 - Math.pow(1 - progress, 3);
                const current = Math.round(start + (end - start) * easedProgress);
                
                if (format === 'currency') {
                    element.textContent = 'Rp ' + current.toLocaleString('id-ID');
                } else {
                    element.textContent = current.toLocaleString('id-ID');
                }
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            
            requestAnimationFrame(update);
        }
        
        function updateRecentActivities() {
            const activitiesContainer = document.getElementById('recentActivities');
            
            // Check if element exists before proceeding
            if (!activitiesContainer) {
                console.warn('Recent activities container not found');
                return;
            }
            
            activitiesContainer.style.opacity = '0.5';
            
            setTimeout(() => {
                // This would normally fetch from API
                const newActivities = [
                    {
                        icon: '🍕',
                        title: 'Makan Siang',
                        time: 'Baru saja',
                        amount: -85000,
                        type: 'expense'
                    },
                    {
                        icon: '🏪',
                        title: 'Belanja Groceries',
                        time: '2 jam yang lalu',
                        amount: -250000,
                        type: 'expense'
                    },
                    {
                        icon: '💼',
                        title: 'Gaji Bulanan',
                        time: '3 hari yang lalu',
                        amount: 8500000,
                        type: 'income'
                    }
                ];
                
                activitiesContainer.innerHTML = newActivities.map(activity => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                        <div>
                            <div style="font-weight: 500;">${activity.icon} ${activity.title}</div>
                            <div style="font-size: 0.8rem; color: #666;">${activity.time}</div>
                        </div>
                        <div style="color: ${activity.type === 'income' ? '#28a745' : '#dc3545'}; font-weight: 600;">
                            ${activity.type === 'income' ? '+' : '-'}Rp ${Math.abs(activity.amount).toLocaleString('id-ID')}
                        </div>
                    </div>
                `).join('');
                
                activitiesContainer.style.opacity = '1';
            }, 500);
        }
        
        function initializePusher() {
            try {
                // Replace with your actual Pusher key
                const pusher = new Pusher('{{ env("PUSHER_APP_KEY") }}', {
                    cluster: '{{ env("PUSHER_APP_CLUSTER") }}',
                    encrypted: true
                });
                
                const channel = pusher.subscribe('financial-updates');
                
                // Listen for new transactions
                channel.bind('new-transaction', function(data) {
                    showNewTransactionNotification(data);
                    updateDashboardData();
                });
                
                // Listen for balance updates
                channel.bind('balance-updated', function(data) {
                    animateValue('totalBalance', 0, data.newBalance, 1500, 'currency');
                });
                
                console.log('Pusher initialized successfully');
            } catch (error) {
                console.log('Pusher not configured or error:', error);
            }
        }
        
        function showNewTransactionNotification(data) {
            // Show toast notification for new transaction
            Swal.fire({
                toast: true,
                position: 'top-end',
                icon: data.type === 'income' ? 'success' : 'info',
                title: `Transaksi Baru: ${data.title}`,
                text: `${data.type === 'income' ? '+' : '-'}Rp ${Math.abs(data.amount).toLocaleString('id-ID')}`,
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        }        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const themeSwitcher = document.querySelector('.theme-switcher');
            const userInfoDropdown = document.querySelector('.user-info-dropdown');
            
            if (!themeSwitcher.contains(event.target)) {
                document.getElementById('themeDropdown').classList.remove('show');
            }
            
            if (!userInfoDropdown.contains(event.target)) {
                document.getElementById('userDropdown').classList.remove('show');
            }
        });
          // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const sidebarCollapseBtn = document.querySelector('.sidebar-collapse-btn');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !sidebarCollapseBtn.contains(event.target)) {
                sidebar.classList.remove('open');
            }
        });
        
        // Welcome greeting function
        function showWelcomeGreeting() {
            const userName = '{{ Auth::user()->name }}';
            const currentHour = new Date().getHours();
            let greeting = '';
            let icon = '';
            
            if (currentHour < 12) {
                greeting = 'Selamat Pagi';
                icon = '🌅';
            } else if (currentHour < 17) {
                greeting = 'Selamat Siang';
                icon = '☀️';
            } else if (currentHour < 21) {
                greeting = 'Selamat Sore';
                icon = '🌇';
            } else {
                greeting = 'Selamat Malam';
                icon = '🌙';
            }
            
            Swal.fire({
                title: `${icon} ${greeting}, ${userName}!`,
                text: 'Selamat datang kembali di My Money. Siap mengelola keuangan Anda hari ini?',
                icon: 'success',
                confirmButtonText: 'Mulai Sekarang!',
                confirmButtonColor: '#667eea',
                timer: 5000,
                timerProgressBar: true,
                allowOutsideClick: false,
                customClass: {
                    popup: 'animated bounceIn'
                }
            });
        }
          // Logout confirmation
        function confirmLogout() {
            Swal.fire({
                title: '🚪 Logout Konfirmasi',
                text: 'Apakah Anda yakin ingin keluar dari akun?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#dc3545',
                confirmButtonText: 'Ya, Logout',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show logout success message
                    Swal.fire({
                        title: '👋 Sampai Jumpa!',
                        text: 'Anda telah berhasil logout. Terima kasih telah menggunakan My Money!',
                        icon: 'success',
                        timer: 2000,
                        timerProgressBar: true,
                        showConfirmButton: false
                    }).then(() => {
                        // Clear session storage
                        sessionStorage.clear();
                        // Submit logout form
                        document.getElementById('logoutForm').submit();
                    });
                }
            });
        }
        
        // Flash Messages with SweetAlert2
        @if(session('status'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('status') }}',
                confirmButtonColor: '#667eea',
                timer: 3000,
                timerProgressBar: true
            });
        @endif

        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                confirmButtonColor: '#667eea',
                timer: 3000,
                timerProgressBar: true
            });
        @endif

        @if(session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Terjadi Kesalahan!',
                text: '{{ session('error') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        @if(session('warning'))
            Swal.fire({
                icon: 'warning',
                title: 'Peringatan!',
                text: '{{ session('warning') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        @if(session('info'))
            Swal.fire({
                icon: 'info',
                title: 'Informasi',
                text: '{{ session('info') }}',
                confirmButtonColor: '#667eea'
            });        @endif
    </script>
    
    <!-- Page Transitions JavaScript -->
    <script src="{{ asset('js/page-transitions.js') }}"></script>
    
    @stack('scripts')
</body>
</html>
