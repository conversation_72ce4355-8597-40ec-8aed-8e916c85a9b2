<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\ReportController;
use Illuminate\Http\Request;

class TestYearlyApi extends Command
{
    protected $signature = 'test:yearly-api {year=2025}';
    protected $description = 'Test yearly API with real data';

    public function handle()
    {
        $year = $this->argument('year');
        
        // <PERSON><PERSON> as user 2
        auth()->loginUsingId(2);
        
        // Create request
        $request = new Request([
            'year' => $year
        ]);
        
        // Test the API
        $controller = new ReportController();
        $response = $controller->apiYearly($request);
        
        $this->info("Yearly API Response for $year:");
        $this->line($response->getContent());
    }
}
