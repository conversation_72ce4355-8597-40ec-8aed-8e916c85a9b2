<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Account;
use App\Models\Category;
use Carbon\Carbon;

class TransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::first();
        
        if (!$user) {
            $this->command->error('No user found. Please create a user first.');
            return;
        }

        $accounts = Account::where('user_id', $user->id)->get();
        $categories = Category::where('user_id', $user->id)->get();
        
        if ($accounts->isEmpty() || $categories->isEmpty()) {
            $this->command->error('No accounts or categories found. Please seed them first.');
            return;
        }

        $cashAccount = $accounts->where('type', 'cash')->first();
        $bankAccount = $accounts->where('type', 'bank')->first();
        $ewalletAccount = $accounts->where('type', 'e_wallet')->first();
        
        $foodCategory = $categories->where('name', '<PERSON><PERSON><PERSON> & <PERSON>')->first();
        $transportCategory = $categories->where('name', 'Transportasi')->first();
        $salaryCategory = $categories->where('name', 'Gaji')->first();
        $freelanceCategory = $categories->where('name', 'Freelance')->first();

        // Sample transactions for the last 30 days
        $transactions = [
            // Income transactions
            [
                'account_id' => $bankAccount->id,
                'category_id' => $salaryCategory->id,
                'type' => 'income',
                'amount' => 8500000,
                'title' => 'Gaji Bulan Juni 2025',
                'description' => 'Gaji bulanan dari PT. Tech Indonesia',
                'transaction_date' => Carbon::now()->startOfMonth(),
                'payment_method' => 'bank_transfer',
                'reference_number' => 'SAL-202506-001'
            ],
            [
                'account_id' => $bankAccount->id,
                'category_id' => $freelanceCategory->id,
                'type' => 'income',
                'amount' => 2500000,
                'title' => 'Project Website E-Commerce',
                'description' => 'Pembayaran project dari PT. Retail Online',
                'transaction_date' => Carbon::now()->subDays(15),
                'payment_method' => 'bank_transfer',
                'reference_number' => 'FRL-202506-001'
            ],
            [
                'account_id' => $bankAccount->id,
                'category_id' => $freelanceCategory->id,
                'type' => 'income',
                'amount' => 1200000,
                'title' => 'Konsultasi IT',
                'description' => 'Fee konsultasi sistem untuk startup',
                'transaction_date' => Carbon::now()->subDays(8),
                'payment_method' => 'bank_transfer'
            ],

            // Expense transactions
            [
                'account_id' => $cashAccount->id,
                'category_id' => $foodCategory->id,
                'type' => 'expense',
                'amount' => 45000,
                'title' => 'Makan Siang',
                'description' => 'Nasi gudeg + es teh di warung mbak Sri',
                'transaction_date' => Carbon::now()->subDays(1),
                'payment_method' => 'cash',
                'location' => 'Warung Gudeg Mbak Sri, Yogyakarta'
            ],
            [
                'account_id' => $ewalletAccount->id,
                'category_id' => $transportCategory->id,
                'type' => 'expense',
                'amount' => 25000,
                'title' => 'Gojek ke Kantor',
                'description' => 'Ojek online dari rumah ke kantor',
                'transaction_date' => Carbon::now()->subDays(1),
                'payment_method' => 'e_wallet',
                'reference_number' => 'GO-************'
            ],
            [
                'account_id' => $cashAccount->id,
                'category_id' => $foodCategory->id,
                'type' => 'expense',
                'amount' => 15000,
                'title' => 'Kopi Pagi',
                'description' => 'Kopi susu + roti bakar',
                'transaction_date' => Carbon::now()->subDays(2),
                'payment_method' => 'cash'
            ],
            [
                'account_id' => $ewalletAccount->id,
                'category_id' => $transportCategory->id,
                'type' => 'expense',
                'amount' => 30000,
                'title' => 'Grab ke Mall',
                'description' => 'Pergi ke mall weekend',
                'transaction_date' => Carbon::now()->subDays(2),
                'payment_method' => 'e_wallet'
            ],
            [
                'account_id' => $bankAccount->id,
                'category_id' => $categories->where('name', 'Belanja')->first()->id,
                'type' => 'expense',
                'amount' => 250000,
                'title' => 'Belanja Groceries',
                'description' => 'Belanja bulanan di supermarket',
                'transaction_date' => Carbon::now()->subDays(3),
                'payment_method' => 'debit_card',
                'location' => 'Superindo Malioboro'
            ],
            [
                'account_id' => $cashAccount->id,
                'category_id' => $foodCategory->id,
                'type' => 'expense',
                'amount' => 35000,
                'title' => 'Makan Malam',
                'description' => 'Ayam geprek + es jeruk',
                'transaction_date' => Carbon::now()->subDays(3),
                'payment_method' => 'cash'
            ],
            [
                'account_id' => $ewalletAccount->id,
                'category_id' => $categories->where('name', 'Hiburan')->first()->id,
                'type' => 'expense',
                'amount' => 75000,
                'title' => 'Tiket Bioskop',
                'description' => 'Nonton film terbaru di XXI',
                'transaction_date' => Carbon::now()->subDays(4),
                'payment_method' => 'e_wallet',
                'location' => 'XXI Malioboro Mall'
            ]
        ];

        // Generate more random transactions for the past month
        $paymentMethods = ['cash', 'debit_card', 'e_wallet', 'bank_transfer'];
        $expenseCategories = $categories->where('type', 'expense');
        
        for ($i = 5; $i <= 30; $i++) {
            // Random expense
            $category = $expenseCategories->random();
            $account = $accounts->where('include_in_total', true)->random();
            
            $amount = match($category->name) {
                'Makanan & Minuman' => rand(15000, 100000),
                'Transportasi' => rand(10000, 50000),
                'Belanja' => rand(50000, 500000),
                'Hiburan' => rand(25000, 200000),
                'Kesehatan' => rand(50000, 300000),
                'Tagihan & Utilities' => rand(100000, 800000),
                default => rand(20000, 150000)
            };

            $transactions[] = [
                'account_id' => $account->id,
                'category_id' => $category->id,
                'type' => 'expense',
                'amount' => $amount,
                'title' => $this->generateTransactionTitle($category->name),
                'description' => 'Transaksi ' . $category->name . ' pada ' . Carbon::now()->subDays($i)->format('d M Y'),
                'transaction_date' => Carbon::now()->subDays($i),
                'payment_method' => $paymentMethods[array_rand($paymentMethods)]
            ];
        }

        // Create transactions
        foreach ($transactions as $transactionData) {
            Transaction::create([
                'user_id' => $user->id,
                'account_id' => $transactionData['account_id'],
                'category_id' => $transactionData['category_id'],
                'type' => $transactionData['type'],
                'amount' => $transactionData['amount'],
                'title' => $transactionData['title'],
                'description' => $transactionData['description'],
                'transaction_date' => $transactionData['transaction_date'],
                'payment_method' => $transactionData['payment_method'],
                'reference_number' => $transactionData['reference_number'] ?? null,
                'location' => $transactionData['location'] ?? null,
                'status' => 'completed'
            ]);
        }

        $this->command->info('Sample transactions created successfully!');
    }

    private function generateTransactionTitle($categoryName): string
    {
        $titles = [
            'Makanan & Minuman' => [
                'Makan Siang', 'Kopi Pagi', 'Makan Malam', 'Snack Sore', 'Minum Boba',
                'Sarapan', 'Makan di Resto', 'Beli Groceries', 'Jajan Pasar', 'Warung Kopi'
            ],
            'Transportasi' => [
                'Gojek', 'Grab Car', 'Bensin Motor', 'Parkir', 'Ojek Pangkalan',
                'Bus Trans', 'Taxi', 'Tol', 'Grab Bike', 'Kereta'
            ],
            'Belanja' => [
                'Belanja Bulanan', 'Beli Baju', 'Elektronik', 'Alat Tulis', 'Keperluan Rumah',
                'Kosmetik', 'Obat-obatan', 'Aksesoris', 'Gadget', 'Peralatan'
            ],
            'Hiburan' => [
                'Bioskop', 'Karaoke', 'Game Online', 'Streaming Netflix', 'Konser',
                'Wisata', 'Hobby', 'Buku', 'Majalah', 'Event'
            ],
            'Kesehatan' => [
                'Dokter', 'Obat', 'Vitamin', 'Medical Check Up', 'Rumah Sakit',
                'Klinik', 'Asuransi Kesehatan', 'Terapi', 'Gym', 'Suplemen'
            ],
            'Tagihan & Utilities' => [
                'Listrik PLN', 'Air PDAM', 'Internet', 'Telepon', 'TV Kabel',
                'Asuransi', 'Pajak', 'Iuran', 'Maintenance', 'Service'
            ]
        ];

        $categoryTitles = $titles[$categoryName] ?? ['Transaksi ' . $categoryName];
        return $categoryTitles[array_rand($categoryTitles)];
    }
}
