<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class ThemeChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user;
    public $theme;
    public $themeDisplay;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, string $theme)
    {
        $this->user = $user;
        $this->theme = $theme;
        
        // Set display name untuk tema
        $this->themeDisplay = match($theme) {
            'light' => 'Terang',
            'solarized-dark' => 'Solarized Dark', 
            'synth-wave' => 'Synth Wave',
            default => 'Terang'
        };
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->user->id . '.theme'),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'theme' => $this->theme,
            'theme_display' => $this->themeDisplay,
            'user_id' => $this->user->id,
            'timestamp' => now()->toISOString()
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'theme.changed';
    }
}
