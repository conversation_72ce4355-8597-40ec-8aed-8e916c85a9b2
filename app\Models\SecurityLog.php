<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SecurityLog extends Model
{
    protected $fillable = [
        'user_id',
        'event_type',
        'ip_address',
        'user_agent',
        'details',
        'risk_level',
        'is_blocked',
        'description',
    ];

    protected function casts(): array
    {
        return [
            'details' => 'array',
            'is_blocked' => 'boolean',
        ];
    }

    /**
     * User relationship
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Log security event
     */
    public static function logEvent(
        ?int $userId,
        string $eventType,
        string $description = '',
        string $riskLevel = 'low',
        array $details = []
    ): self {
        return self::create([
            'user_id' => $userId,
            'event_type' => $eventType,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'details' => $details,
            'risk_level' => $riskLevel,
            'description' => $description,
        ]);
    }
}
