<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use PragmaRX\Google2FA\Google2FA;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'username',
        'full_name',
        'email',
        'phone',
        'gender',
        'birth_date',
        'birth_date', // Add alias for birth_date
        'address',
        'city',
        'postal_code',
        'country',
        'avatar',
        'bio', // Add bio field
        'password',
        'is_active',
        'timezone',
        'language',
        'theme',
        'currency',
        'date_format',
        'time_format',
        'notification_enabled', // Add cast for notification_enabled
        'notification_settings',
        'last_backup_at',
        'current_session_id',
        'last_login_at',
        'last_login_ip',
        'login_attempts',
        'locked_until',
        'password_changed_at',
        'password_expires_at',
        'two_factor_enabled',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
        'backup_codes',
        'profile_photo_path',
        'preferences',
        'security_settings',
        'notification_preferences'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'verification_token',
        'login_attempts',
        'current_session_id',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'backup_codes',
        'security_questions',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'birth_date' => 'date',
            'birth_date' => 'date', // Add cast for birth_date
            'locked_until' => 'datetime',
            'last_login_at' => 'datetime',
            'last_activity' => 'datetime',
            'password_changed_at' => 'datetime',
            'two_factor_confirmed_at' => 'datetime',
            'two_factor_enabled_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'deactivated_at' => 'datetime',
            'last_backup_at' => 'datetime',
            'is_active' => 'boolean',
            'two_factor_enabled' => 'boolean',
            'notification_enabled' => 'boolean', // Add cast for notification_enabled
            'security_questions' => 'array',
            'backup_codes' => 'array',
            'two_factor_recovery_codes' => 'array',
            'notification_settings' => 'array',
        ];
    }
    
    /**
     * Check if user account is locked
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }
    
    /**
     * Lock user account
     */
    public function lockAccount(int $minutes = 30): void
    {
        $this->update([
            'locked_until' => now()->addMinutes($minutes),
            'login_attempts' => 0
        ]);
        
        SecurityLog::logEvent(
            $this->id,
            'account_locked',
            "Account locked due to failed login attempts",
            'high',
            ['locked_until' => $this->locked_until, 'reason' => 'failed_login_attempts']
        );
    }
    
    /**
     * Increment login attempts
     */
    public function incrementLoginAttempts(): void
    {
        $this->increment('login_attempts');
        $this->increment('failed_login_attempts');
        
        if ($this->login_attempts >= 3) {
            $this->lockAccount();
        }
    }
    
    /**
     * Reset login attempts
     */
    public function resetLoginAttempts(): void
    {
        $this->update([
            'login_attempts' => 0,
            'locked_until' => null,
            'last_login_at' => now(),
            'last_login_ip' => request()->ip(),
            'last_activity' => now(),
        ]);
    }
    
    /**
     * Security logs relationship
     */
    public function securityLogs()
    {
        return $this->hasMany(SecurityLog::class);
    }
    
    /**
     * Get the user's notification settings.
     */
    public function notificationSettings()
    {
        return $this->hasOne(NotificationSetting::class);
    }

    /**
     * Get or create notification settings for the user.
     */
    public function getNotificationSettings()
    {
        if (!$this->notificationSettings) {
            return $this->notificationSettings()->create([]);
        }
        
        return $this->notificationSettings;
    }
    
    /**
     * Check if password needs to be changed (older than 90 days)
     */
    public function passwordNeedsChange(): bool
    {
        // For new users (created less than 1 day ago), don't force password change
        if ($this->created_at && $this->created_at->diffInDays(now()) < 1) {
            return false;
        }
        
        if (!$this->password_changed_at) {
            return false; // Don't force change for users without password_changed_at
        }
        
        return $this->password_changed_at->diffInDays(now()) > 90;
    }
    
    /**
     * Update session ID for double login prevention
     */
    public function updateSessionId(string $sessionId): void
    {
        $this->update([
            'current_session_id' => $sessionId,
            'last_activity' => now(),
        ]);
    }
    
    /**
     * Check if user has active session
     */
    public function hasActiveSession(): bool
    {
        return !empty($this->current_session_id);
    }
    
    /**
     * Clear session
     */
    public function clearSession(): void
    {
        $this->update([
            'current_session_id' => null,
        ]);
    }
    
    /**
     * 2FA Methods
     */
    
    /**
     * Generate a new 2FA secret key
     */
    public function generate2FASecret(): string
    {
        $google2fa = new Google2FA();
        $secret = $google2fa->generateSecretKey();
        
        $this->update([
            'two_factor_secret' => encrypt($secret),
            'two_factor_confirmed_at' => null,
        ]);
        
        return $secret;
    }
    
    /**
     * Get decrypted 2FA secret
     */
    public function get2FASecret(): ?string
    {
        if (!$this->two_factor_secret) {
            return null;
        }
        
        try {
            return decrypt($this->two_factor_secret);
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Generate QR Code URL for 2FA setup
     */
    public function get2FAQRCodeUrl(string $appName = 'MyMoney'): ?string
    {
        $secret = $this->get2FASecret();
        if (!$secret) {
            return null;
        }
        
        $google2fa = new Google2FA();
        return $google2fa->getQRCodeUrl(
            $appName,
            $this->email,
            $secret
        );
    }
    
    /**
     * Verify 2FA code
     */
    public function verify2FACode(string $code): bool
    {
        $secret = $this->get2FASecret();
        if (!$secret) {
            return false;
        }
        
        $google2fa = new Google2FA();
        return $google2fa->verifyKey($secret, $code);
    }
    
    /**
     * Enable 2FA after successful verification
     */
    public function enable2FA(): void
    {
        \Log::info('Enable2FA called for user: ' . $this->id);
        
        $result = $this->update([
            'two_factor_enabled' => true,
            'two_factor_confirmed_at' => now(),
        ]);
        
        \Log::info('Update result: ' . ($result ? 'success' : 'failed'));
        \Log::info('After update - two_factor_confirmed_at: ' . $this->fresh()->two_factor_confirmed_at);
        
        SecurityLog::logEvent(
            $this->id,
            '2fa_enabled',
            'Two-Factor Authentication enabled',
            'medium',
            ['method' => 'TOTP']
        );
        
        // Create notification for 2FA activation
        \App\Services\NotificationService::create2FAAlert($this->id, [
            'message' => 'Two-Factor Authentication berhasil diaktifkan untuk akun Anda. Keamanan akun meningkat!',
        ]);
    }
    
    /**
     * Disable 2FA
     */
    public function disable2FA(): void
    {
        $this->update([
            'two_factor_enabled' => false,
            'two_factor_secret' => null,
            'two_factor_confirmed_at' => null,
        ]);
        
        SecurityLog::logEvent(
            $this->id,
            '2fa_disabled',
            'Two-Factor Authentication disabled',
            'medium'
        );
    }
    
    /**
     * Check if 2FA is properly set up and enabled
     */
    public function has2FAEnabled(): bool
    {
        return $this->two_factor_enabled 
            && !empty($this->two_factor_secret) 
            && !empty($this->two_factor_confirmed_at);
    }
    
    /**
     * Generate new backup codes (improved version)
     */
    public function generateBackupCodes(): array
    {
        $codes = [];
        for ($i = 0; $i < 10; $i++) {
            // Generate more secure backup codes
            $codes[] = strtoupper(substr(bin2hex(random_bytes(4)), 0, 8));
        }
        
        $this->update(['backup_codes' => $codes]);
        
        SecurityLog::logEvent(
            $this->id,
            'backup_codes_generated',
            'New backup codes generated',
            'low'
        );
        
        return $codes;
    }
    
    /**
     * Use a backup code for 2FA
     */
    public function useBackupCode(string $code): bool
    {
        $codes = $this->backup_codes ?? [];
        $code = strtoupper(trim($code));
        
        if (in_array($code, $codes)) {
            // Remove used code
            $remainingCodes = array_values(array_diff($codes, [$code]));
            $this->update(['backup_codes' => $remainingCodes]);
            
            SecurityLog::logEvent(
                $this->id,
                'backup_code_used',
                'Backup code used for 2FA verification',
                'medium',
                ['remaining_codes' => count($remainingCodes)]
            );
            
            return true;
        }
        
        return false;
    }
    
    // ===== NOTIFICATION RELATIONSHIPS =====
    
    /**
     * Get all notifications for this user
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }
    
    /**
     * Get unread notifications
     */
    public function unreadNotifications()
    {
        return $this->notifications()->whereNull('read_at');
    }
    
    /**
     * Get important notifications
     */
    public function importantNotifications()
    {
        return $this->notifications()->where('is_important', true);
    }
    
    /**
     * Get recent notifications (last 7 days)
     */
    public function recentNotifications($days = 7)
    {
        return $this->notifications()
            ->where('created_at', '>=', now()->subDays($days))
            ->orderBy('created_at', 'desc');
    }
    
    /**
     * Get notification counts
     */
    public function getNotificationCounts()
    {
        return [
            'total' => $this->notifications()->count(),
            'unread' => $this->unreadNotifications()->count(),
            'important' => $this->importantNotifications()->whereNull('read_at')->count(),
        ];
    }
    
    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        // Use Laravel's default notification system for password reset
        $this->notify(new \Illuminate\Auth\Notifications\ResetPassword($token));
    }

    /**
     * Create custom notification for this user
     */
    public function createNotification($data)
    {
        return Notification::createForUser($this->id, $data);
    }
    
    /**
     * Mark all notifications as read
     */
    public function markAllNotificationsAsRead()
    {
        return $this->unreadNotifications()->update(['read_at' => now()]);
    }
}
