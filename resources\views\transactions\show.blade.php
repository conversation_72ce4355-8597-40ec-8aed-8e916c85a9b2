@extends('layouts.dashboard')

@section('title', 'Detail Transaksi')

@section('page-title', '�️ Detail Transaksi')

@push('styles')
<style>
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    .detail-header {
        background: rgba(23, 162, 184, 0.1);
        backdrop-filter: blur(10px);
        color: #0c5460;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        border: 2px solid rgba(23, 162, 184, 0.3);
    }
    
    /* Synth-wave detail header */
    body[data-theme="synth-wave"] .detail-header {
        background: rgba(26, 26, 26, 0.9) !important;
        border: 1px solid #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.3) !important;
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .detail-header h2 {
        color: #ff00ff !important;
        text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
    }
    
    body[data-theme="synth-wave"] .detail-header p {
        color: #00ffff !important;
    }
    
    .detail-card {
        background: #fdf6e3;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    /* Synth-wave detail card */
    body[data-theme="synth-wave"] .detail-card {
        background: #1a1a1a !important;
        border: 1px solid #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.2) !important;
    }
    
    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
    }
    
    .detail-item {
        background: #f8f4f0;
        padding: 20px;
        border-radius: 15px;
        border-left: 4px solid #17a2b8;
    }
    
    /* Synth-wave detail items */
    body[data-theme="synth-wave"] .detail-item {
        background: #2a2a2a !important;
        border-left: 4px solid #00ffff !important;
        color: #ffffff !important;
    }
    
    .detail-label {
        font-weight: 600;
        color: #666;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
    }
    
    /* Synth-wave detail labels */
    body[data-theme="synth-wave"] .detail-label {
        color: #ff00ff !important;
    }
    
    .detail-label i {
        color: #17a2b8;
    }
    
    body[data-theme="synth-wave"] .detail-label i {
        color: #00ffff !important;
    }
    
    .detail-value {
        font-size: 1.1rem;
        color: #333;
        font-weight: 500;
    }
    
    body[data-theme="synth-wave"] .detail-value {
        color: #ffffff !important;
    }
    
    .transaction-type {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .type-income {
        background: rgba(40, 167, 69, 0.1);
        color: #155724;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }
    
    .type-expense {
        background: rgba(220, 53, 69, 0.1);
        color: #721c24;
        border: 1px solid rgba(220, 53, 69, 0.3);
    }
    
    .type-transfer {
        background: rgba(23, 162, 184, 0.1);
        color: #0c5460;
        border: 1px solid rgba(23, 162, 184, 0.3);
    }
    
    .amount-display {
        font-size: 2rem;
        font-weight: 700;
        text-align: center;
        padding: 20px;
        border-radius: 15px;
        margin: 20px 0;
    }
    
    .amount-income {
        background: rgba(40, 167, 69, 0.1);
        color: #155724;
        border: 2px solid rgba(40, 167, 69, 0.3);
    }
    
    body[data-theme="synth-wave"] .amount-income {
        background: rgba(0, 255, 102, 0.1) !important;
        color: #00ff66 !important;
        border: 2px solid #00ff66 !important;
        box-shadow: 0 0 20px rgba(0, 255, 102, 0.3) !important;
    }
    
    .amount-expense {
        background: rgba(220, 53, 69, 0.1);
        color: #721c24;
        border: 2px solid rgba(220, 53, 69, 0.3);
    }
    
    body[data-theme="synth-wave"] .amount-expense {
        background: rgba(255, 0, 102, 0.1) !important;
        color: #ff0066 !important;
        border: 2px solid #ff0066 !important;
        box-shadow: 0 0 20px rgba(255, 0, 102, 0.3) !important;
    }
    
    .amount-transfer {
        background: rgba(23, 162, 184, 0.1);
        color: #0c5460;
        border: 2px solid rgba(23, 162, 184, 0.3);
    }
    
    body[data-theme="synth-wave"] .amount-transfer {
        background: rgba(0, 255, 255, 0.1) !important;
        color: #00ffff !important;
        border: 2px solid #00ffff !important;
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.3) !important;
    }
    
    .btn-group {
        display: flex;
        gap: 25px;
        margin-top: 30px;
        flex-wrap: wrap;
    }
    
    .btn {
        background: none !important;
        border: none !important;
        padding: 8px 0 !important;
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        font-size: 1rem;
        position: relative;
        box-shadow: none !important;
        border-radius: 0 !important;
    }
    
    .btn::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        transition: all 0.3s ease;
    }
    
    .btn:hover::after {
        width: 100%;
    }
    
    .btn-primary {
        color: #17a2b8;
    }
    
    .btn-primary::after {
        background: linear-gradient(90deg, #17a2b8, #138496);
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        color: #17a2b8;
        text-decoration: none;
    }
    
    body[data-theme="synth-wave"] .btn-primary {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .btn-primary::after {
        background: linear-gradient(90deg, #00ffff, #0099cc) !important;
    }
    
    body[data-theme="synth-wave"] .btn-primary:hover {
        text-shadow: 0 0 10px #00ffff !important;
        color: #00ffff !important;
        transform: translateY(-2px);
    }
    
    body[data-theme="synth-wave"] .btn-primary:hover::after {
        box-shadow: 0 0 10px #00ffff !important;
    }
    
    .btn-warning {
        color: #ffc107;
    }
    
    .btn-warning::after {
        background: linear-gradient(90deg, #ffc107, #e0a800);
    }
    
    .btn-warning:hover {
        transform: translateY(-2px);
        color: #ffc107;
        text-decoration: none;
    }
    
    body[data-theme="synth-wave"] .btn-warning {
        color: #ffff00 !important;
    }
    
    body[data-theme="synth-wave"] .btn-warning::after {
        background: linear-gradient(90deg, #ffff00, #ff9900) !important;
    }
    
    body[data-theme="synth-wave"] .btn-warning:hover {
        text-shadow: 0 0 10px #ffff00 !important;
        color: #ffff00 !important;
        transform: translateY(-2px);
    }
    
    body[data-theme="synth-wave"] .btn-warning:hover::after {
        box-shadow: 0 0 10px #ffff00 !important;
    }
    
    .btn-danger {
        color: #dc3545;
    }
    
    .btn-danger::after {
        background: linear-gradient(90deg, #dc3545, #c82333);
    }
    
    .btn-danger:hover {
        transform: translateY(-2px);
        color: #dc3545;
        text-decoration: none;
    }
    
    body[data-theme="synth-wave"] .btn-danger {
        color: #ff0066 !important;
    }
    
    body[data-theme="synth-wave"] .btn-danger::after {
        background: linear-gradient(90deg, #ff0066, #cc0033) !important;
    }
    
    body[data-theme="synth-wave"] .btn-danger:hover {
        text-shadow: 0 0 10px #ff0066 !important;
        color: #ff0066 !important;
        transform: translateY(-2px);
    }
    
    body[data-theme="synth-wave"] .btn-danger:hover::after {
        box-shadow: 0 0 10px #ff0066 !important;
    }
    
    .btn-secondary {
        color: #6c757d;
    }
    
    .btn-secondary::after {
        background: linear-gradient(90deg, #6c757d, #5a6268);
    }
    
    .btn-secondary:hover {
        transform: translateY(-2px);
        color: #6c757d;
        text-decoration: none;
    }
    
    body[data-theme="synth-wave"] .btn-secondary {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .btn-secondary::after {
        background: linear-gradient(90deg, #ff00ff, #00ffff) !important;
    }
    
    body[data-theme="synth-wave"] .btn-secondary:hover {
        text-shadow: 0 0 10px #ff00ff !important;
        color: #cccccc !important;
        transform: translateY(-2px);
    }
    
    body[data-theme="synth-wave"] .btn-secondary:hover::after {
        box-shadow: 0 0 10px #ff00ff !important;
    }
    
    .btn i {
        transition: all 0.3s ease;
    }
    
    .btn:hover i {
        transform: scale(1.1);
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .btn-group {
            flex-direction: column;
            gap: 15px;
            align-items: flex-start;
        }
        
        .btn {
            font-size: 0.95rem;
        }
    }
    
    .attachments-section {
        margin-top: 30px;
    }
    
    .attachment-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px 15px;
        background: #f8f4f0;
        border-radius: 8px;
        margin-bottom: 10px;
    }
    
    body[data-theme="synth-wave"] .attachment-item {
        background: #2a2a2a !important;
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .attachment-item a {
        color: #00ffff !important;
    }
    
    .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 10px;
    }
    
    .tag {
        background: #17a2b8;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    body[data-theme="synth-wave"] .tag {
        background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
        box-shadow: 0 0 10px rgba(255, 0, 255, 0.3) !important;
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <div class="detail-header">
        <h2><i class="fas fa-eye"></i> Detail Transaksi</h2>
        <p>Informasi lengkap transaksi yang dipilih</p>
    </div>

    <div class="detail-card">
        <!-- Transaction Amount Display -->
        <div class="amount-display amount-{{ $transaction->type }}">
            {{ $transaction->type === 'expense' ? '-' : '' }}{{ formatRupiah($transaction->amount) }}
        </div>

        <!-- Basic Information -->
        <div class="detail-grid">
            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-file-alt"></i> Judul Transaksi
                </div>
                <div class="detail-value">{{ $transaction->title }}</div>
            </div>

            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-tags"></i> Jenis Transaksi
                </div>
                <div class="detail-value">
                    <span class="transaction-type type-{{ $transaction->type }}">
                        @if($transaction->type === 'income')
                            <i class="fas fa-arrow-up"></i> Pemasukan
                        @elseif($transaction->type === 'expense')
                            <i class="fas fa-arrow-down"></i> Pengeluaran
                        @else
                            <i class="fas fa-exchange-alt"></i> Transfer
                        @endif
                    </span>
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-calendar"></i> Tanggal Transaksi
                </div>
                <div class="detail-value">{{ \Carbon\Carbon::parse($transaction->transaction_date)->isoFormat('dddd, D MMMM Y') }}</div>
            </div>

            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-university"></i> Akun
                </div>
                <div class="detail-value">{{ $transaction->account->name }}</div>
            </div>

            @if($transaction->toAccount)
            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-arrow-right"></i> Ke Akun
                </div>
                <div class="detail-value">{{ $transaction->toAccount->name }}</div>
            </div>
            @endif

            @if($transaction->category)
            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-folder"></i> Kategori
                </div>
                <div class="detail-value">{{ $transaction->category->name }}</div>
            </div>
            @endif

            @if($transaction->payment_method)
            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-credit-card"></i> Metode Pembayaran
                </div>
                <div class="detail-value">
                    @switch($transaction->payment_method)
                        @case('cash') Tunai @break
                        @case('debit_card') Kartu Debit @break
                        @case('credit_card') Kartu Kredit @break
                        @case('bank_transfer') Transfer Bank @break
                        @case('e_wallet') E-Wallet @break
                        @default {{ $transaction->payment_method }}
                    @endswitch
                </div>
            </div>
            @endif

            @if($transaction->reference_number)
            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-hashtag"></i> Nomor Referensi
                </div>
                <div class="detail-value">{{ $transaction->reference_number }}</div>
            </div>
            @endif

            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-clock"></i> Waktu Dibuat
                </div>
                <div class="detail-value">{{ $transaction->created_at->isoFormat('dddd, D MMMM Y - HH:mm') }}</div>
            </div>

            @if($transaction->updated_at != $transaction->created_at)
            <div class="detail-item">
                <div class="detail-label">
                    <i class="fas fa-edit"></i> Terakhir Diubah
                </div>
                <div class="detail-value">{{ $transaction->updated_at->isoFormat('dddd, D MMMM Y - HH:mm') }}</div>
            </div>
            @endif
        </div>

        @if($transaction->description)
        <div class="detail-item" style="margin-top: 30px;">
            <div class="detail-label">
                <i class="fas fa-comment"></i> Deskripsi
            </div>
            <div class="detail-value">{{ $transaction->description }}</div>
        </div>
        @endif

        @if($transaction->tags && count($transaction->tags) > 0)
        <div class="detail-item" style="margin-top: 30px;">
            <div class="detail-label">
                <i class="fas fa-tag"></i> Tags
            </div>
            <div class="tags-container">
                @foreach($transaction->tags as $tag)
                    <span class="tag">{{ $tag }}</span>
                @endforeach
            </div>
        </div>
        @endif

        @if($transaction->location)
        <div class="detail-item" style="margin-top: 30px;">
            <div class="detail-label">
                <i class="fas fa-map-marker-alt"></i> Lokasi
            </div>
            <div class="detail-value">{{ $transaction->location }}</div>
        </div>
        @endif

        @if($transaction->attachments && count($transaction->attachments) > 0)
        <div class="attachments-section">
            <div class="detail-label">
                <i class="fas fa-paperclip"></i> Lampiran
            </div>
            @foreach($transaction->attachments as $attachment)
                <div class="attachment-item">
                    <i class="fas fa-file"></i>
                    <a href="{{ Storage::url($attachment) }}" target="_blank">{{ basename($attachment) }}</a>
                </div>
            @endforeach
        </div>
        @endif

        <!-- Action Buttons -->
        <div class="btn-group">
            <a href="{{ route('transactions.edit', $transaction) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <button class="btn btn-danger" onclick="deleteTransaction({{ $transaction->id }})">
                <i class="fas fa-trash"></i> Hapus
            </button>
            <a href="{{ route('transactions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<script>
function deleteTransaction(id) {
    Swal.fire({
        title: 'Hapus Transaksi?',
        text: 'Apakah Anda yakin ingin menghapus transaksi ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/transactions/${id}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Berhasil!', 'Transaksi telah dihapus.', 'success')
                        .then(() => {
                            window.location.href = '/transactions';
                        });
                } else {
                    Swal.fire('Gagal!', 'Gagal menghapus transaksi.', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Terjadi kesalahan saat menghapus transaksi.', 'error');
            });
        }
    });
}
</script>
@endsection
