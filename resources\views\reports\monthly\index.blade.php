@extends('layouts.dashboard')

@section('title', 'Laporan Bulanan')

@section('page-title', 'Laporan')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800"><PERSON>por<PERSON> Bulanan</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('reports.index') }}">Laporan</a></li>
                            <li class="breadcrumb-item active">Bulanan</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-success" id="exportBtn">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body py-3">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <select class="form-control" id="monthFilter">
                                <option value="">Semua Bulan</option>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ $i == now()->month ? 'selected' : '' }}>
                                        {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-control" id="yearFilter">
                                @for($year = now()->year; $year >= now()->year - 5; $year--)
                                    <option value="{{ $year }}" {{ $year == now()->year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="categoryFilter">
                                <option value="">Semua Kategori</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="accountFilter">
                                <option value="">Semua Akun</option>
                                @foreach($accounts as $account)
                                    <option value="{{ $account->id }}">{{ $account->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary" id="generateBtn">
                                <i class="fas fa-chart-bar"></i> Generate
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Content -->
    <div id="reportContent" class="d-none">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Total Pemasukan
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalIncome">-</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    Total Pengeluaran
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalExpense">-</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Net Amount
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="netAmount">-</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Total Transaksi
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalTransactions">-</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-list fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Grafik Harian</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="dailyChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Kategori Pengeluaran</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="categoryChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Detail Transaksi</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="transactionTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Tanggal</th>
                                        <th>Kategori</th>
                                        <th>Akun</th>
                                        <th>Deskripsi</th>
                                        <th>Tipe</th>
                                        <th>Jumlah</th>
                                    </tr>
                                </thead>
                                <tbody id="transactionTableBody">
                                    <!-- Data will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empty State -->
    <div id="emptyState" class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-chart-bar fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">Pilih Parameter Laporan</h5>
                    <p class="text-muted">Pilih bulan, tahun, dan filter lainnya untuk melihat laporan bulanan</p>
                    <button class="btn btn-primary" id="autoGenerateBtn">
                        <i class="fas fa-magic"></i> Generate Laporan Bulan Ini
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.transaction-income {
    border-left: 3px solid #1cc88a;
}

.transaction-expense {
    border-left: 3px solid #e74a3b;
}

#dailyChart, #categoryChart {
    max-height: 300px;
}

.table td {
    vertical-align: middle;
}

.amount-income {
    color: #1cc88a;
    font-weight: 600;
}

.amount-expense {
    color: #e74a3b;
    font-weight: 600;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Setup CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let dailyChart = null;
    let categoryChart = null;
    
    // Event handlers
    $('#generateBtn, #autoGenerateBtn').on('click', function() {
        generateReport();
    });

    $('#exportBtn').on('click', function() {
        exportReport();
    });

    function generateReport() {
        const month = $('#monthFilter').val();
        const year = $('#yearFilter').val();
        const categoryId = $('#categoryFilter').val();
        const accountId = $('#accountFilter').val();

        showLoadingState();

        $.ajax({
            url: '{{ route("reports.api.monthly") }}',
            method: 'GET',
            data: {
                month: month,
                year: year,
                category_id: categoryId,
                account_id: accountId
            },
            success: function(response) {
                console.log('API Response:', response); // Debug log
                hideLoadingState();
                displayReport(response);
            },
            error: function(xhr) {
                hideLoadingState();
                console.error('Error generating monthly report:', xhr);
                console.log('XHR Status:', xhr.status);
                console.log('XHR Response:', xhr.responseText);
                alert('Terjadi kesalahan saat membuat laporan: ' + xhr.status);
            }
        });
    }

    function showLoadingState() {
        $('#emptyState').addClass('d-none');
        $('#reportContent').removeClass('d-none');
        $('#reportContent').append(`
            <div class="loading-overlay">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
            </div>
        `);
    }

    function hideLoadingState() {
        $('.loading-overlay').remove();
    }

    function displayReport(data) {
        console.log('Received data:', data);
        console.log('Data summary:', data.summary);
        console.log('Data chart_data:', data.chart_data);
        console.log('Data transactions:', data.transactions);
        
        $('#emptyState').addClass('d-none');
        $('#reportContent').removeClass('d-none');

        // Update summary cards
        updateSummary(data.summary);

        // Update charts
        updateCharts(data.chart_data);

        // Update transaction table
        updateTransactionTable(data.transactions);
    }

    function updateSummary(summary) {
        $('#totalIncome').text(formatCurrency(summary.total_income));
        $('#totalExpense').text(formatCurrency(summary.total_expense));
        $('#netAmount').text(formatCurrency(summary.net_amount));
        $('#totalTransactions').text(summary.transaction_count + ' transaksi');

        // Update net amount color
        const netElement = $('#netAmount');
        netElement.removeClass('text-success text-danger');
        if (summary.net_amount > 0) {
            netElement.addClass('text-success');
        } else if (summary.net_amount < 0) {
            netElement.addClass('text-danger');
        }
    }

    function updateCharts(chartData) {
        // Update daily chart
        if (dailyChart) dailyChart.destroy();
        
        const dailyCtx = document.getElementById('dailyChart').getContext('2d');
        dailyChart = new Chart(dailyCtx, {
            type: 'line',
            data: chartData.daily_line,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + value.toLocaleString('id-ID');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': Rp ' + 
                                       context.parsed.y.toLocaleString('id-ID');
                            }
                        }
                    }
                }
            }
        });

        // Update category chart
        if (categoryChart) categoryChart.destroy();
        
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        
        // Check if there's data for category chart
        if (!chartData.category_pie || !chartData.category_pie.labels || chartData.category_pie.labels.length === 0) {
            // Create empty chart with message
            categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Tidak ada data'],
                    datasets: [{
                        data: [1],
                        backgroundColor: ['#e9ecef'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    }
                }
            });
        } else {
            categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: chartData.category_pie,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': Rp ' + 
                                           context.parsed.toLocaleString('id-ID');
                                }
                            }
                        }
                    }
                }
            });
        }
    }

    function updateTransactionTable(transactions) {
        if (transactions.length === 0) {
            $('#transactionTableBody').html(`
                <tr>
                    <td colspan="6" class="text-center text-muted">
                        Tidak ada transaksi untuk periode ini
                    </td>
                </tr>
            `);
            return;
        }

        let html = '';
        transactions.forEach(transaction => {
            const typeClass = transaction.type === 'income' ? 'transaction-income' : 'transaction-expense';
            const amountClass = transaction.type === 'income' ? 'amount-income' : 'amount-expense';
            const typeText = transaction.type === 'income' ? 'Pemasukan' : 'Pengeluaran';
            const sign = transaction.type === 'income' ? '+' : '-';

            html += `
                <tr class="${typeClass}">
                    <td>${formatDate(transaction.transaction_date)}</td>
                    <td>${transaction.category ? transaction.category.name : '-'}</td>
                    <td>${transaction.account ? transaction.account.name : '-'}</td>
                    <td>${transaction.description || '-'}</td>
                    <td>
                        <span class="badge badge-${transaction.type === 'income' ? 'success' : 'danger'}">
                            ${typeText}
                        </span>
                    </td>
                    <td class="text-right ${amountClass}">
                        ${sign}${formatCurrency(transaction.amount)}
                    </td>
                </tr>
            `;
        });

        $('#transactionTableBody').html(html);

        // Initialize or refresh DataTable if needed
        if ($.fn.DataTable) {
            if ($.fn.DataTable.isDataTable('#transactionTable')) {
                $('#transactionTable').DataTable().destroy();
            }
            
            $('#transactionTable').DataTable({
                "pageLength": 25,
                "order": [[ 0, "desc" ]],
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });
        }
    }

    function exportReport() {
        const month = $('#monthFilter').val();
        const year = $('#yearFilter').val();
        const categoryId = $('#categoryFilter').val();
        const accountId = $('#accountFilter').val();

        const params = new URLSearchParams({
            month: month,
            year: year,
            export: 'excel'
        });

        if (categoryId) params.append('category_id', categoryId);
        if (accountId) params.append('account_id', accountId);

        window.open(`{{ route('reports.api.monthly') }}?${params}`, '_blank');
    }

    function formatCurrency(amount) {
        return 'Rp ' + parseInt(amount).toLocaleString('id-ID');
    }

    function formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
        });
    }

    // Auto-generate current month report on page load if current month is selected
    if ($('#monthFilter').val() == {{ now()->month }} && $('#yearFilter').val() == {{ now()->year }}) {
        generateReport();
    }
});
</script>
@endpush
@endsection
