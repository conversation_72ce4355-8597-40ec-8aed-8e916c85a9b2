<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'account_id',
        'category_id',
        'to_account_id',
        'type',
        'amount',
        'title',
        'description',
        'transaction_date',
        'payment_method',
        'reference_number',
        'attachments',
        'tags',
        'location',
        'exchange_rate',
        'currency',
        'is_recurring',
        'recurring_transaction_id',
        'status',
        'processed_at',
        'metadata'
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'attachments' => 'array',
        'tags' => 'array',
        'metadata' => 'array',
        'is_recurring' => 'boolean',
        'processed_at' => 'datetime'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function toAccount(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'to_account_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function recurringTransaction(): BelongsTo
    {
        return $this->belongsTo(RecurringTransaction::class);
    }

    // Scopes
    public function scopeIncomes($query)
    {
        return $query->where('type', 'income');
    }

    public function scopeExpenses($query)
    {
        return $query->where('type', 'expense');
    }

    public function scopeTransfers($query)
    {
        return $query->where('type', 'transfer');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForAccount($query, $accountId)
    {
        return $query->where('account_id', $accountId);
    }

    public function scopeForCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereYear('transaction_date', now()->year)
                    ->whereMonth('transaction_date', now()->month);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('transaction_date', now()->year);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Accessors
    public function getFormattedAmountAttribute()
    {
        return 'Rp ' . number_format($this->amount, 0, ',', '.');
    }

    public function getFormattedDateAttribute()
    {
        return $this->transaction_date->format('d M Y');
    }

    public function getTypeColorAttribute()
    {
        return match($this->type) {
            'income' => 'success',
            'expense' => 'danger',
            'transfer' => 'info',
            default => 'secondary'
        };
    }

    public function getTypeIconAttribute()
    {
        return match($this->type) {
            'income' => 'fas fa-arrow-up',
            'expense' => 'fas fa-arrow-down',
            'transfer' => 'fas fa-exchange-alt',
            default => 'fas fa-circle'
        };
    }

    // Helper methods
    public function isIncome(): bool
    {
        return $this->type === 'income';
    }

    public function isExpense(): bool
    {
        return $this->type === 'expense';
    }

    public function isTransfer(): bool
    {
        return $this->type === 'transfer';
    }

    public function hasAttachments(): bool
    {
        return !empty($this->attachments);
    }

    public function hasTags(): bool
    {
        return !empty($this->tags);
    }

    public static function getTotalByType($userId, $type, $startDate = null, $endDate = null)
    {
        $query = static::forUser($userId)->where('type', $type)->completed();
        
        if ($startDate && $endDate) {
            $query->inDateRange($startDate, $endDate);
        }
        
        return $query->sum('amount');
    }

    public static function getMonthlyBalance($userId, $year = null, $month = null)
    {
        $year = $year ?? now()->year;
        $month = $month ?? now()->month;
        
        $income = static::forUser($userId)
            ->incomes()
            ->completed()
            ->whereYear('transaction_date', $year)
            ->whereMonth('transaction_date', $month)
            ->sum('amount');
            
        $expense = static::forUser($userId)
            ->expenses()
            ->completed()
            ->whereYear('transaction_date', $year)
            ->whereMonth('transaction_date', $month)
            ->sum('amount');
            
        return $income - $expense;
    }

    protected static function booted()
    {
        // Broadcast dashboard update when transaction is created, updated, or deleted
        static::created(function ($transaction) {
            static::broadcastDashboardUpdate($transaction->user_id);
            static::broadcastNewTransaction($transaction);
        });
        
        static::updated(function ($transaction) {
            static::broadcastDashboardUpdate($transaction->user_id);
        });
        
        static::deleted(function ($transaction) {
            static::broadcastDashboardUpdate($transaction->user_id);
        });
    }
    
    protected static function broadcastDashboardUpdate($userId)
    {
        try {
            $dashboardService = app(\App\Services\DashboardRealtimeService::class);
            $stats = $dashboardService->getDashboardStats($userId);
            $dashboardService->broadcastStatsUpdate($stats);
        } catch (\Exception $e) {
            \Log::error('Failed to broadcast dashboard update: ' . $e->getMessage());
        }
    }
    
    protected static function broadcastNewTransaction($transaction)
    {
        try {
            $dashboardService = app(\App\Services\DashboardRealtimeService::class);
            $dashboardService->broadcastNewTransaction([
                'id' => $transaction->id,
                'title' => $transaction->title,
                'amount' => $transaction->amount,
                'type' => $transaction->type,
                'category' => $transaction->category ? $transaction->category->name : 'Lainnya',
                'created_at' => $transaction->created_at->toISOString(),
                'user_id' => $transaction->user_id
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to broadcast new transaction: ' . $e->getMessage());
        }
    }
}
