<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CreateAndVerifyTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:create-test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create and verify a test user for login testing';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Check if user already exists
        $existingUser = User::where('username', 'testuser')->orWhere('email', '<EMAIL>')->first();
        
        if ($existingUser) {
            $this->info('User test sudah ada:');
            $this->line('Username: ' . $existingUser->username);
            $this->line('Email: ' . $existingUser->email);
            $this->line('Email Verified: ' . ($existingUser->email_verified_at ? 'Yes' : 'No'));
            $this->line('Is Active: ' . ($existingUser->is_active ? 'Yes' : 'No'));
              if ($this->confirm('Apakah ingin mengupdate user ini?')) {
                $existingUser->email_verified_at = now();
                $existingUser->is_active = true;
                $existingUser->save();
                $this->info('✓ User test berhasil diupdate dan diaktifkan!');
                $this->line('Email Verified: ' . $existingUser->email_verified_at);
                $this->line('Is Active: ' . ($existingUser->is_active ? 'Yes' : 'No'));
            }
            
            return Command::SUCCESS;
        }
          // Create new test user
        $user = User::create([
            'name' => 'Test User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        
        $this->info('✓ User test berhasil dibuat dan diaktifkan!');
        $this->line('Username: testuser');
        $this->line('Email: <EMAIL>');
        $this->line('Password: password123');
        $this->line('Status: Email Verified & Active');
        
        return Command::SUCCESS;
    }
}
