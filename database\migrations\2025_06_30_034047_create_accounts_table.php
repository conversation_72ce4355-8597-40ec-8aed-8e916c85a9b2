<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('name');
            $table->string('type')->default('cash'); // cash, bank, credit_card, e_wallet, investment
            $table->string('account_number')->nullable();
            $table->string('bank_name')->nullable();
            $table->decimal('initial_balance', 15, 2)->default(0);
            $table->decimal('current_balance', 15, 2)->default(0);
            $table->string('currency', 3)->default('IDR');
            $table->string('icon')->default('fas fa-wallet');
            $table->string('color')->default('#28a745');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('include_in_total')->default(true);
            $table->decimal('credit_limit', 15, 2)->nullable(); // For credit cards
            $table->date('due_date')->nullable(); // For credit cards
            $table->decimal('interest_rate', 5, 2)->nullable(); // For savings/investment accounts
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'is_active']);
            $table->index(['type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accounts');
    }
};
