<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CheckUserStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:check {username}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check status of a specific user';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $username = $this->argument('username');
        
        $user = User::where('username', $username)->first();
        
        if (!$user) {
            $this->error('User dengan username "' . $username . '" tidak ditemukan.');
            return Command::FAILURE;
        }
        
        $this->info('=== STATUS USER ===');
        $this->line('Username: ' . $user->username);
        $this->line('Email: ' . $user->email);
        $this->line('Email Verified: ' . ($user->email_verified_at ? 'Yes (' . $user->email_verified_at . ')' : 'No'));
        $this->line('Is Active: ' . ($user->is_active ? 'Yes' : 'No'));
        $this->line('Created: ' . $user->created_at);
        $this->line('Updated: ' . $user->updated_at);
        
        return Command::SUCCESS;
    }
}
