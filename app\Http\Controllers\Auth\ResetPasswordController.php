<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\ResetsPasswords;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;    /**
     * Where to redirect users after resetting their password.
     *
     * @var string
     */
    protected $redirectTo = '/login';

    /**
     * Get the response for a successful password reset.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $response
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    protected function sendResetResponse(\Illuminate\Http\Request $request, $response)
    {
        return redirect()->route('login')
                        ->with('success', 'Password berhasil direset! Silakan login dengan password baru Anda.');
    }

    /**
     * Get the redirect path after password reset
     * 
     * @return string
     */
    public function redirectPath()
    {
        return '/login';
    }

    /**
     * Reset the given user's password.
     *
     * @param  \Illuminate\Contracts\Auth\CanResetPassword  $user
     * @param  string  $password
     * @return void
     */
    protected function resetPassword($user, $password)
    {
        try {
            $user->password = bcrypt($password);
            $user->password_changed_at = now(); // Track password change
            $user->setRememberToken(\Str::random(60));
            $user->save();

            // Log password reset
            \App\Models\SecurityLog::logEvent(
                $user->id,
                'password_reset_completed',
                'Password reset completed via email',
                'medium',
                [
                    'ip' => request()->ip(),
                    'user_agent' => request()->userAgent()
                ]
            );

            // Don't auto-login after password reset for security
            // User must login manually with new password
            
        } catch (\Exception $e) {
            \Log::error('Password reset error during save: ' . $e->getMessage());
            throw $e;
        }
    }
}
