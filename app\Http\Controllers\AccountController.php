<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class AccountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Account::where('user_id', Auth::id());
        
        // Filter berdasarkan status
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }
        
        // Filter berdasarkan tipe
        if ($request->filled('type') && $request->type !== 'all') {
            $query->where('type', $request->type);
        }
        
        // Search berdasarkan nama
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }
        
        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        
        if (in_array($sortBy, ['name', 'type', 'current_balance', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }
        
        $accounts = $query->paginate(10)->withQueryString();
        
        // Statistik untuk dashboard - consistent with other controllers
        $totalBalance = Account::where('user_id', Auth::id())
            ->where('is_active', true)
            ->where('include_in_total', true)
            ->sum('current_balance');
            
        $activeAccounts = Account::where('user_id', Auth::id())
            ->where('is_active', true)
            ->count();
            
        $inactiveAccounts = Account::where('user_id', Auth::id())
            ->where('is_active', false)
            ->count();

        // Hitung jumlah jenis akun yang aktif
        $activeAccountTypes = Account::where('user_id', Auth::id())
            ->where('is_active', true)
            ->distinct('type')
            ->count('type');
            
        $stats = [
            'total_balance' => formatRupiah($totalBalance),
            'total_accounts' => Account::where('user_id', Auth::id())->count(),
            'active_accounts' => $activeAccounts,
            'inactive_accounts' => $inactiveAccounts,
            'account_types' => $activeAccountTypes,
        ];
        
        // Data untuk chart - saldo per tipe akun
        $balanceByType = Account::where('user_id', Auth::id())
            ->where('is_active', true)
            ->select('type', DB::raw('SUM(current_balance) as total_balance'))
            ->groupBy('type')
            ->get()
            ->mapWithKeys(function ($item) {
                $typeLabels = [
                    'bank' => 'Bank',
                    'cash' => 'Tunai',
                    'e_wallet' => 'E-Wallet',
                    'investment' => 'Investasi',
                    'credit_card' => 'Kartu Kredit',
                    'savings' => 'Tabungan',
                    'other' => 'Lainnya'
                ];
                return [$typeLabels[$item->type] ?? $item->type => $item->total_balance];
            });

        return view('accounts.list.index', compact(
            'accounts', 
            'stats', 
            'balanceByType'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $accountTypes = [
            'bank' => 'Bank',
            'cash' => 'Tunai',
            'e_wallet' => 'E-Wallet',
            'investment' => 'Investasi',
            'credit_card' => 'Kartu Kredit',
            'savings' => 'Tabungan',
            'other' => 'Lainnya'
        ];
        
        return view('accounts.create.index', compact('accountTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            \Log::info('AccountController@store called', [
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            $request->validate([
                'name' => 'required|string|max:255',
                'type' => 'required|string|in:bank,cash,e_wallet,investment,credit_card,savings,other',
                'initial_balance' => 'nullable|string', // Accept as string for currency format
                'current_balance' => 'nullable|numeric|min:0', // For edit mode
                'current_balance_numeric' => 'nullable|numeric|min:0', // For AJAX submission
                'description' => 'nullable|string|max:500',
                'bank_name' => 'nullable|string|max:255',
                'account_number' => 'nullable|string|max:255',
                'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'icon' => 'nullable|string|max:50',
                'credit_limit' => 'nullable|string', // Accept as string for currency format
                'credit_limit_numeric' => 'nullable|numeric|min:0', // For AJAX submission
            ]);

            \Log::info('AccountController@store validation passed');

            // Set initial balance to 0 for new accounts (parse currency if provided)
            $initialBalance = 0; // Always start with 0 for new accounts
            if ($request->initial_balance) {
                $initialBalance = (float) preg_replace('/[^0-9]/', '', $request->initial_balance) ?: 0;
            }
            
            // Get credit limit (use numeric version if available from AJAX, otherwise parse currency)
            $creditLimit = $request->credit_limit_numeric ?? 
                          ($request->credit_limit ? (float) preg_replace('/[^0-9]/', '', $request->credit_limit) : null);

            $accountData = [
                'user_id' => Auth::id(),
                'name' => $request->name,
                'type' => $request->type,
                'initial_balance' => $initialBalance,
                'current_balance' => $initialBalance,
                'description' => $request->description,
                'bank_name' => $request->bank_name,
                'account_number' => $request->account_number,
                'color' => $request->color ?? '#007bff',
                'icon' => $request->icon ?? 'fas fa-wallet',
                'credit_limit' => $creditLimit,
                'is_active' => true,
                'include_in_total' => true, // Default to include in total
            ];

            \Log::info('AccountController@store about to create account', $accountData);

            $account = Account::create($accountData);

            \Log::info('AccountController@store account created successfully', [
                'account_id' => $account->id,
                'account_name' => $account->name
            ]);

            // Check if request is AJAX for SweetAlert response
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Akun berhasil ditambahkan dengan saldo awal Rp 0!',
                    'redirect' => route('accounts.list.index')
                ]);
            }

            return redirect()->route('accounts.list.index')
                ->with('success', 'Akun berhasil ditambahkan dengan saldo awal Rp 0!');
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('AccountController@store validation error', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            
            // Handle validation errors for AJAX
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan validasi!',
                    'errors' => $e->errors()
                ], 422);
            }
            
            throw $e;
        } catch (\Exception $e) {
            \Log::error('AccountController@store general error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            // Handle general errors
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat menyimpan akun!'
                ], 500);
            }
            
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat menyimpan akun!')
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $account = Account::where('user_id', Auth::id())->findOrFail($id);
        
        // Transaksi terakhir
        $recentTransactions = Transaction::where('account_id', $id)
            ->orderBy('date', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
            
        // Statistik bulanan
        $monthlyStats = Transaction::where('account_id', $id)
            ->where('date', '>=', now()->startOfMonth())
            ->selectRaw('
                SUM(CASE WHEN type = "income" THEN amount ELSE 0 END) as total_income,
                SUM(CASE WHEN type = "expense" THEN amount ELSE 0 END) as total_expense,
                COUNT(*) as total_transactions
            ')
            ->first();
            
        return view('accounts.show', compact('account', 'recentTransactions', 'monthlyStats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $account = Account::where('user_id', Auth::id())->findOrFail($id);
        
        $accountTypes = [
            'bank' => 'Bank',
            'cash' => 'Tunai',
            'e_wallet' => 'E-Wallet',
            'investment' => 'Investasi',
            'credit_card' => 'Kartu Kredit',
            'savings' => 'Tabungan',
            'other' => 'Lainnya'
        ];
        
        // For now, redirect to list page
        // TODO: Implement modal edit or separate edit page
        return redirect()->route('accounts.list.index')->with('info', 'Edit akun akan segera tersedia.');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $account = Account::where('user_id', Auth::id())->findOrFail($id);
            
            $request->validate([
                'name' => 'required|string|max:255',
                'type' => 'required|string|in:bank,cash,e_wallet,investment,credit_card,savings,other',
                'current_balance' => 'required|string', // Accept as string for currency format
                'current_balance_numeric' => 'nullable|numeric|min:0', // For AJAX submission
                'description' => 'nullable|string|max:500',
                'bank_name' => 'nullable|string|max:255',
                'account_number' => 'nullable|string|max:255',
                'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'icon' => 'nullable|string|max:50',
                'credit_limit' => 'nullable|string', // Accept as string for currency format
                'credit_limit_numeric' => 'nullable|numeric|min:0', // For AJAX submission
                'is_active' => 'boolean',
            ]);

            // Parse currency formatted values (use numeric version if available)
            $currentBalance = $request->current_balance_numeric ?? (float) preg_replace('/[^0-9]/', '', $request->current_balance);
            $creditLimit = $request->credit_limit_numeric ?? ($request->credit_limit ? (float) preg_replace('/[^0-9]/', '', $request->credit_limit) : null);

            $account->update([
                'name' => $request->name,
                'type' => $request->type,
                'current_balance' => $currentBalance,
                'description' => $request->description,
                'bank_name' => $request->bank_name,
                'account_number' => $request->account_number,
                'color' => $request->color ?? '#007bff',
                'icon' => $request->icon ?? 'fas fa-wallet',
                'credit_limit' => $creditLimit,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Check if request is AJAX for SweetAlert response
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Akun berhasil diperbarui!',
                    'redirect' => route('accounts.list.index')
                ]);
            }

            return redirect()->route('accounts.list.index')
                ->with('success', 'Akun berhasil diperbarui!');
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Handle validation errors for AJAX
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan validasi!',
                    'errors' => $e->errors()
                ], 422);
            }
            
            throw $e;
        } catch (\Exception $e) {
            // Handle general errors
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat memperbarui akun!'
                ], 500);
            }
            
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat memperbarui akun!')
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $account = Account::where('user_id', Auth::id())->findOrFail($id);
        
        // Check apakah ada transaksi
        $transactionCount = Transaction::where('account_id', $id)->count();
        
        if ($transactionCount > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat menghapus akun yang masih memiliki transaksi!'
            ]);
        }
        
        $account->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Akun berhasil dihapus!'
        ]);
    }
    
    /**
     * Toggle status akun (aktif/nonaktif)
     */
    public function toggleStatus(Request $request, string $id)
    {
        $account = Account::where('user_id', Auth::id())->findOrFail($id);
        
        // Use the is_active value from request if provided, otherwise toggle
        $newStatus = $request->has('is_active') ? $request->boolean('is_active') : !$account->is_active;
        
        $account->update([
            'is_active' => $newStatus
        ]);
        
        $status = $account->is_active ? 'diaktifkan' : 'dinonaktifkan';
        
        return response()->json([
            'success' => true,
            'message' => "Akun berhasil {$status}!",
            'is_active' => $account->is_active
        ]);
    }
    
    /**
     * Monitor saldo semua akun
     */
    public function balance(Request $request)
    {
        $accounts = Account::where('user_id', Auth::id())
            ->where('is_active', true)
            ->orderBy('current_balance', 'desc')
            ->get();
            
        // Total saldo
        $totalBalance = $accounts->sum('current_balance');
        
        // Saldo per tipe akun
        $balanceByType = $accounts->groupBy('type')->map(function ($accounts) {
            return [
                'count' => $accounts->count(),
                'balance' => $accounts->sum('current_balance')
            ];
        });
        
        // Transaksi hari ini
        $todayTransactions = Transaction::whereIn('account_id', $accounts->pluck('id'))
            ->whereDate('date', today())
            ->selectRaw('
                SUM(CASE WHEN type = "income" THEN amount ELSE 0 END) as today_income,
                SUM(CASE WHEN type = "expense" THEN amount ELSE 0 END) as today_expense
            ')
            ->first();
            
        // Trend 7 hari terakhir
        $weeklyTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $dailyBalance = Transaction::whereIn('account_id', $accounts->pluck('id'))
                ->whereDate('date', $date)
                ->selectRaw('
                    SUM(CASE WHEN type = "income" THEN amount ELSE 0 END) - 
                    SUM(CASE WHEN type = "expense" THEN amount ELSE 0 END) as net_amount
                ')
                ->first();
                
            $weeklyTrend[] = [
                'date' => $date->format('Y-m-d'),
                'label' => $date->format('d M'),
                'amount' => $dailyBalance->net_amount ?? 0            ];
        }
        
        // Prepare summary data for the view
        $summary = [
            'total_balance' => formatRupiah($totalBalance),
            'positive_balance' => formatRupiah($accounts->where('current_balance', '>', 0)->sum('current_balance')),
            'negative_balance' => formatRupiah(abs($accounts->where('current_balance', '<', 0)->sum('current_balance'))),
            'zero_balance' => formatRupiah($accounts->where('current_balance', '=', 0)->sum('current_balance')),
            'positive_accounts' => $accounts->where('current_balance', '>', 0)->count(),
            'negative_accounts' => $accounts->where('current_balance', '<', 0)->count(),
            'zero_accounts' => $accounts->where('current_balance', '=', 0)->count(),
            'total_accounts' => $accounts->count(),
            'active_accounts' => $accounts->count(),
            'balance_trend' => 'stable' // You can implement trend calculation here
        ];

        return view('accounts.monitor.index', compact(
            'accounts', 
            'totalBalance', 
            'balanceByType', 
            'todayTransactions',
            'weeklyTrend',
            'summary'
        ));
    }

    /**
     * Halaman Daftar Akun (Sub Menu)
     */
    public function listAccounts(Request $request)
    {
        $query = Account::where('user_id', Auth::id());
        
        // Filter berdasarkan status
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }
        
        // Filter berdasarkan tipe
        if ($request->filled('type') && $request->type !== 'all') {
            $query->where('type', $request->type);
        }
        
        // Search berdasarkan nama
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }
        
        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        
        if (in_array($sortBy, ['name', 'type', 'current_balance', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }
        
        $accounts = $query->paginate(12)->withQueryString();
        
        // Statistik untuk dashboard - consistent with other controllers
        $stats = [
            'total_balance' => Account::where('user_id', Auth::id())
                ->where('is_active', true)
                ->where('include_in_total', true)
                ->sum('current_balance'),
            'total_accounts' => Account::where('user_id', Auth::id())->count(),
            'active_accounts' => Account::where('user_id', Auth::id())
                ->where('is_active', true)
                ->count(),
            'inactive_accounts' => Account::where('user_id', Auth::id())
                ->where('is_active', false)
                ->count(),
        ];
        
        // Data untuk chart - saldo per tipe akun
        $balanceByType = Account::where('user_id', Auth::id())
            ->where('is_active', true)
            ->select('type', DB::raw('SUM(current_balance) as total_balance'))
            ->groupBy('type')
            ->get()
            ->mapWithKeys(function ($item) {
                $typeLabels = [
                    'bank' => 'Bank',
                    'cash' => 'Tunai',
                    'e_wallet' => 'E-Wallet',
                    'investment' => 'Investasi',
                    'credit_card' => 'Kartu Kredit',
                    'savings' => 'Tabungan',
                    'other' => 'Lainnya'
                ];
                return [$typeLabels[$item->type] ?? $item->type => $item->total_balance];
            });
        
        return view('accounts.list.index', compact('accounts', 'stats', 'balanceByType'));
    }

    /**
     * Halaman Tambah Akun (Sub Menu)
     */
    public function createAccount(Request $request)
    {
        // Check if this is an edit request
        $account = null;
        if ($request->has('edit')) {
            $account = Account::where('user_id', Auth::id())->find($request->edit);
        }
        
        $accountTypes = [
            'bank' => 'Bank',
            'cash' => 'Tunai',
            'e_wallet' => 'E-Wallet',
            'investment' => 'Investasi',
            'credit_card' => 'Kartu Kredit',
            'savings' => 'Tabungan',
            'other' => 'Lainnya'
        ];

        $predefinedIcons = [
            'fas fa-university' => 'Bank',
            'fas fa-wallet' => 'Dompet',
            'fas fa-money-bill-wave' => 'Uang Tunai',
            'fas fa-mobile-alt' => 'E-Wallet',
            'fas fa-chart-line' => 'Investasi',
            'fas fa-credit-card' => 'Kartu Kredit',
            'fas fa-piggy-bank' => 'Tabungan',
            'fas fa-coins' => 'Koin',
            'fas fa-dollar-sign' => 'Dollar',
            'fas fa-euro-sign' => 'Euro',
            'fas fa-pound-sign' => 'Pound',
            'fas fa-yen-sign' => 'Yen',
        ];

        $predefinedColors = [
            '#007bff' => 'Biru',
            '#28a745' => 'Hijau',
            '#dc3545' => 'Merah',
            '#ffc107' => 'Kuning',
            '#17a2b8' => 'Cyan',
            '#6f42c1' => 'Ungu',
            '#e83e8c' => 'Pink',
            '#fd7e14' => 'Orange',
            '#20c997' => 'Teal',
            '#6c757d' => 'Abu-abu',
            '#343a40' => 'Hitam',
            '#f8f9fa' => 'Putih',
        ];
        
        return view('accounts.create.index', compact('accountTypes', 'predefinedIcons', 'predefinedColors', 'account'));
    }

    /**
     * Display monitor balance page
     */
    public function monitorBalance()
    {
        return view('monitor.index');
    }
    
    /**
     * Get balance cards data for monitor
     */
    public function getBalanceCards(Request $request)
    {
        $period = $request->get('period', 30);
        $accountId = $request->get('account_id');
        
        $query = Account::where('user_id', Auth::id())
                       ->where('is_active', true);
        
        if ($accountId) {
            $query->where('id', $accountId);
        }
        
        $accounts = $query->get();
        
        $result = [];
        foreach ($accounts as $account) {
            // Calculate balance change for period
            $periodStartDate = now()->subDays($period)->format('Y-m-d');
            
            $balanceChange = Transaction::where('account_id', $account->id)
                ->where('transaction_date', '>=', $periodStartDate)
                ->sum(DB::raw('CASE WHEN type = "income" THEN amount ELSE -amount END'));
            
            $result[] = [
                'id' => $account->id,
                'name' => $account->name,
                'type' => $account->type,
                'balance' => $account->current_balance,
                'balance_change' => $balanceChange
            ];
        }
        
        return response()->json($result);
    }
    
    /**
     * Get chart data for monitor
     */
    public function getChartData(Request $request)
    {
        $period = $request->get('period', 30);
        $accountId = $request->get('account_id');
        
        $endDate = now();
        $startDate = $endDate->copy()->subDays($period);
        
        $query = Account::where('user_id', Auth::id())
                       ->where('is_active', true);
        
        if ($accountId) {
            $query->where('id', $accountId);
        }
        
        $accounts = $query->get();
        
        // Generate date labels
        $labels = [];
        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $labels[] = $currentDate->format('Y-m-d');
            $currentDate->addDay();
        }
        
        $datasets = [];
        $colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#43e97b'];
        $colorIndex = 0;
        
        foreach ($accounts as $account) {
            $data = [];
            $runningBalance = 0;
            
            // Get initial balance
            $initialBalance = $account->current_balance;
            $totalTransactions = Transaction::where('account_id', $account->id)
                ->where('transaction_date', '>=', $startDate->format('Y-m-d'))
                ->sum(DB::raw('CASE WHEN type = "income" THEN amount ELSE -amount END'));
            
            $initialBalance -= $totalTransactions;
            
            foreach ($labels as $date) {
                $dayTransactions = Transaction::where('account_id', $account->id)
                    ->whereDate('transaction_date', $date)
                    ->sum(DB::raw('CASE WHEN type = "income" THEN amount ELSE -amount END'));
                
                $runningBalance += $dayTransactions;
                $data[] = $initialBalance + $runningBalance;
            }
            
            $datasets[] = [
                'label' => $account->name,
                'data' => $data,
                'borderColor' => $colors[$colorIndex % count($colors)],
                'backgroundColor' => $colors[$colorIndex % count($colors)] . '20',
                'fill' => false,
                'tension' => 0.1
            ];
            
            $colorIndex++;
        }
        
        return response()->json([
            'labels' => array_map(function($date) {
                return \Carbon\Carbon::parse($date)->format('d/m');
            }, $labels),
            'datasets' => $datasets
        ]);
    }
    
    /**
     * Get monitor table data
     */
    public function getMonitorData(Request $request)
    {
        $period = $request->get('period', 30);
        $accountId = $request->get('account_id');
        $page = $request->get('page', 1);
        $perPage = 10;
        
        $query = Account::where('user_id', Auth::id())
                       ->where('is_active', true);
        
        if ($accountId) {
            $query->where('id', $accountId);
        }
        
        $accounts = $query->paginate($perPage, ['*'], 'page', $page);
        
        $periodStartDate = now()->subDays($period)->format('Y-m-d');
        
        $data = [];
        foreach ($accounts as $account) {
            // Calculate period start balance
            $periodTransactions = Transaction::where('account_id', $account->id)
                ->where('transaction_date', '>=', $periodStartDate)
                ->sum(DB::raw('CASE WHEN type = "income" THEN amount ELSE -amount END'));
            
            $periodStartBalance = $account->current_balance - $periodTransactions;
            
            // Get last transaction
            $lastTransaction = Transaction::where('account_id', $account->id)
                ->orderBy('transaction_date', 'desc')
                ->orderBy('created_at', 'desc')
                ->first();
            
            $data[] = [
                'id' => $account->id,
                'name' => $account->name,
                'type' => $account->type,
                'description' => $account->description,
                'current_balance' => $account->current_balance,
                'period_start_balance' => $periodStartBalance,
                'last_transaction' => $lastTransaction ? [
                    'title' => $lastTransaction->title,
                    'transaction_date' => $lastTransaction->transaction_date,
                    'amount' => $lastTransaction->amount,
                    'type' => $lastTransaction->type
                ] : null
            ];
        }
        
        return response()->json([
            'data' => $data,
            'current_page' => $accounts->currentPage(),
            'last_page' => $accounts->lastPage(),
            'total' => $accounts->total(),
            'from' => $accounts->firstItem(),
            'to' => $accounts->lastItem()
        ]);
    }
    
    /**
     * Export monitor data
     */
    public function exportMonitorData(Request $request)
    {
        $format = $request->get('format', 'pdf');
        
        // For now, just return a simple response
        // In production, you would use packages like DomPDF or PhpSpreadsheet
        
        if ($format === 'pdf') {
            // Generate PDF export
            return response()->json(['message' => 'PDF export will be implemented']);
        } else {
            // Generate Excel export
            return response()->json(['message' => 'Excel export will be implemented']);
        }
    }

    /**
     * API: Get list of accounts
     */
    public function apiList()
    {
        try {
            $accounts = Account::where('user_id', Auth::id())
                             ->where('is_active', true)
                             ->orderBy('name')
                             ->get(['id', 'name', 'type', 'current_balance']);

            return response()->json($accounts);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load accounts'
            ], 500);
        }
    }
}
