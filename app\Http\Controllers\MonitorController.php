<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Account;
use App\Models\Transaction;
use App\Models\Category;
use App\Services\DashboardRealtimeService;
use App\Services\EnhancedDashboardService;
use Carbon\Carbon;
use DB;

class MonitorController extends Controller
{
    protected $dashboardService;
    protected $enhancedService;

    public function __construct(DashboardRealtimeService $dashboardService, EnhancedDashboardService $enhancedService)
    {
        $this->middleware('auth');
        $this->dashboardService = $dashboardService;
        $this->enhancedService = $enhancedService;
    }

    /**
     * Display monitor balance page
     */
    public function index()
    {
        $userId = auth()->id();
        
        // Get all accounts for the user (both active and inactive for complete overview)
        $allAccounts = Account::where('user_id', $userId)->get();
        
        // Get only active accounts for main calculations
        $accounts = Account::where('user_id', $userId)
                          ->where('is_active', true)
                          ->get();

        // Add current_balance to each account (using the current_balance field)
        $accounts->each(function ($account) {
            $account->current_balance = $account->current_balance; // Already correct field
        });
        
        $allAccounts->each(function ($account) {
            $account->current_balance = $account->current_balance; // Already correct field
        });

        // Calculate summary data from ACTIVE accounts that are included in total (consistent with Dashboard)
        $activeIncludedAccounts = $allAccounts->where('is_active', true)->where('include_in_total', true);
        $totalBalance = $activeIncludedAccounts->sum('current_balance');
        
        // For display purposes, still show all active accounts
        $activeAccounts = $allAccounts->where('is_active', true);
        $positiveAccounts = $activeAccounts->where('current_balance', '>', 0);
        $negativeAccounts = $activeAccounts->where('current_balance', '<', 0);
        $positiveBalance = $positiveAccounts->sum('current_balance');
        $negativeBalance = $negativeAccounts->sum('current_balance');
        
        // Calculate trend (simplified - you can enhance this with historical data)
        $balanceTrend = 'stable';
        if ($positiveBalance > abs($negativeBalance)) {
            $balanceTrend = 'up';
        } elseif ($positiveBalance < abs($negativeBalance)) {
            $balanceTrend = 'down';
        }

        $summary = [
            'total_balance' => 'Rp ' . number_format($totalBalance, 0, ',', '.'),
            'balance_trend' => $balanceTrend,
            'positive_balance' => 'Rp ' . number_format($positiveBalance, 0, ',', '.'),
            'positive_accounts' => $positiveAccounts->count(),
            'negative_balance' => 'Rp ' . number_format(abs($negativeBalance), 0, ',', '.'),
            'negative_accounts' => $negativeAccounts->count(),
            'total_accounts' => $allAccounts->count(),
            'active_accounts' => $activeAccounts->count()
        ];

        // Enhanced chart data
        $chartData = [
            'balance' => [
                'labels' => $allAccounts->pluck('name')->toArray(),
                'data' => $allAccounts->pluck('current_balance')->toArray(),
                'backgroundColor' => $allAccounts->pluck('color')->toArray()
            ],
            'trend' => [
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'data' => [
                    $totalBalance * 0.7,  // Simulate historical data
                    $totalBalance * 0.8,
                    $totalBalance * 0.75,
                    $totalBalance * 0.9,
                    $totalBalance * 0.95,
                    $totalBalance
                ]
            ],
            'accountTypes' => [
                'labels' => $allAccounts->groupBy('type')->keys()->toArray(),
                'data' => $allAccounts->groupBy('type')->map(function ($group) {
                    return $group->sum('current_balance');
                })->values()->toArray()
            ]
        ];

        // Return all accounts for display, but summary calculated from active ones
        return view('accounts.monitor.index', compact('allAccounts', 'summary', 'chartData'));
    }

    /**
     * API: Get balance cards data
     */
    public function getBalanceCards()
    {
        try {
            $userId = auth()->id();
            
            // Get active accounts that are included in total for consistent calculation with Dashboard
            $activeAccounts = Account::where('user_id', $userId)
                                   ->where('is_active', true)
                                   ->where('include_in_total', true)
                                   ->get();
            
            $totalBalance = $activeAccounts->sum('current_balance');
            
            // For trend calculation, use all active accounts (including those not in total)
            $allActiveAccounts = Account::where('user_id', $userId)
                                      ->where('is_active', true)
                                      ->get();
            $positiveAccounts = $allActiveAccounts->where('current_balance', '>', 0);
            $negativeAccounts = $allActiveAccounts->where('current_balance', '<', 0);
            $positiveBalance = $positiveAccounts->sum('current_balance');
            $negativeBalance = $negativeAccounts->sum('current_balance');
            
            // Calculate trend
            $balanceTrend = 'stable';
            if ($positiveBalance > abs($negativeBalance)) {
                $balanceTrend = 'up';
            } elseif ($positiveBalance < abs($negativeBalance)) {
                $balanceTrend = 'down';
            }

            // Get today's transactions if Transaction model exists
            $todayIncome = 0;
            $todayExpense = 0;
            $totalTransactionsToday = 0;
            
            if (class_exists('App\\Models\\Transaction')) {
                $todayIncome = \App\Models\Transaction::where('user_id', $userId)
                                       ->where('type', 'income')
                                       ->whereDate('transaction_date', today())
                                       ->sum('amount');

                $todayExpense = \App\Models\Transaction::where('user_id', $userId)
                                        ->where('type', 'expense')
                                        ->whereDate('transaction_date', today())
                                        ->sum('amount');

                $totalTransactionsToday = \App\Models\Transaction::where('user_id', $userId)
                                                   ->whereDate('transaction_date', today())
                                                   ->count();
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'total_balance' => 'Rp ' . number_format($totalBalance, 0, ',', '.'),
                    'balance_trend' => $balanceTrend,
                    'positive_balance' => 'Rp ' . number_format($positiveBalance, 0, ',', '.'),
                    'positive_accounts' => $positiveAccounts->count(),
                    'negative_balance' => 'Rp ' . number_format(abs($negativeBalance), 0, ',', '.'),
                    'negative_accounts' => $negativeAccounts->count(),
                    'total_accounts' => $allAccounts->count(),
                    'active_accounts' => $activeAccounts->count(),
                    'today_income' => 'Rp ' . number_format($todayIncome, 0, ',', '.'),
                    'today_expense' => 'Rp ' . number_format($todayExpense, 0, ',', '.'),
                    'total_transactions_today' => $totalTransactionsToday,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching balance cards data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Get monitor data (accounts and recent transactions)
     */
    public function getData()
    {
        try {
            $userId = auth()->id();
            
            // Get ALL accounts (both active and inactive)
            $allAccounts = Account::where('user_id', $userId)->get();
            
            // Add current_balance to each account
            $allAccounts->each(function ($account) {
                $account->current_balance = $account->current_balance;
                $account->formatted_balance = 'Rp ' . number_format($account->current_balance, 0, ',', '.');
            });

            // Get recent activities if Transaction model exists
            $recentActivities = [];
            if (class_exists('App\\Models\\Transaction')) {
                $recentActivities = \App\Models\Transaction::where('user_id', $userId)
                                             ->with(['account', 'category'])
                                             ->orderBy('transaction_date', 'desc')
                                             ->orderBy('created_at', 'desc')
                                             ->limit(10)
                                             ->get();
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'accounts' => $allAccounts,
                    'recent_activities' => $recentActivities,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching monitor data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Get chart data for monitor
     */
    public function getChartData()
    {
        try {
            $userId = auth()->id();
            
            // Get accounts data for distribution chart
            $allAccounts = Account::where('user_id', $userId)->get();
            
            // Account balance distribution
            $accountDistribution = [
                'labels' => $allAccounts->pluck('name')->toArray(),
                'data' => $allAccounts->pluck('current_balance')->toArray(),
                'backgroundColor' => $allAccounts->pluck('color')->toArray()
            ];
            
            // Account type distribution
            $accountTypes = $allAccounts->groupBy('type');
            $typeDistribution = [
                'labels' => $accountTypes->keys()->map(function($type) {
                    return ucfirst($type);
                })->toArray(),
                'data' => $accountTypes->map(function ($group) {
                    return $group->sum('current_balance');
                })->values()->toArray()
            ];
            
            // Monthly data (if Transaction model exists)
            $monthlyData = [];
            if (class_exists('App\\Models\\Transaction')) {
                for ($i = 5; $i >= 0; $i--) {
                    $date = \Carbon\Carbon::now()->subMonths($i);
                    
                    $income = \App\Models\Transaction::where('user_id', $userId)
                                       ->where('type', 'income')
                                       ->whereYear('transaction_date', $date->year)
                                       ->whereMonth('transaction_date', $date->month)
                                       ->sum('amount');

                    $expense = \App\Models\Transaction::where('user_id', $userId)
                                        ->where('type', 'expense')
                                        ->whereYear('transaction_date', $date->year)
                                        ->whereMonth('transaction_date', $date->month)
                                        ->sum('amount');

                    $monthlyData[] = [
                        'month' => $date->format('M Y'),
                        'month_short' => $date->format('M'),
                        'income' => $income,
                        'expense' => $expense,
                        'net' => $income - $expense
                    ];
                }
            } else {
                // Simulate data if no transactions table
                $totalBalance = $allAccounts->sum('current_balance');
                for ($i = 5; $i >= 0; $i--) {
                    $date = \Carbon\Carbon::now()->subMonths($i);
                    $baseAmount = $totalBalance * (0.7 + ($i * 0.05));
                    
                    $monthlyData[] = [
                        'month' => $date->format('M Y'),
                        'month_short' => $date->format('M'),
                        'income' => $baseAmount * 1.2,
                        'expense' => $baseAmount * 0.8,
                        'net' => $baseAmount * 0.4
                    ];
                }
            }
            
            // 30-day trend (simplified)
            $trendData = [];
            $baseBalance = $allAccounts->sum('current_balance');
            for ($i = 29; $i >= 0; $i--) {
                $date = \Carbon\Carbon::now()->subDays($i);
                $variation = rand(-5, 5) / 100; // ±5% random variation
                $dailyBalance = $baseBalance * (1 + $variation);
                
                $trendData[] = [
                    'date' => $date->format('M j'),
                    'balance' => $dailyBalance
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'account_distribution' => $accountDistribution,
                    'type_distribution' => $typeDistribution,
                    'monthly_trend' => $monthlyData,
                    'daily_trend' => $trendData,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching chart data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Get account balance history
     */
    public function getAccountHistory($accountId)
    {
        try {
            $userId = auth()->id();
            
            // Verify account belongs to user
            $account = Account::where('id', $accountId)
                            ->where('user_id', $userId)
                            ->firstOrFail();

            // Get last 30 days transactions for this account
            $transactions = Transaction::where('account_id', $accountId)
                                     ->where('transaction_date', '>=', Carbon::now()->subDays(30))
                                     ->orderBy('transaction_date', 'asc')
                                     ->orderBy('created_at', 'asc')
                                     ->get();

            // Calculate running balance
            $currentBalance = $account->balance;
            $historyData = [];
            
            // Start from current balance and work backwards
            $runningBalance = $currentBalance;
            foreach ($transactions->reverse() as $transaction) {
                if ($transaction->type === 'income') {
                    $runningBalance -= $transaction->amount;
                } else {
                    $runningBalance += $transaction->amount;
                }
            }

            // Now calculate forward
            foreach ($transactions as $transaction) {
                if ($transaction->type === 'income') {
                    $runningBalance += $transaction->amount;
                } else {
                    $runningBalance -= $transaction->amount;
                }

                $historyData[] = [
                    'date' => $transaction->transaction_date,
                    'balance' => $runningBalance,
                    'amount' => $transaction->amount,
                    'type' => $transaction->type,
                    'description' => $transaction->description
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'account' => $account,
                    'history' => $historyData,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching account history: ' . $e->getMessage()
            ], 500);
        }
    }
}
