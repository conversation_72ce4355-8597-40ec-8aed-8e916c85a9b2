<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Advanced security settings for the application
    |
    */

    // Rate Limiting
    'rate_limits' => [
        'global' => 100, // requests per minute
        'auth' => 10,    // auth requests per minute
        'api' => 60,     // API requests per minute
    ],

    // Account Locking
    'account_locking' => [
        'max_attempts' => 3,
        'lockout_duration' => 30, // minutes
        'auto_unlock' => true,
    ],

    // Password Policy
    'password_policy' => [
        'min_length' => 12,
        'max_length' => 128,
        'require_uppercase' => true,
        'require_lowercase' => true,
        'require_numbers' => true,
        'require_symbols' => true,
        'allowed_symbols' => '@$!%*?&^#()',
        'max_age_days' => 90,
        'min_unique_chars' => 8,
        'prevent_reuse' => 5, // last N passwords
    ],

    // Session Security
    'session_security' => [
        'regenerate_interval' => 900, // seconds (15 minutes)
        'max_idle_time' => 3600,      // seconds (1 hour)
        'single_session' => true,     // prevent concurrent sessions
        'secure_cookies' => true,
        'httponly_cookies' => true,
        'samesite_cookies' => 'strict',
    ],

    // IP Security
    'ip_security' => [
        'whitelist' => [
            // '127.0.0.1', '::1' // localhost
        ],
        'blacklist' => [
            // Add suspicious IPs here
        ],
        'auto_blacklist_threshold' => 50,
        'geo_blocking' => [
            'enabled' => false,
            'allowed_countries' => ['ID', 'US', 'SG'],
        ],
    ],

    // File Upload Security
    'file_upload' => [
        'max_size' => 10485760, // 10MB
        'allowed_extensions' => [
            'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg',
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt',
            'csv', 'zip', 'rar'
        ],
        'blocked_extensions' => [
            'php', 'php3', 'php4', 'php5', 'phtml',
            'asp', 'aspx', 'jsp', 'jspx', 'cfm', 'cfc',
            'pl', 'py', 'rb', 'sh', 'bat', 'cmd', 'com',
            'pif', 'scr', 'msi', 'vbs', 'js', 'jar',
            'exe', 'dll', 'so', 'dylib'
        ],
        'scan_content' => true,
        'quarantine_suspicious' => true,
    ],

    // Email Security
    'email_security' => [
        'blocked_domains' => [
            'tempmail.org', '10minutemail.com', 'guerrillamail.com',
            'mailinator.com', 'throwaway.email', 'temp-mail.org',
            'fakeinbox.com', 'maildrop.cc', 'getnada.com',
            'tempail.com', 'sharklasers.com'
        ],
        'require_verification' => true,
        'verification_expiry' => 24, // hours
    ],

    // Attack Detection
    'attack_detection' => [
        'sql_injection' => true,
        'xss_detection' => true,
        'command_injection' => true,
        'path_traversal' => true,
        'file_inclusion' => true,
        'dos_protection' => true,
        'bot_detection' => true,
    ],

    // Security Headers
    'security_headers' => [
        'x_content_type_options' => 'nosniff',
        'x_frame_options' => 'DENY',
        'x_xss_protection' => '1; mode=block',
        'referrer_policy' => 'strict-origin-when-cross-origin',
        'strict_transport_security' => 'max-age=31536000; includeSubDomains',
        'content_security_policy' => [
            'default-src' => "'self'",
            'script-src' => "'self' 'unsafe-inline' 'unsafe-eval' cdn.jsdelivr.net cdnjs.cloudflare.com js.pusher.com",
            'style-src' => "'self' 'unsafe-inline' fonts.googleapis.com cdn.jsdelivr.net cdnjs.cloudflare.com",
            'font-src' => "'self' fonts.gstatic.com cdn.jsdelivr.net cdnjs.cloudflare.com",
            'img-src' => "'self' data: cdn.jsdelivr.net cdnjs.cloudflare.com",
            'connect-src' => "'self' ws.pusherapp.com sockjs-eu.pusher.com",
            'frame-ancestors' => "'none'",
            'base-uri' => "'self'",
            'form-action' => "'self'",
        ],
        'permissions_policy' => [
            'geolocation' => '()',
            'midi' => '()',
            'sync-xhr' => '()',
            'microphone' => '()',
            'camera' => '()',
            'magnetometer' => '()',
            'gyroscope' => '()',
            'fullscreen' => '(self)',
        ]
    ],

    // Logging
    'logging' => [
        'log_all_requests' => false,
        'log_failed_attempts' => true,
        'log_suspicious_activity' => true,
        'log_admin_actions' => true,
        'retention_days' => 90,
        'high_priority_retention_days' => 365,
    ],

    // Monitoring
    'monitoring' => [
        'real_time_alerts' => true,
        'email_alerts' => [
            'enabled' => true,
            'recipients' => [
                '<EMAIL>'
            ],
            'severity_threshold' => 'high',
        ],
        'webhook_alerts' => [
            'enabled' => false,
            'url' => '',
            'secret' => '',
        ],
    ],

    // Backup & Recovery
    'backup' => [
        'auto_backup' => true,
        'backup_interval' => 'daily',
        'retention_days' => 30,
        'encrypt_backups' => true,
        'backup_security_logs' => true,
    ],

    // Development/Debug
    'debug' => [
        'log_all_security_events' => env('APP_DEBUG', false),
        'detailed_error_messages' => env('APP_DEBUG', false),
        'show_security_warnings' => env('APP_DEBUG', false),
    ],
];
