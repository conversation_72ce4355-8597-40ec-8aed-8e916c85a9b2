<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register global helper for Indonesian currency formatting
        if (!function_exists('formatRupiah')) {
            function formatRupiah($amount, $showSymbol = true) {
                $formatted = number_format($amount, 0, ',', '.');
                return $showSymbol ? 'Rp ' . $formatted : $formatted;
            }
        }
    }
}
