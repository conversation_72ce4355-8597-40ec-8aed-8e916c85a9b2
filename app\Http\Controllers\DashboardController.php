<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\DashboardRealtimeService;
use App\Services\EnhancedDashboardService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    protected $realtimeService;
    protected $enhancedService;

    /**
     * Create a new controller instance.
     */
    public function __construct(DashboardRealtimeService $realtimeService, EnhancedDashboardService $enhancedService)
    {
        $this->middleware('auth');
        $this->realtimeService = $realtimeService;
        $this->enhancedService = $enhancedService;
    }    /**
     * Show the application dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get dashboard statistics
        $stats = $this->realtimeService->getDashboardStats();
        
        // Data untuk dashboard
        $data = [
            'user' => $user,
            'stats' => $stats,
            // Nanti akan ditambahkan data keuangan
        ];
        
        return view('dashboard.index', $data);
    }

    /**
     * Get dashboard statistics API
     */
    public function getStats()
    {
        $stats = $this->realtimeService->getDashboardStats();
        $monthlyHistory = $this->enhancedService->getMonthlyHistory();
        $monthlyTransactionStats = $this->enhancedService->getMonthlyTransactionStats();
        
        return response()->json(array_merge($stats, [
            'monthlyHistory' => $monthlyHistory,
            'monthlyTransactionStats' => $monthlyTransactionStats
        ]));
    }

    /**
     * Simulate new transaction for demo
     */
    public function simulateTransaction()
    {
        $result = $this->realtimeService->simulateNewTransaction();
        
        return response()->json([
            'success' => $result,
            'message' => $result ? 'Transaction simulated successfully' : 'Failed to simulate transaction'
        ]);
    }
}
