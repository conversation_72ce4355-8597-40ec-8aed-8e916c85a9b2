<?php

require_once 'vendor/autoload.php';
require_once 'bootstrap/app.php';

use App\Models\Transaction;
use App\Models\Category;
use App\Models\Account;
use Carbon\Carbon;

$app = require_once 'bootstrap/app.php';

$user_id = 2;

// Get expense category
$category = Category::where('user_id', $user_id)->where('type', 'expense')->first();

// Get account
$account = Account::where('user_id', $user_id)->first();

if ($category && $account) {
    // Create expense transaction for July 2025
    Transaction::create([
        'user_id' => $user_id,
        'account_id' => $account->id,
        'category_id' => $category->id,
        'type' => 'expense',
        'amount' => 500000,
        'title' => 'Belanja Bulanan',
        'transaction_date' => '2025-07-15',
        'status' => 'completed',
        'processed_at' => now()
    ]);
    
    echo "Expense transaction created successfully\n";
    
    // Create income transaction for July 2025
    $incomeCategory = Category::where('user_id', $user_id)->where('type', 'income')->first();
    if ($incomeCategory) {
        Transaction::create([
            'user_id' => $user_id,
            'account_id' => $account->id,
            'category_id' => $incomeCategory->id,
            'type' => 'income',
            'amount' => 7000000,
            'title' => 'Gaji Juli',
            'transaction_date' => '2025-07-01',
            'status' => 'completed',
            'processed_at' => now()
        ]);
        echo "Income transaction created successfully\n";
    }
    
} else {
    echo "Category or Account not found\n";
    echo "Categories found: " . Category::where('user_id', $user_id)->count() . "\n";
    echo "Accounts found: " . Account::where('user_id', $user_id)->count() . "\n";
}

// Show total transactions
echo "Total transactions: " . Transaction::where('user_id', $user_id)->count() . "\n";
