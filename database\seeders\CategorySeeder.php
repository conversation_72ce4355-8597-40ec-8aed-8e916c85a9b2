<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\User;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::first();
        
        if (!$user) {
            $this->command->error('No user found. Please create a user first.');
            return;
        }

        // Expense Categories
        $expenseCategories = [
            [
                'name' => 'Makanan & Minuman',
                'type' => 'expense',
                'icon' => 'fas fa-utensils',
                'color' => '#ff6b6b',
                'description' => 'Pengeluaran untuk makanan dan minuman',
                'budget_limit' => 2000000,
                'budget_period' => 'monthly'
            ],
            [
                'name' => 'Transportasi',
                'type' => 'expense',
                'icon' => 'fas fa-car',
                'color' => '#4ecdc4',
                'description' => 'Bensin, parkir, ojek online, dll',
                'budget_limit' => 1500000,
                'budget_period' => 'monthly'
            ],
            [
                'name' => 'Belanja',
                'type' => 'expense',
                'icon' => 'fas fa-shopping-cart',
                'color' => '#45b7d1',
                'description' => 'Berbelanja kebutuhan sehari-hari',
                'budget_limit' => 3000000,
                'budget_period' => 'monthly'
            ],
            [
                'name' => 'Hiburan',
                'type' => 'expense',
                'icon' => 'fas fa-gamepad',
                'color' => '#f9ca24',
                'description' => 'Bioskop, games, streaming, dll',
                'budget_limit' => 500000,
                'budget_period' => 'monthly'
            ],
            [
                'name' => 'Kesehatan',
                'type' => 'expense',
                'icon' => 'fas fa-heartbeat',
                'color' => '#e17055',
                'description' => 'Obat, dokter, rumah sakit',
                'budget_limit' => 1000000,
                'budget_period' => 'monthly'
            ],
            [
                'name' => 'Pendidikan',
                'type' => 'expense',
                'icon' => 'fas fa-graduation-cap',
                'color' => '#6c5ce7',
                'description' => 'Kursus, buku, seminar',
                'budget_limit' => 800000,
                'budget_period' => 'monthly'
            ],
            [
                'name' => 'Tagihan & Utilities',
                'type' => 'expense',
                'icon' => 'fas fa-file-invoice-dollar',
                'color' => '#fd79a8',
                'description' => 'Listrik, air, internet, telepon',
                'budget_limit' => 1200000,
                'budget_period' => 'monthly'
            ],
            [
                'name' => 'Investasi',
                'type' => 'expense',
                'icon' => 'fas fa-chart-line',
                'color' => '#00b894',
                'description' => 'Saham, reksadana, emas',
                'budget_limit' => 2000000,
                'budget_period' => 'monthly'
            ]
        ];

        // Income Categories
        $incomeCategories = [
            [
                'name' => 'Gaji',
                'type' => 'income',
                'icon' => 'fas fa-money-check-alt',
                'color' => '#00b894',
                'description' => 'Gaji bulanan dari pekerjaan'
            ],
            [
                'name' => 'Freelance',
                'type' => 'income',
                'icon' => 'fas fa-laptop-code',
                'color' => '#6c5ce7',
                'description' => 'Penghasilan dari pekerjaan freelance'
            ],
            [
                'name' => 'Investasi',
                'type' => 'income',
                'icon' => 'fas fa-chart-line',
                'color' => '#fdcb6e',
                'description' => 'Dividen, capital gain, bunga'
            ],
            [
                'name' => 'Bonus',
                'type' => 'income',
                'icon' => 'fas fa-gift',
                'color' => '#e17055',
                'description' => 'Bonus kerja, THR, insentif'
            ],
            [
                'name' => 'Bisnis',
                'type' => 'income',
                'icon' => 'fas fa-store',
                'color' => '#0984e3',
                'description' => 'Penghasilan dari bisnis'
            ],
            [
                'name' => 'Lainnya',
                'type' => 'income',
                'icon' => 'fas fa-coins',
                'color' => '#a29bfe',
                'description' => 'Penghasilan lain-lain'
            ]
        ];

        // Create expense categories
        foreach ($expenseCategories as $category) {
            $parent = Category::create([
                'user_id' => $user->id,
                'name' => $category['name'],
                'type' => $category['type'],
                'icon' => $category['icon'],
                'color' => $category['color'],
                'description' => $category['description'],
                'budget_limit' => $category['budget_limit'] ?? null,
                'budget_period' => $category['budget_period'] ?? null,
                'sort_order' => array_search($category, $expenseCategories)
            ]);

            // Note: Subcategories removed since parent_id column was dropped
        }

        // Create income categories
        foreach ($incomeCategories as $category) {
            Category::create([
                'user_id' => $user->id,
                'name' => $category['name'],
                'type' => $category['type'],
                'icon' => $category['icon'],
                'color' => $category['color'],
                'description' => $category['description'],
                'sort_order' => array_search($category, $incomeCategories)
            ]);
        }

        $this->command->info('Categories created successfully!');
    }
}
