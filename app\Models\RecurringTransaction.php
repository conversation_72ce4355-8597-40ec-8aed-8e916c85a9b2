<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class RecurringTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'account_id',
        'category_id',
        'to_account_id',
        'type',
        'amount',
        'title',
        'description',
        'frequency',
        'interval_count',
        'frequency_details',
        'start_date',
        'end_date',
        'max_occurrences',
        'next_due_date',
        'last_generated_date',
        'payment_method',
        'tags',
        'is_active',
        'auto_generate',
        'days_before_reminder',
        'metadata'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'next_due_date' => 'date',
        'last_generated_date' => 'date',
        'frequency_details' => 'array',
        'tags' => 'array',
        'metadata' => 'array',
        'is_active' => 'boolean',
        'auto_generate' => 'boolean',
        'interval_count' => 'integer',
        'max_occurrences' => 'integer',
        'days_before_reminder' => 'integer'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function toAccount(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'to_account_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeDueToday($query)
    {
        return $query->where('next_due_date', '<=', now()->toDateString());
    }

    public function scopeDueThisWeek($query)
    {
        return $query->whereBetween('next_due_date', [
            now()->startOfWeek()->toDateString(),
            now()->endOfWeek()->toDateString()
        ]);
    }

    public function scopeAutoGenerate($query)
    {
        return $query->where('auto_generate', true);
    }

    // Accessors
    public function getFormattedAmountAttribute()
    {
        return 'Rp ' . number_format($this->amount, 0, ',', '.');
    }

    public function getFrequencyLabelAttribute()
    {
        $interval = $this->interval_count > 1 ? "setiap {$this->interval_count}" : 'setiap';
        
        return match($this->frequency) {
            'daily' => $interval . ' hari',
            'weekly' => $interval . ' minggu',
            'monthly' => $interval . ' bulan',
            'yearly' => $interval . ' tahun',
            default => $this->frequency
        };
    }

    // Helper methods
    public function generateNextDueDate(): Carbon
    {
        $currentDue = Carbon::parse($this->next_due_date);
        
        return match($this->frequency) {
            'daily' => $currentDue->addDays($this->interval_count),
            'weekly' => $currentDue->addWeeks($this->interval_count),
            'monthly' => $currentDue->addMonths($this->interval_count),
            'yearly' => $currentDue->addYears($this->interval_count),
            default => $currentDue->addDays(1)
        };
    }

    public function isDue(): bool
    {
        return Carbon::parse($this->next_due_date)->isPast();
    }

    public function shouldAutoGenerate(): bool
    {
        return $this->is_active && $this->auto_generate && $this->isDue();
    }

    public function canGenerateMore(): bool
    {
        if ($this->end_date && Carbon::parse($this->end_date)->isPast()) {
            return false;
        }
        
        if ($this->max_occurrences) {
            $generatedCount = $this->transactions()->count();
            return $generatedCount < $this->max_occurrences;
        }
        
        return true;
    }

    public function generateTransaction(): ?Transaction
    {
        if (!$this->shouldAutoGenerate() || !$this->canGenerateMore()) {
            return null;
        }

        $transaction = Transaction::create([
            'user_id' => $this->user_id,
            'account_id' => $this->account_id,
            'category_id' => $this->category_id,
            'to_account_id' => $this->to_account_id,
            'type' => $this->type,
            'amount' => $this->amount,
            'title' => $this->title,
            'description' => $this->description,
            'transaction_date' => $this->next_due_date,
            'payment_method' => $this->payment_method,
            'tags' => $this->tags,
            'is_recurring' => true,
            'recurring_transaction_id' => $this->id,
            'status' => 'completed'
        ]);

        // Update next due date
        $this->last_generated_date = $this->next_due_date;
        $this->next_due_date = $this->generateNextDueDate();
        $this->save();

        return $transaction;
    }

    public function getReminderDate(): Carbon
    {
        return Carbon::parse($this->next_due_date)
            ->subDays($this->days_before_reminder);
    }

    public function shouldSendReminder(): bool
    {
        return $this->is_active && 
               now()->toDateString() >= $this->getReminderDate()->toDateString() && 
               now()->toDateString() < $this->next_due_date->toDateString();
    }
}
