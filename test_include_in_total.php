<?php

/**
 * Script untuk testing fitur toggle include_in_total
 * Jalankan dengan: php test_include_in_total.php
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Account;
use App\Services\DashboardRealtimeService;
use Illuminate\Support\Facades\Auth;

echo "=== TESTING FITUR TOGGLE INCLUDE IN TOTAL ===\n\n";

// Login sebagai user pertama
$user = User::first();
if (!$user) {
    echo "❌ Tidak ada user untuk testing.\n";
    exit;
}

Auth::login($user);
echo "👤 Testing dengan user: {$user->name} (ID: {$user->id})\n\n";

// Ambil semua akun user
$accounts = Account::where('user_id', $user->id)->get();
if ($accounts->count() < 2) {
    echo "❌ Perlu minimal 2 akun untuk testing.\n";
    exit;
}

echo "💳 Daftar Akun:\n";
foreach ($accounts as $account) {
    $includeStatus = $account->include_in_total ? '✅ Dihitung' : '❌ Tidak Dihitung';
    $activeStatus = $account->is_active ? '🟢 Aktif' : '🔴 Nonaktif';
    echo "   - {$account->name}: Rp " . number_format($account->current_balance, 0, ',', '.') . " | {$activeStatus} | {$includeStatus}\n";
}
echo "\n";

// Hitung saldo total sebelum perubahan
$dashboardService = new DashboardRealtimeService();
$statsBefore = $dashboardService->getDashboardStats($user->id);
$totalBalanceBefore = $statsBefore['totalBalance'];

echo "💰 Saldo Total Sebelum: Rp " . number_format($totalBalanceBefore, 0, ',', '.') . "\n\n";

// Test 1: Toggle OFF akun pertama yang sedang ON
$testAccount1 = $accounts->where('include_in_total', true)->first();
if ($testAccount1) {
    echo "🔄 Test 1: Menonaktifkan '{$testAccount1->name}' dari perhitungan saldo total...\n";
    
    $balanceAccount1 = $testAccount1->current_balance;
    $testAccount1->update(['include_in_total' => false]);
    
    // Hitung saldo total setelah perubahan
    $statsAfter1 = $dashboardService->getDashboardStats($user->id);
    $totalBalanceAfter1 = $statsAfter1['totalBalance'];
    
    echo "   - Saldo akun: Rp " . number_format($balanceAccount1, 0, ',', '.') . "\n";
    echo "   - Saldo total sebelum: Rp " . number_format($totalBalanceBefore, 0, ',', '.') . "\n";
    echo "   - Saldo total sesudah: Rp " . number_format($totalBalanceAfter1, 0, ',', '.') . "\n";
    
    $expectedBalance = $totalBalanceBefore - $balanceAccount1;
    if ($totalBalanceAfter1 == $expectedBalance) {
        echo "   ✅ Test 1 PASSED: Saldo total berkurang sesuai saldo akun yang dinonaktifkan\n\n";
    } else {
        echo "   ❌ Test 1 FAILED: Saldo total tidak sesuai (expected: Rp " . number_format($expectedBalance, 0, ',', '.') . ")\n\n";
    }
    
    $totalBalanceBefore = $totalBalanceAfter1; // Update untuk test berikutnya
} else {
    echo "⚠️  Test 1 SKIPPED: Tidak ada akun yang sedang dihitung dalam saldo total\n\n";
}

// Test 2: Toggle ON akun yang sedang OFF
$testAccount2 = $accounts->where('include_in_total', false)->first();
if ($testAccount2) {
    echo "🔄 Test 2: Mengaktifkan '{$testAccount2->name}' dalam perhitungan saldo total...\n";
    
    $balanceAccount2 = $testAccount2->current_balance;
    $testAccount2->update(['include_in_total' => true]);
    
    // Hitung saldo total setelah perubahan
    $statsAfter2 = $dashboardService->getDashboardStats($user->id);
    $totalBalanceAfter2 = $statsAfter2['totalBalance'];
    
    echo "   - Saldo akun: Rp " . number_format($balanceAccount2, 0, ',', '.') . "\n";
    echo "   - Saldo total sebelum: Rp " . number_format($totalBalanceBefore, 0, ',', '.') . "\n";
    echo "   - Saldo total sesudah: Rp " . number_format($totalBalanceAfter2, 0, ',', '.') . "\n";
    
    $expectedBalance = $totalBalanceBefore + $balanceAccount2;
    if ($totalBalanceAfter2 == $expectedBalance) {
        echo "   ✅ Test 2 PASSED: Saldo total bertambah sesuai saldo akun yang diaktifkan\n\n";
    } else {
        echo "   ❌ Test 2 FAILED: Saldo total tidak sesuai (expected: Rp " . number_format($expectedBalance, 0, ',', '.') . ")\n\n";
    }
    
    $totalBalanceBefore = $totalBalanceAfter2; // Update untuk test berikutnya
} else {
    echo "⚠️  Test 2 SKIPPED: Tidak ada akun yang sedang tidak dihitung dalam saldo total\n\n";
}

// Test 3: Cek konsistensi dengan perhitungan manual
echo "🔄 Test 3: Validasi konsistensi perhitungan saldo total...\n";

$manualTotal = Account::where('user_id', $user->id)
    ->where('is_active', true)
    ->where('include_in_total', true)
    ->sum('current_balance');

$dashboardTotal = $dashboardService->getDashboardStats($user->id)['totalBalance'];

echo "   - Perhitungan manual: Rp " . number_format($manualTotal, 0, ',', '.') . "\n";
echo "   - Dashboard service: Rp " . number_format($dashboardTotal, 0, ',', '.') . "\n";

if ($manualTotal == $dashboardTotal) {
    echo "   ✅ Test 3 PASSED: Perhitungan konsisten antara manual dan dashboard service\n\n";
} else {
    echo "   ❌ Test 3 FAILED: Perhitungan tidak konsisten\n\n";
}

// Test 4: Cek akun nonaktif tidak dihitung meskipun include_in_total = true
$inactiveAccount = $accounts->where('is_active', false)->first();
if ($inactiveAccount) {
    echo "🔄 Test 4: Memastikan akun nonaktif tidak dihitung meskipun include_in_total = true...\n";
    
    $originalIncludeStatus = $inactiveAccount->include_in_total;
    $inactiveAccount->update(['include_in_total' => true]);
    
    $totalBeforeInactive = $dashboardService->getDashboardStats($user->id)['totalBalance'];
    
    // Akun nonaktif tidak boleh mempengaruhi saldo total
    if ($totalBeforeInactive == $totalBalanceBefore) {
        echo "   ✅ Test 4 PASSED: Akun nonaktif tidak mempengaruhi saldo total\n\n";
    } else {
        echo "   ❌ Test 4 FAILED: Akun nonaktif mempengaruhi saldo total\n\n";
    }
    
    // Restore original status
    $inactiveAccount->update(['include_in_total' => $originalIncludeStatus]);
} else {
    echo "⚠️  Test 4 SKIPPED: Tidak ada akun nonaktif untuk testing\n\n";
}

// Tampilkan status akhir
echo "📊 STATUS AKHIR:\n";
$finalAccounts = Account::where('user_id', $user->id)->get();
foreach ($finalAccounts as $account) {
    $includeStatus = $account->include_in_total ? '✅ Dihitung' : '❌ Tidak Dihitung';
    $activeStatus = $account->is_active ? '🟢 Aktif' : '🔴 Nonaktif';
    echo "   - {$account->name}: Rp " . number_format($account->current_balance, 0, ',', '.') . " | {$activeStatus} | {$includeStatus}\n";
}

$finalStats = $dashboardService->getDashboardStats($user->id);
echo "\n💰 Saldo Total Akhir: Rp " . number_format($finalStats['totalBalance'], 0, ',', '.') . "\n";

echo "\n=== TESTING SELESAI ===\n";

// Cleanup - restore original states jika diperlukan
echo "\n🧹 Catatan: Jika perlu mengembalikan status asli akun, lakukan secara manual melalui UI.\n";
