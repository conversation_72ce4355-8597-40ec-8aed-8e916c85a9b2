@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON><PERSON>')

@section('page-title', '💸 Kate<PERSON>')

@push('styles')
<style>
    /* Dashboard container styling sesuai referensi */
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    /* Theme support untuk breadcrumb */
    body[data-theme="solarized-dark"] .breadcrumb-item a {
        color: #b58900 !important;
    }
    
    body[data-theme="solarized-dark"] .breadcrumb-item.active {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item a {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item.active {
        color: #ffffff !important;
    }
    
    /* Theme support untuk h2 */
    body[data-theme="solarized-dark"] h2 {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] h2 {
        color: #ffffff !important;
    }
    
    /* Theme support untuk small text */
    body[data-theme="solarized-dark"] .text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="solarized-dark"] .small.text-muted,
    body[data-theme="solarized-dark"] small.text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="synth-wave"] .text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .small.text-muted,
    body[data-theme="synth-wave"] small.text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .small {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .card-text.text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .card-text.small {
        color: #cccccc !important;
    }

    /* Theme support untuk cards - Red theme untuk expense */
    body[data-theme="solarized-dark"] .expense-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
    }
    
    body[data-theme="synth-wave"] .expense-card {
        background: rgba(255, 0, 102, 0.1);
        border: 1px solid #ff0066;
        color: #ffffff;
    }
    
    body[data-theme="synth-wave"] .expense-card .card-title,
    body[data-theme="synth-wave"] .expense-card .card-text {
        color: #ffffff !important;
    }
    
    /* Default cream theme with red accent untuk expense */
    .expense-card {
        background: #fdf6e3;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .expense-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
    }

    /* Stats card styling sesuai dengan income */
    .stats-card {
        background: #fdf6e3;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #333;
        padding: 20px;
        margin-bottom: 25px;
        transition: all 0.3s ease;
    }
    
    /* Theme support untuk stats card */
    body[data-theme="solarized-dark"] .stats-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
        color: #333;
    }
    
    body[data-theme="synth-wave"] .stats-card {
        background: rgba(255, 0, 102, 0.1);
        border: 1px solid #ff0066;
        color: #ffffff;
    }

    /* Button styling sesuai dengan income */
    .btn-add {
        background: #fdf6e3;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 12px 30px;
        color: #333;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        color: #333;
        text-decoration: none;
    }
    
    /* Theme support untuk btn-add */
    body[data-theme="solarized-dark"] .btn-add {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
        color: #333;
    }
    
    body[data-theme="solarized-dark"] .btn-add:hover {
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        color: #333;
    }
    
    body[data-theme="synth-wave"] .btn-add {
        background: linear-gradient(45deg, #ff1493, #ff0066);
        color: #000000;
        text-shadow: none;
        box-shadow: 0 0 20px rgba(255, 20, 147, 0.5);
    }
    
    body[data-theme="synth-wave"] .btn-add:hover {
        box-shadow: 0 0 30px rgba(255, 20, 147, 0.8);
        color: #000000;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        text-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        position: relative;
    }
    
    .category-icon i {
        font-size: 24px !important;
        font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome" !important;
        font-weight: 900 !important;
        color: white !important;
        display: inline-block !important;
        line-height: 1 !important;
        width: 24px;
        text-align: center;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-rendering: auto !important;
    }
    
    /* FontAwesome fallback untuk emoji di category icons */
    body.fa-fallback .category-icon i::before {
        font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI Symbol", "Noto Emoji" !important;
        font-weight: normal !important;
        font-size: 20px !important;
    }
    
    /* Fallback emoji untuk expense icons - Makanan & Minuman */
    body.fa-fallback .category-icon .fa-utensils::before { content: "🍽️"; }
    body.fa-fallback .category-icon .fa-coffee::before { content: "☕"; }
    body.fa-fallback .category-icon .fa-wine-glass::before { content: "🍷"; }
    body.fa-fallback .category-icon .fa-birthday-cake::before { content: "🎂"; }
    body.fa-fallback .category-icon .fa-pizza-slice::before { content: "🍕"; }
    body.fa-fallback .category-icon .fa-hamburger::before { content: "🍔"; }
    body.fa-fallback .category-icon .fa-ice-cream::before { content: "🍦"; }
    body.fa-fallback .category-icon .fa-apple-alt::before { content: "🍎"; }
    
    /* Transportasi */
    body.fa-fallback .category-icon .fa-car::before { content: "🚗"; }
    body.fa-fallback .category-icon .fa-motorcycle::before { content: "🏍️"; }
    body.fa-fallback .category-icon .fa-bus::before { content: "🚌"; }
    body.fa-fallback .category-icon .fa-train::before { content: "🚆"; }
    body.fa-fallback .category-icon .fa-plane::before { content: "✈️"; }
    body.fa-fallback .category-icon .fa-bicycle::before { content: "🚲"; }
    body.fa-fallback .category-icon .fa-taxi::before { content: "🚕"; }
    body.fa-fallback .category-icon .fa-gas-pump::before { content: "⛽"; }
    body.fa-fallback .category-icon .fa-parking::before { content: "🅿️"; }
    body.fa-fallback .category-icon .fa-ship::before { content: "🚢"; }
    body.fa-fallback .category-icon .fa-truck::before { content: "🚛"; }
    body.fa-fallback .category-icon .fa-subway::before { content: "🚇"; }
    
    /* Belanja */
    body.fa-fallback .category-icon .fa-shopping-cart::before { content: "🛒"; }
    body.fa-fallback .category-icon .fa-shopping-bag::before { content: "🛍️"; }
    body.fa-fallback .category-icon .fa-store::before { content: "🏪"; }
    body.fa-fallback .category-icon .fa-tshirt::before { content: "👕"; }
    body.fa-fallback .category-icon .fa-shoe-prints::before { content: "👟"; }
    body.fa-fallback .category-icon .fa-glasses::before { content: "👓"; }
    body.fa-fallback .category-icon .fa-watch::before { content: "⌚"; }
    body.fa-fallback .category-icon .fa-gem::before { content: "💎"; }
    body.fa-fallback .category-icon .fa-ring::before { content: "💍"; }
    body.fa-fallback .category-icon .fa-handbag::before { content: "👜"; }
    
    /* Rumah & Utilitas */
    body.fa-fallback .category-icon .fa-home::before { content: "🏠"; }
    body.fa-fallback .category-icon .fa-bolt::before { content: "⚡"; }
    body.fa-fallback .category-icon .fa-water::before { content: "💧"; }
    body.fa-fallback .category-icon .fa-wifi::before { content: "📶"; }
    body.fa-fallback .category-icon .fa-phone::before { content: "📱"; }
    body.fa-fallback .category-icon .fa-tv::before { content: "📺"; }
    body.fa-fallback .category-icon .fa-couch::before { content: "🛋️"; }
    body.fa-fallback .category-icon .fa-bed::before { content: "🛏️"; }
    body.fa-fallback .category-icon .fa-bath::before { content: "🛁"; }
    body.fa-fallback .category-icon .fa-hammer::before { content: "🔨"; }
    body.fa-fallback .category-icon .fa-tools::before { content: "🔧"; }
    body.fa-fallback .category-icon .fa-paint-roller::before { content: "🎨"; }
    
    /* Kesehatan & Kecantikan */
    body.fa-fallback .category-icon .fa-medkit::before { content: "🩹"; }
    body.fa-fallback .category-icon .fa-user-md::before { content: "👨‍⚕️"; }
    body.fa-fallback .category-icon .fa-hospital::before { content: "🏥"; }
    body.fa-fallback .category-icon .fa-pills::before { content: "💊"; }
    body.fa-fallback .category-icon .fa-syringe::before { content: "💉"; }
    body.fa-fallback .category-icon .fa-heartbeat::before { content: "💓"; }
    body.fa-fallback .category-icon .fa-eye::before { content: "👁️"; }
    body.fa-fallback .category-icon .fa-tooth::before { content: "🦷"; }
    body.fa-fallback .category-icon .fa-spa::before { content: "🧖‍♀️"; }
    body.fa-fallback .category-icon .fa-cut::before { content: "✂️"; }
    
    /* Hiburan & Rekreasi */
    body.fa-fallback .category-icon .fa-gamepad::before { content: "🎮"; }
    body.fa-fallback .category-icon .fa-film::before { content: "🎬"; }
    body.fa-fallback .category-icon .fa-music::before { content: "🎵"; }
    body.fa-fallback .category-icon .fa-headphones::before { content: "🎧"; }
    body.fa-fallback .category-icon .fa-camera::before { content: "📷"; }
    body.fa-fallback .category-icon .fa-guitar::before { content: "🎸"; }
    body.fa-fallback .category-icon .fa-microphone::before { content: "🎤"; }
    body.fa-fallback .category-icon .fa-ticket-alt::before { content: "🎫"; }
    body.fa-fallback .category-icon .fa-bowling-ball::before { content: "🎳"; }
    body.fa-fallback .category-icon .fa-football-ball::before { content: "🏈"; }
    body.fa-fallback .category-icon .fa-basketball-ball::before { content: "🏀"; }
    body.fa-fallback .category-icon .fa-swimming-pool::before { content: "🏊‍♂️"; }
    
    /* Pendidikan & Buku */
    body.fa-fallback .category-icon .fa-book::before { content: "📚"; }
    body.fa-fallback .category-icon .fa-graduation-cap::before { content: "🎓"; }
    body.fa-fallback .category-icon .fa-university::before { content: "🏛️"; }
    body.fa-fallback .category-icon .fa-pen::before { content: "✒️"; }
    body.fa-fallback .category-icon .fa-pencil-alt::before { content: "✏️"; }
    body.fa-fallback .category-icon .fa-calculator::before { content: "🧮"; }
    body.fa-fallback .category-icon .fa-microscope::before { content: "🔬"; }
    body.fa-fallback .category-icon .fa-flask::before { content: "⚗️"; }
    body.fa-fallback .category-icon .fa-chalkboard-teacher::before { content: "👨‍🏫"; }
    
    /* Olahraga & Fitness */
    body.fa-fallback .category-icon .fa-dumbbell::before { content: "🏋️"; }
    body.fa-fallback .category-icon .fa-running::before { content: "🏃‍♂️"; }
    body.fa-fallback .category-icon .fa-swimmer::before { content: "🏊‍♂️"; }
    body.fa-fallback .category-icon .fa-walking::before { content: "🚶‍♂️"; }
    body.fa-fallback .category-icon .fa-skiing::before { content: "⛷️"; }
    body.fa-fallback .category-icon .fa-horse::before { content: "🐎"; }
    body.fa-fallback .category-icon .fa-table-tennis::before { content: "🏓"; }
    body.fa-fallback .category-icon .fa-golf-ball::before { content: "⛳"; }
    body.fa-fallback .category-icon .fa-volleyball-ball::before { content: "🏐"; }
    
    /* Keuangan & Asuransi */
    body.fa-fallback .category-icon .fa-credit-card::before { content: "💳"; }
    body.fa-fallback .category-icon .fa-money-bill::before { content: "💵"; }
    body.fa-fallback .category-icon .fa-coins::before { content: "🪙"; }
    body.fa-fallback .category-icon .fa-shield-alt::before { content: "🛡️"; }
    body.fa-fallback .category-icon .fa-file-invoice-dollar::before { content: "🧾"; }
    
    /* Lainnya */
    body.fa-fallback .category-icon .fa-gift::before { content: "🎁"; }
    body.fa-fallback .category-icon .fa-heart::before { content: "❤️"; }
    body.fa-fallback .category-icon .fa-paw::before { content: "🐾"; }
    body.fa-fallback .category-icon .fa-seedling::before { content: "🌱"; }
    body.fa-fallback .category-icon .fa-recycle::before { content: "♻️"; }
    body.fa-fallback .category-icon .fa-envelope::before { content: "✉️"; }
    body.fa-fallback .category-icon .fa-newspaper::before { content: "📰"; }
    body.fa-fallback .category-icon .fa-clipboard-list::before { content: "📋"; }
    body.fa-fallback .category-icon .fa-calendar::before { content: "📅"; }
    body.fa-fallback .category-icon .fa-clock::before { content: "🕐"; }
    
    body[data-theme="synth-wave"] .category-icon {
        box-shadow: 0 0 20px rgba(255, 0, 102, 0.5);
    }
    
    /* Button styling */
    .btn-sm {
        font-size: 0.775rem;
        padding: 0.25rem 0.5rem;
    }
    
    .btn-outline-primary {
        color: #0d6efd;
        border-color: #0d6efd;
    }
    
    .btn-outline-primary:hover {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;
    }
    
    .btn-outline-warning {
        color: #ffc107;
        border-color: #ffc107;
    }
    
    .btn-outline-warning:hover {
        color: #000;
        background-color: #ffc107;
        border-color: #ffc107;
    }
    
    .btn-outline-success {
        color: #198754;
        border-color: #198754;
    }
    
    .btn-outline-success:hover {
        color: #fff;
        background-color: #198754;
        border-color: #198754;
    }
    
    .btn-outline-danger {
        color: #dc3545;
        border-color: #dc3545;
    }
    
    .btn-outline-danger:hover {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
    }
    
    .btn-outline-secondary {
        color: #6c757d;
        border-color: #6c757d;
    }
    
    /* Theme support untuk buttons */
    body[data-theme="synth-wave"] .btn-outline-primary {
        color: #00ffff;
        border-color: #00ffff;
    }
    
    body[data-theme="synth-wave"] .btn-outline-primary:hover {
        color: #000;
        background-color: #00ffff;
        border-color: #00ffff;
    }
    
    body[data-theme="synth-wave"] .btn-outline-warning {
        color: #ffff00;
        border-color: #ffff00;
    }
    
    body[data-theme="synth-wave"] .btn-outline-warning:hover {
        color: #000;
        background-color: #ffff00;
        border-color: #ffff00;
    }
    
    body[data-theme="synth-wave"] .btn-outline-success {
        color: #00ff00;
        border-color: #00ff00;
    }
    
    body[data-theme="synth-wave"] .btn-outline-success:hover {
        color: #000;
        background-color: #00ff00;
        border-color: #00ff00;
    }
    
    body[data-theme="synth-wave"] .btn-outline-danger {
        color: #ff0066;
        border-color: #ff0066;
    }
    
    body[data-theme="synth-wave"] .btn-outline-danger:hover {
        color: #fff;
        background-color: #ff0066;
        border-color: #ff0066;
    }
    


    
    /* Animation untuk cards */
    .expense-card {
        animation: fadeInUp 0.5s ease-out;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .expense-card {
            margin-bottom: 15px;
        }
        
        .stats-card {
            padding: 20px;
        }
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('dashboard') }}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="#" class="text-decoration-none">
                    <i class="fas fa-tags me-1"></i>Kategori
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-arrow-down me-1"></i>Pengeluaran
            </li>
        </ol>
    </nav>

    <!-- Stats Card -->
    <div class="stats-card">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="fw-bold fs-4">{{ $stats['total_categories'] }}</div>
                <div class="small">Total Kategori</div>
            </div>
            <div class="col-md-4">
                <div class="fw-bold fs-4">{{ $stats['active_categories'] }}</div>
                <div class="small">Kategori Aktif</div>
            </div>
            <div class="col-md-4">
                <div class="fw-bold fs-4">{{ $stats['inactive_categories'] }}</div>
                <div class="small">Kategori Nonaktif</div>
            </div>
        </div>
    </div>

    <!-- Header dengan tombol tambah -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-arrow-down text-danger me-2"></i>
            Kategori Pengeluaran
        </h2>
        <a href="{{ route('categories.expense.create') }}" class="btn btn-add">
            <i class="fas fa-plus me-2"></i>Tambah Kategori
        </a>
    </div>    <!-- Categories Grid -->
    <div class="row">
        @forelse($categories as $category)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card expense-card h-100">
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="category-icon me-3" style="background-color: {{ $category->color }};">
                                    <i class="{{ $category->icon }} fa-lg" style="color: white !important;"></i>
                                </div>
                                <div>
                                    <h5 class="card-title fw-bold mb-1">{{ $category->name }}</h5>
                                    <span class="badge {{ $category->is_active ? 'bg-success' : 'bg-secondary' }}">
                                        {{ $category->is_active ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex-grow-1">
                            <p class="card-text text-muted small mb-2">{{ $category->description ?: 'Tidak ada deskripsi' }}</p>
                            
                            <div class="mt-auto">
                                @if($category->budget_limit)
                                    <div class="small text-muted">
                                        <i class="fas fa-target me-1"></i>
                                        Budget: Rp {{ number_format($category->budget_limit, 0, ',', '.') }} / {{ $category->budget_period === 'monthly' ? 'Bulan' : ucfirst($category->budget_period) }}
                                    </div>
                                @endif
                                
                                <div class="small text-muted mt-1 mb-3">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    {{ $category->transactions->count() }} transaksi
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="d-flex gap-2 flex-wrap">
                                    <a href="{{ route('categories.expense.edit', $category->id) }}" class="btn btn-sm btn-outline-primary" title="Edit Kategori">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </a>
                                    <button class="btn btn-sm btn-outline-{{ $category->is_active ? 'warning' : 'success' }}" 
                                            onclick="toggleStatus({{ $category->id }}, {{ $category->is_active ? 'false' : 'true' }})"
                                            title="{{ $category->is_active ? 'Nonaktifkan' : 'Aktifkan' }} Kategori">
                                        <i class="fas fa-{{ $category->is_active ? 'eye-slash' : 'eye' }} me-1"></i>
                                        {{ $category->is_active ? 'Nonaktifkan' : 'Aktifkan' }}
                                    </button>
                                    @if($category->transactions->count() == 0)
                                        <button class="btn btn-sm btn-outline-danger delete-category" 
                                                data-id="{{ $category->id }}" 
                                                data-name="{{ $category->name }}"
                                                title="Hapus Kategori">
                                            <i class="fas fa-trash me-1"></i>Hapus
                                        </button>
                                    @else
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                disabled
                                                data-bs-toggle="tooltip" 
                                                title="Tidak dapat dihapus karena memiliki {{ $category->transactions->count() }} transaksi">
                                            <i class="fas fa-lock me-1"></i>Terkunci
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-folder-open text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">Belum ada kategori pengeluaran</h4>
                    <p class="text-muted">Mulai dengan menambahkan kategori pengeluaran pertama Anda</p>
                    <a href="{{ route('categories.expense.create') }}" class="btn btn-add">
                        <i class="fas fa-plus me-2"></i>Tambah Kategori Pertama
                    </a>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($categories->hasPages())
        <div class="d-flex justify-content-center mt-4">
            {{ $categories->appends(request()->query())->links() }}
        </div>
    @endif
</div>

<!-- Delete Form (hidden) -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if FontAwesome is loaded
    function isFontAwesomeLoaded() {
        const testElement = document.createElement('i');
        testElement.className = 'fas fa-heart';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement, ':before');
        const content = computedStyle.getPropertyValue('content');
        
        document.body.removeChild(testElement);
        
        // FontAwesome loaded if content is not "none" or empty
        return content && content !== 'none' && content !== '""';
    }
    
    // Add fallback class if FontAwesome is not loaded
    if (!isFontAwesomeLoaded()) {
        console.log('FontAwesome not detected, using emoji fallbacks');
        document.body.classList.add('fa-fallback');
    }

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Delete category functionality
    const deleteButtons = document.querySelectorAll('.delete-category');
    const deleteForm = document.getElementById('deleteForm');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.dataset.id;
            const categoryName = this.dataset.name;
            
            Swal.fire({
                title: 'Hapus Kategori?',
                text: `Kategori "${categoryName}" akan dihapus permanen!`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Menghapus Kategori',
                        text: 'Sedang memproses penghapusan...',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    
                    // Submit delete form
                    deleteForm.action = `/categories/expense/${categoryId}`;
                    
                    // Use AJAX instead of form submit
                    const formData = new FormData(deleteForm);
                    
                    fetch(deleteForm.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonColor: '#28a745',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                // Reload page or redirect
                                if (data.redirect) {
                                    window.location.href = data.redirect;
                                } else {
                                    window.location.reload();
                                }
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonColor: '#dc3545',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus kategori.',
                            icon: 'error',
                            confirmButtonColor: '#dc3545',
                            confirmButtonText: 'OK'
                        });
                    });
                }
            });
        });
    });
});

// Toggle category status function
function toggleStatus(categoryId, newStatus) {
    Swal.fire({
        title: newStatus ? 'Aktifkan Kategori?' : 'Nonaktifkan Kategori?',
        text: newStatus ? 'Kategori akan diaktifkan dan dapat digunakan untuk transaksi.' : 'Kategori akan dinonaktifkan dan tidak dapat digunakan untuk transaksi baru.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: newStatus ? '#28a745' : '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: newStatus ? 'Ya, Aktifkan!' : 'Ya, Nonaktifkan!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Memproses',
                text: 'Sedang mengubah status kategori...',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Make AJAX request
            fetch(`/categories/expense/${categoryId}/toggle-status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    is_active: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        // Reload page to show updated status
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message || 'Terjadi kesalahan saat mengubah status kategori.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengubah status kategori.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}
</script>
@endpush
