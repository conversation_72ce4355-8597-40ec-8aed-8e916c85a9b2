@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON><PERSON>')

@section('page-title', '💰 Kategori <PERSON>')

@push('styles')
<style>
    /* Dashboard container styling sesuai referensi */
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    /* Theme support untuk breadcrumb */
    body[data-theme="solarized-dark"] .breadcrumb-item a {
        color: #b58900 !important;
    }
    
    body[data-theme="solarized-dark"] .breadcrumb-item.active {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item a {
        color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .breadcrumb-item.active {
        color: #ffffff !important;
    }
    
    /* Theme support untuk h2 */
    body[data-theme="solarized-dark"] h2 {
        color: #657b83 !important;
    }
    
    body[data-theme="synth-wave"] h2 {
        color: #ffffff !important;
    }
    
    /* Theme support untuk small text */
    body[data-theme="solarized-dark"] .text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="solarized-dark"] .small.text-muted,
    body[data-theme="solarized-dark"] small.text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="solarized-dark"] .small {
        color: #93a1a1 !important;
    }
    
    body[data-theme="solarized-dark"] .card-text.text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="solarized-dark"] .card-text.small {
        color: #93a1a1 !important;
    }
    
    body[data-theme="synth-wave"] .text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .small.text-muted,
    body[data-theme="synth-wave"] small.text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .small {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .card-text.text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .card-text.small {
        color: #cccccc !important;
    }

    /* Theme support untuk cards - Green theme untuk income */
    body[data-theme="solarized-dark"] .income-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
    }
    
    body[data-theme="synth-wave"] .income-card {
        background: rgba(0, 255, 255, 0.1);
        border: 1px solid #00ffff;
        color: #ffffff;
    }
    
    body[data-theme="synth-wave"] .income-card .card-title,
    body[data-theme="synth-wave"] .income-card .card-text {
        color: #ffffff !important;
    }
    
    /* Default cream theme with green accent untuk income */
    .income-card {
        background: #fdf6e3;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .income-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
    }
    
    .category-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 15px;
        flex-shrink: 0;
    }
    
    .category-icon i {
        font-size: 24px !important;
        color: white !important;
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro" !important;
        font-weight: 900 !important;
        display: inline-block !important;
        line-height: 1 !important;
        text-align: center !important;
        width: auto !important;
    }
    
    .category-icon .emoji-fallback {
        font-size: 24px;
        line-height: 1;
        display: none;
    }
    
    /* FontAwesome fallback untuk emoji */
    body.fa-fallback .category-icon i {
        display: none !important;
    }
    
    body.fa-fallback .category-icon .emoji-fallback {
        display: block !important;
    }
    
    .stats-card {
        background: #fdf6e3;
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #333;
        padding: 20px;
        margin-bottom: 25px;
        transition: all 0.3s ease;
    }
    
    .btn-add {
        background: #fdf6e3;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 12px 30px;
        color: #333;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        color: #333;
        text-decoration: none;
    }
    
    /* Theme support untuk stats card dan btn-add */
    body[data-theme="solarized-dark"] .stats-card {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
        color: #333;
    }
    
    body[data-theme="solarized-dark"] .btn-add {
        background: #fdf6e3;
        border: 1px solid #eee8d5;
        color: #333;
    }
    
    body[data-theme="solarized-dark"] .btn-add:hover {
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        color: #333;
    }
    
    body[data-theme="synth-wave"] .stats-card {
        background: rgba(0, 255, 255, 0.1);
        border: 1px solid #00ffff;
        color: #ffffff;
        box-shadow: 0 20px 60px rgba(0, 255, 255, 0.2);
    }
    
    body[data-theme="synth-wave"] .btn-add {
        background: rgba(0, 255, 255, 0.1);
        border: 1px solid #00ffff;
        color: #ffffff;
        box-shadow: 0 8px 20px rgba(0, 255, 255, 0.2);
    }
    
    body[data-theme="synth-wave"] .btn-add:hover {
        box-shadow: 0 12px 30px rgba(0, 255, 255, 0.3);
        color: #ffffff;
        transform: translateY(-2px) scale(1.02);
    }
    }
    
    /* Theme specific category icon */
    body[data-theme="solarized-dark"] .category-icon {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    body[data-theme="synth-wave"] .category-icon {
        box-shadow: 0 0 15px rgba(0,255,255,0.3);
        border: 2px solid rgba(0,255,255,0.5);
    }
    
    /* Dropdown styling */
    .dropdown-menu {
        border-radius: 10px;
        border: none;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        padding: 10px;
    }
    
    .dropdown-item {
        border-radius: 8px;
        padding: 10px 15px;
        margin-bottom: 5px;
        transition: all 0.3s ease;
    }
    
    .dropdown-item:hover {
        background: #f8f9fa;
        transform: translateX(5px);
    }
    
    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-right: 10px;
    }
    
    /* Theme support untuk dropdown */
    body[data-theme="synth-wave"] .dropdown-menu {
        background: rgba(0, 20, 40, 0.95) !important;
        border: 1px solid #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .dropdown-item {
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .dropdown-item:hover {
        background: rgba(0, 255, 255, 0.2) !important;
        color: #00ffff !important;
    }
    
    /* Badge styling */
    .badge {
        font-size: 0.75em;
        padding: 0.5em 0.75em;
        border-radius: 10px;
    }
    
    /* Theme support untuk badge */
    body[data-theme="solarized-dark"] .badge.bg-success {
        background-color: #859900 !important;
    }
    
    body[data-theme="solarized-dark"] .badge.bg-secondary {
        background-color: #6c757d !important;
    }
    
    body[data-theme="synth-wave"] .badge.bg-success {
        background: linear-gradient(45deg, #00ffff, #0080ff) !important;
        color: #000000 !important;
        font-weight: 600;
    }
    
    body[data-theme="synth-wave"] .badge.bg-secondary {
        background: rgba(108, 117, 125, 0.5) !important;
        color: #ffffff !important;
        border: 1px solid #6c757d;
    }
    
    /* Action buttons styling */
    .card .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.785rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .card .btn-sm:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    /* Theme support untuk action buttons */
    body[data-theme="synth-wave"] .card .btn-outline-primary {
        color: #00ffff !important;
        border-color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-primary:hover {
        background-color: #00ffff !important;
        color: #000000 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-warning {
        color: #ffc107 !important;
        border-color: #ffc107 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-warning:hover {
        background-color: #ffc107 !important;
        color: #000000 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-success {
        color: #00ff00 !important;
        border-color: #00ff00 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-success:hover {
        background-color: #00ff00 !important;
        color: #000000 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-danger {
        color: #ff1493 !important;
        border-color: #ff1493 !important;
    }
    
    body[data-theme="synth-wave"] .card .btn-outline-danger:hover {
        background-color: #ff1493 !important;
        color: #ffffff !important;
    }
    
    /* Enhanced text-muted styling untuk semua tema */
    body[data-theme="solarized-dark"] .text-muted,
    body[data-theme="solarized-dark"] .small.text-muted,
    body[data-theme="solarized-dark"] small.text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="synth-wave"] .text-muted,
    body[data-theme="synth-wave"] .small.text-muted,
    body[data-theme="synth-wave"] small.text-muted {
        color: #cccccc !important;
    }
    
    /* SweetAlert2 theme support */
    body[data-theme="solarized-dark"] .swal2-container {
        background: rgba(0, 43, 54, 0.9) !important;
    }
    
    body[data-theme="solarized-dark"] .swal2-popup {
        background: #fdf6e3 !important;
        color: #657b83 !important;
    }
    
    body[data-theme="solarized-dark"] .swal2-title {
        color: #268bd2 !important;
    }
    
    body[data-theme="solarized-dark"] .swal2-content {
        color: #657b83 !important;
    }
    
    body[data-theme="solarized-dark"] .swal2-progress-steps {
        background: #eee8d5 !important;
    }
    
    body[data-theme="solarized-dark"] .text-muted {
        color: #93a1a1 !important;
    }
    
    body[data-theme="synth-wave"] .swal2-container {
        background: rgba(0, 0, 0, 0.9) !important;
    }
    
    body[data-theme="synth-wave"] .swal2-popup {
        background: rgba(0, 20, 40, 0.95) !important;
        border: 1px solid #00ffff !important;
        color: #ffffff !important;
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.3) !important;
    }
    
    body[data-theme="synth-wave"] .swal2-title {
        color: #00ffff !important;
        text-shadow: 0 0 10px #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .swal2-content {
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .swal2-progress-steps {
        background: rgba(0, 255, 255, 0.2) !important;
        border: 1px solid #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .text-muted {
        color: #cccccc !important;
    }
    
    body[data-theme="synth-wave"] .swal2-confirm {
        background: linear-gradient(45deg, #ff1493, #00ffff) !important;
        color: #000000 !important;
        border: none !important;
    }
    
    body[data-theme="synth-wave"] .swal2-cancel {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
        border: 1px solid #ffffff !important;
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('dashboard') }}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="#" class="text-decoration-none">
                    <i class="fas fa-tags me-1"></i>Kategori
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-arrow-up me-1"></i>Pemasukan
            </li>
        </ol>
    </nav>

    <!-- Stats Card -->
    <div class="stats-card">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="fw-bold fs-4">{{ $stats['total_categories'] }}</div>
                <div class="small">Total Kategori</div>
            </div>
            <div class="col-md-4">
                <div class="fw-bold fs-4">{{ $stats['active_categories'] }}</div>
                <div class="small">Kategori Aktif</div>
            </div>
            <div class="col-md-4">
                <div class="fw-bold fs-4">{{ $stats['inactive_categories'] }}</div>
                <div class="small">Kategori Nonaktif</div>
            </div>
        </div>
    </div>

    <!-- Header dengan tombol tambah -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-arrow-up text-success me-2"></i>
            Kategori Pemasukan
        </h2>
        <a href="{{ route('categories.income.create') }}" class="btn btn-add">
            <i class="fas fa-plus me-2"></i>Tambah Kategori
        </a>
    </div>

    <!-- Categories Grid -->
    <div class="row">
        @forelse($categories as $category)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card income-card h-100">
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="category-icon me-3" style="background-color: {{ $category->color }};">
                                    <i class="{{ $category->icon }} fa-lg" style="color: white !important;"></i>
                                    <span class="emoji-fallback">
                                        @switch($category->icon)
                                            @case('fas fa-dollar-sign') $ @break
                                            @case('fas fa-coins') 🪙 @break
                                            @case('fas fa-money-bill-wave') 💵 @break
                                            @case('fas fa-chart-line') 📈 @break
                                            @case('fas fa-briefcase') 💼 @break
                                            @case('fas fa-laptop') 💻 @break
                                            @case('fas fa-graduation-cap') 🎓 @break
                                            @case('fas fa-gift') 🎁 @break
                                            @case('fas fa-home') 🏠 @break
                                            @case('fas fa-car') 🚗 @break
                                            @case('fas fa-credit-card') 💳 @break
                                            @case('fas fa-piggy-bank') 🐷 @break
                                            @default 💰
                                        @endswitch
                                    </span>
                                    <small class="debug-info" style="font-size: 10px; color: rgba(255,255,255,0.7); position: absolute; bottom: -15px; left: 0; right: 0; text-align: center;">{{ $category->icon }}</small>
                                </div>
                                <div>
                                    <h5 class="card-title fw-bold mb-1">{{ $category->name }}</h5>
                                    <span class="badge {{ $category->is_active ? 'bg-success' : 'bg-secondary' }}">
                                        {{ $category->is_active ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex-grow-1">
                            <p class="card-text text-muted small mb-2">{{ $category->description }}</p>
                            
                            <div class="mt-auto">
                                @if($category->budget_limit)
                                    <div class="small text-muted">
                                        <i class="fas fa-target me-1"></i>
                                        Target: Rp {{ number_format($category->budget_limit, 0, ',', '.') }} / {{ $category->budget_period === 'monthly' ? 'Bulan' : ucfirst($category->budget_period) }}
                                    </div>
                                @endif
                                
                                <div class="small text-muted mt-1 mb-3">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    {{ $category->transactions_count ?? 0 }} transaksi
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="d-flex gap-2 flex-wrap">
                                    <a href="{{ route('categories.income.edit', $category->id) }}" class="btn btn-sm btn-outline-primary" title="Edit Kategori">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </a>
                                    <button class="btn btn-sm btn-outline-{{ $category->is_active ? 'warning' : 'success' }}" 
                                            onclick="toggleStatus({{ $category->id }}, {{ $category->is_active ? 'false' : 'true' }})"
                                            title="{{ $category->is_active ? 'Nonaktifkan' : 'Aktifkan' }} Kategori">
                                        <i class="fas fa-{{ $category->is_active ? 'eye-slash' : 'eye' }} me-1"></i>
                                        {{ $category->is_active ? 'Nonaktifkan' : 'Aktifkan' }}
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteCategory({{ $category->id }}, '{{ $category->name }}')"
                                            title="Hapus Kategori">
                                        <i class="fas fa-trash me-1"></i>Hapus
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-folder-open text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">Belum ada kategori pemasukan</h4>
                    <p class="text-muted">Mulai dengan menambahkan kategori pemasukan pertama Anda</p>
                    <a href="{{ route('categories.income.create') }}" class="btn btn-add">
                        <i class="fas fa-plus me-2"></i>Tambah Kategori Pertama
                    </a>
                </div>
            </div>
        @endforelse
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Force initialize Bootstrap dropdowns
    setTimeout(function() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
            console.log('Initializing Bootstrap dropdowns...');
            var dropdownElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
            console.log('Bootstrap dropdowns initialized:', dropdownList.length);
        } else {
            console.log('Bootstrap not available, using manual dropdown');
        }
    }, 100);
    
    // Check if FontAwesome is loaded
    function isFontAwesomeLoaded() {
        const testElement = document.createElement('i');
        testElement.className = 'fas fa-heart';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement, ':before');
        const content = computedStyle.getPropertyValue('content');
        
        document.body.removeChild(testElement);
        
        // FontAwesome loaded if content is not "none" or empty
        return content && content !== 'none' && content !== '""';
    }
    
    // Add fallback class if FontAwesome is not loaded
    if (!isFontAwesomeLoaded()) {
        console.log('FontAwesome not detected, using emoji fallbacks');
        document.body.classList.add('fa-fallback');
    }
    
    // Manual dropdown toggle as additional fallback
    document.addEventListener('click', function(e) {
        if (e.target.closest('[data-bs-toggle="dropdown"]')) {
            const button = e.target.closest('[data-bs-toggle="dropdown"]');
            const menu = button.nextElementSibling;
            
            if (menu && menu.classList.contains('dropdown-menu')) {
                e.preventDefault();
                e.stopPropagation();
                
                // Close all other dropdowns
                document.querySelectorAll('.dropdown-menu.show').forEach(otherMenu => {
                    if (otherMenu !== menu) {
                        otherMenu.classList.remove('show');
                    }
                });
                
                // Toggle current dropdown
                menu.classList.toggle('show');
                console.log('Dropdown toggled manually');
            }
        } else if (!e.target.closest('.dropdown-menu')) {
            // Close all dropdowns when clicking outside
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });
});

function toggleStatus(categoryId, newStatus) {
    const action = newStatus ? 'mengaktifkan' : 'menonaktifkan';
    const statusText = newStatus ? 'aktif' : 'nonaktif';
    const icon = newStatus ? 'success' : 'warning';
    
    Swal.fire({
        title: `${newStatus ? 'Aktifkan' : 'Nonaktifkan'} Kategori?`,
        html: `Apakah Anda yakin ingin ${action} kategori ini?<br><br><small class="text-muted">Kategori ${statusText} ${newStatus ? 'akan muncul' : 'tidak akan muncul'} di dropdown transaksi.</small>`,
        icon: icon,
        showCancelButton: true,
        confirmButtonColor: newStatus ? '#28a745' : '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: `Ya, ${newStatus ? 'Aktifkan' : 'Nonaktifkan'}!`,
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: `${newStatus ? 'Mengaktifkan' : 'Menonaktifkan'}...`,
                text: 'Sedang memproses perubahan status',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Send toggle request
            fetch(`/categories/${categoryId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ is_active: newStatus })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengubah status kategori.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}

function deleteCategory(categoryId, categoryName) {
    Swal.fire({
        title: 'Hapus Kategori?',
        html: `Apakah Anda yakin ingin menghapus kategori <strong>${categoryName}</strong>?<br><br><small class="text-muted">Tindakan ini tidak dapat dibatalkan!</small>`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus kategori',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Send delete request
            const formData = new FormData();
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
            formData.append('_method', 'DELETE');
            
            fetch(`/categories/income/${categoryId}`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menghapus kategori.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}
</script>
@endpush
