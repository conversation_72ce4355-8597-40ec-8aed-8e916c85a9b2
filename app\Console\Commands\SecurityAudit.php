<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SecurityLog;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class SecurityAudit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'security:audit {--detailed : Show detailed security report}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run security audit and show security status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔒 SECURITY AUDIT REPORT');
        $this->info('========================');
        
        // User Security Status
        $this->auditUsers();
        
        // Recent Security Events
        $this->auditSecurityEvents();
        
        // Failed Login Attempts
        $this->auditFailedLogins();
        
        // Account Locks
        $this->auditAccountLocks();
        
        // Suspicious Activities
        $this->auditSuspiciousActivities();
        
        // Password Security
        $this->auditPasswordSecurity();
        
        if ($this->option('detailed')) {
            $this->detailedSecurityReport();
        }
        
        $this->info('========================');
        $this->info('✅ Security audit completed');
        
        return Command::SUCCESS;
    }
    
    private function auditUsers()
    {
        $this->info('📊 USER SECURITY STATUS');
        $this->line('------------------------');
        
        $totalUsers = User::count();
        $activeUsers = User::where('is_active', true)->count();
        $verifiedUsers = User::whereNotNull('email_verified_at')->count();
        $lockedUsers = User::whereNotNull('locked_until')
                          ->where('locked_until', '>', now())
                          ->count();
        $usersWithOldPasswords = User::where('password_changed_at', '<', now()->subDays(90))
                                   ->orWhereNull('password_changed_at')
                                   ->count();
        
        $this->table(['Metric', 'Count', 'Percentage'], [
            ['Total Users', $totalUsers, '100%'],
            ['Active Users', $activeUsers, $totalUsers > 0 ? round(($activeUsers/$totalUsers)*100, 1).'%' : '0%'],
            ['Verified Users', $verifiedUsers, $totalUsers > 0 ? round(($verifiedUsers/$totalUsers)*100, 1).'%' : '0%'],
            ['Locked Users', $lockedUsers, $totalUsers > 0 ? round(($lockedUsers/$totalUsers)*100, 1).'%' : '0%'],
            ['Users with Old Passwords', $usersWithOldPasswords, $totalUsers > 0 ? round(($usersWithOldPasswords/$totalUsers)*100, 1).'%' : '0%'],
        ]);
        
        if ($lockedUsers > 0) {
            $this->warn("⚠️  {$lockedUsers} users are currently locked");
        }
        
        if ($usersWithOldPasswords > 0) {
            $this->warn("⚠️  {$usersWithOldPasswords} users have old passwords (>90 days)");
        }
        
        $this->line('');
    }
      private function auditSecurityEvents()
    {
        $this->info('🔍 RECENT SECURITY EVENTS (Last 24 hours)');
        $this->line('------------------------------------------');
        
        $events = SecurityLog::where('created_at', '>=', now()->subDay())
                            ->select('risk_level', DB::raw('count(*) as count'))
                            ->groupBy('risk_level')
                            ->get();
        
        if ($events->isEmpty()) {
            $this->line('No security events in the last 24 hours');
        } else {
            $this->table(['Risk Level', 'Count'], $events->map(function($event) {
                return [$event->risk_level, $event->count];
            }));
        }
        
        $this->line('');
    }
    
    private function auditFailedLogins()
    {
        $this->info('🚫 FAILED LOGIN ATTEMPTS (Last 24 hours)');
        $this->line('----------------------------------------');
        
        $failedLogins = SecurityLog::where('event_type', 'login_failed')
                                 ->where('created_at', '>=', now()->subDay())
                                 ->count();
        
        $failedLoginsByIP = SecurityLog::where('event_type', 'login_failed')
                                     ->where('created_at', '>=', now()->subDay())
                                     ->select('ip_address', DB::raw('count(*) as count'))
                                     ->groupBy('ip_address')
                                     ->orderBy('count', 'desc')
                                     ->limit(10)
                                     ->get();
        
        $this->line("Total failed login attempts: {$failedLogins}");
        
        if ($failedLoginsByIP->isNotEmpty()) {
            $this->line('Top IPs with failed attempts:');
            $this->table(['IP Address', 'Failed Attempts'], $failedLoginsByIP->map(function($item) {
                return [$item->ip_address, $item->count];
            }));
        }
        
        $this->line('');
    }
    
    private function auditAccountLocks()
    {
        $this->info('🔒 ACCOUNT LOCKS');
        $this->line('---------------');
        
        $currentlyLocked = User::whereNotNull('locked_until')
                              ->where('locked_until', '>', now())
                              ->count();
        
        $recentLocks = SecurityLog::where('event_type', 'account_locked')
                                 ->where('created_at', '>=', now()->subDay())
                                 ->count();
        
        $this->line("Currently locked accounts: {$currentlyLocked}");
        $this->line("New locks in last 24h: {$recentLocks}");
        
        if ($currentlyLocked > 0) {
            $lockedUsers = User::whereNotNull('locked_until')
                              ->where('locked_until', '>', now())
                              ->select('username', 'locked_until', 'login_attempts')
                              ->get();
            
            $this->table(['Username', 'Locked Until', 'Failed Attempts'], $lockedUsers->map(function($user) {
                return [
                    $user->username,
                    $user->locked_until->format('Y-m-d H:i:s'),
                    $user->login_attempts
                ];
            }));
        }
        
        $this->line('');
    }
    
    private function auditSuspiciousActivities()
    {
        $this->info('⚠️  SUSPICIOUS ACTIVITIES (Last 24 hours)');
        $this->line('----------------------------------------');
        
        $suspiciousEvents = [
            'sql_injection_attempt',
            'xss_attempt',
            'command_injection_attempt',
            'path_traversal_attempt',
            'concurrent_login_attempt',
            'suspicious_registration',
            'attack_tool_detected'
        ];
        
        $suspicious = SecurityLog::whereIn('event_type', $suspiciousEvents)
                                ->where('created_at', '>=', now()->subDay())
                                ->select('event_type', DB::raw('count(*) as count'))
                                ->groupBy('event_type')
                                ->get();
        
        if ($suspicious->isEmpty()) {
            $this->line('✅ No suspicious activities detected');
        } else {
            $this->table(['Event Type', 'Count'], $suspicious->map(function($event) {
                return [$event->event_type, $event->count];
            }));
            
            $this->warn('⚠️  Suspicious activities detected! Review security logs.');
        }
        
        $this->line('');
    }
    
    private function auditPasswordSecurity()
    {
        $this->info('🔐 PASSWORD SECURITY STATUS');
        $this->line('---------------------------');
        
        $weakPasswords = 0; // Would need to implement password strength checking
        $oldPasswords = User::where('password_changed_at', '<', now()->subDays(90))
                          ->orWhereNull('password_changed_at')
                          ->count();
        $recentPasswordChanges = SecurityLog::where('event_type', 'password_changed')
                                           ->where('created_at', '>=', now()->subWeek())
                                           ->count();
        
        $this->table(['Metric', 'Count'], [
            ['Users with old passwords (>90 days)', $oldPasswords],
            ['Password changes this week', $recentPasswordChanges],
        ]);
        
        if ($oldPasswords > 0) {
            $this->warn("⚠️  {$oldPasswords} users should change their passwords");
        }
        
        $this->line('');
    }
      private function detailedSecurityReport()
    {
        $this->info('📋 DETAILED SECURITY REPORT');
        $this->line('---------------------------');
        
        // Recent high/critical events
        $criticalEvents = SecurityLog::whereIn('risk_level', ['high', 'critical'])
                                   ->where('created_at', '>=', now()->subWeek())
                                   ->orderBy('created_at', 'desc')
                                   ->limit(20)
                                   ->get();
        
        if ($criticalEvents->isNotEmpty()) {
            $this->warn('Recent High/Critical Security Events:');
            $this->table(['Date', 'Type', 'Risk Level', 'Description'], $criticalEvents->map(function($event) {
                return [
                    $event->created_at->format('Y-m-d H:i'),
                    $event->event_type,
                    $event->risk_level,
                    substr($event->description, 0, 50) . (strlen($event->description) > 50 ? '...' : '')
                ];
            }));
        }
        
        // User security scores
        $this->info('User Security Scores (Top 10 highest risk):');
        $users = User::limit(10)->get();
        $userScores = [];
        
        foreach ($users as $user) {
            $score = $user->getSecurityScore();
            if ($score < 80) { // Only show users with lower security scores
                $userScores[] = [
                    $user->username,
                    $score . '/100',
                    $score >= 80 ? 'Good' : ($score >= 60 ? 'Fair' : 'Poor')
                ];
            }
        }
        
        if (!empty($userScores)) {
            $this->table(['Username', 'Security Score', 'Status'], $userScores);
        } else {
            $this->line('✅ All users have good security scores');
        }
    }
}
