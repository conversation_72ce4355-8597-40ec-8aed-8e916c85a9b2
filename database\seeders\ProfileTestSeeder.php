<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class ProfileTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test users with complete profile data
        User::create([
            'name' => 'Admin MyMoney',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'phone' => '081234567890',
            'gender' => 'L',
            'birth_date' => '1990-01-15',
            'bio' => 'Administrator aplikasi MyMoney. Mengelola keuangan dengan bijak dan efisien.',
            'address' => 'Jl. Raya Teknologi No. 123',
            'city' => 'Jakarta',
            'postal_code' => '12345',
            'country' => 'Indonesia',
            'timezone' => 'Asia/Jakarta',
            'language' => 'id',
            'theme' => 'light',
            'currency' => 'IDR',
            'date_format' => 'd/m/Y',
            'time_format' => '24',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
            'two_factor_enabled' => false,
            'notification_enabled' => true,
        ]);

        User::create([
            'name' => 'John Doe',
            'username' => 'johndoe',
            'email' => '<EMAIL>',
            'phone' => '081987654321',
            'gender' => 'L',
            'birth_date' => '1985-06-20',
            'bio' => 'Freelancer dan investor. Suka traveling dan fotografi. Percaya pada pentingnya financial planning.',
            'address' => 'Jl. Sudirman No. 45',
            'city' => 'Bandung',
            'postal_code' => '40123',
            'country' => 'Indonesia',
            'timezone' => 'Asia/Jakarta',
            'language' => 'en',
            'theme' => 'solarized-dark',
            'currency' => 'USD',
            'date_format' => 'm/d/Y',
            'time_format' => '12',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
            'two_factor_enabled' => true,
            'notification_enabled' => true,
        ]);

        User::create([
            'name' => 'Jane Smith',
            'username' => 'janesmith',
            'email' => '<EMAIL>',
            'phone' => '082123456789',
            'gender' => 'P',
            'birth_date' => '1992-03-10',
            'bio' => 'Content creator dan business owner. Passionate tentang digital marketing dan sustainable living.',
            'address' => 'Jl. Merdeka No. 67',
            'city' => 'Surabaya',
            'postal_code' => '60123',
            'country' => 'Indonesia', 
            'timezone' => 'Asia/Jakarta',
            'language' => 'id',
            'theme' => 'synth-wave',
            'currency' => 'IDR',
            'date_format' => 'Y-m-d',
            'time_format' => '24',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
            'two_factor_enabled' => false,
            'notification_enabled' => false,
        ]);

        // Create test user dengan data minimal
        User::create([
            'name' => 'Test User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Create user untuk testing edge cases
        User::create([
            'name' => 'Very Long Name That Exceeds Normal Length To Test Field Limits',
            'username' => 'verylongusername123',
            'email' => '<EMAIL>',
            'phone' => '+6281234567890123',
            'gender' => 'P',
            'birth_date' => '2000-12-31',
            'bio' => str_repeat('This is a very long bio text that tests the maximum character limit. ', 10),
            'address' => 'Jl. Very Long Street Name That Tests Address Field Length Limit No. 999, RT.001/RW.002, Kelurahan Test, Kecamatan Test, Kota Test',
            'city' => 'Very Long City Name For Testing',
            'postal_code' => '99999',
            'country' => 'Indonesia',
            'timezone' => 'Asia/Jakarta',
            'language' => 'id',
            'theme' => 'light',
            'currency' => 'IDR',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
    }
}
