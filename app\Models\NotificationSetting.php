<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationSetting extends Model
{
    protected $fillable = [
        'user_id',
        'email_enabled',
        'email_login_alerts',
        'email_security_alerts',
        'email_transaction_alerts',
        'email_budget_alerts',
        'email_weekly_summary',
        'email_monthly_report',
        'email_marketing',
        'web_enabled',
        'web_transaction_alerts',
        'web_budget_warnings',
        'web_goal_progress',
        'web_reminders',
        'push_enabled',
        'push_transaction_alerts',
        'push_security_alerts',
        'push_budget_warnings',
        'sms_enabled',
        'sms_security_alerts',
        'sms_critical_alerts',
        'sms_phone',
        'quiet_hours_start',
        'quiet_hours_end',
        'weekend_notifications',
        'summary_frequency',
        'budget_alert_frequency',
        'custom_keywords',
        'large_transaction_threshold',
        'notification_channels',
    ];

    protected $casts = [
        'email_enabled' => 'boolean',
        'email_login_alerts' => 'boolean',
        'email_security_alerts' => 'boolean',
        'email_transaction_alerts' => 'boolean',
        'email_budget_alerts' => 'boolean',
        'email_weekly_summary' => 'boolean',
        'email_monthly_report' => 'boolean',
        'email_marketing' => 'boolean',
        'web_enabled' => 'boolean',
        'web_transaction_alerts' => 'boolean',
        'web_budget_warnings' => 'boolean',
        'web_goal_progress' => 'boolean',
        'web_reminders' => 'boolean',
        'push_enabled' => 'boolean',
        'push_transaction_alerts' => 'boolean',
        'push_security_alerts' => 'boolean',
        'push_budget_warnings' => 'boolean',
        'sms_enabled' => 'boolean',
        'sms_security_alerts' => 'boolean',
        'sms_critical_alerts' => 'boolean',
        'weekend_notifications' => 'boolean',
        'custom_keywords' => 'array',
        'notification_channels' => 'array',
        'large_transaction_threshold' => 'decimal:2',
    ];

    /**
     * Get the user that owns the notification settings.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if notifications should be sent during quiet hours.
     */
    public function isInQuietHours(): bool
    {
        $now = now()->format('H:i');
        $start = $this->quiet_hours_start;
        $end = $this->quiet_hours_end;

        if ($start < $end) {
            return $now >= $start && $now <= $end;
        } else {
            return $now >= $start || $now <= $end;
        }
    }

    /**
     * Check if weekend notifications are allowed.
     */
    public function allowWeekendNotifications(): bool
    {
        if ($this->weekend_notifications) {
            return true;
        }

        return !now()->isWeekend();
    }

    /**
     * Get formatted notification channels.
     */
    public function getFormattedChannelsAttribute(): array
    {
        $channels = $this->notification_channels ?? [];
        $formatted = [];

        foreach ($channels as $channel => $config) {
            if (isset($config['enabled']) && $config['enabled']) {
                $formatted[] = [
                    'name' => $channel,
                    'label' => ucfirst($channel),
                    'config' => $config
                ];
            }
        }

        return $formatted;
    }
}
