<!DOCTYPE html>
<html>
<head>
    <title>Debug Create Account</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <h2>Debug Create Account</h2>
    <button onclick="testCreateAccount()">Test Create Account</button>
    <button onclick="testOriginalRoute()">Test Original Route</button>
    
    <div id="result"></div>

    <script>
    async function testCreateAccount() {
        const testData = {
            name: 'Debug Test Account ' + Date.now(),
            type: 'cash',
            initial_balance: 0,
            description: 'Debug test account',
            color: '#007bff',
            icon: 'fas fa-wallet',
            _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        };
        
        console.log('Sending data to debug route:', testData);
        
        try {
            const formData = new FormData();
            Object.keys(testData).forEach(key => {
                formData.append(key, testData[key]);
            });
            
            const response = await fetch('/debug/create-account-test', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': testData._token
                },
                credentials: 'same-origin'
            });
            
            console.log('Response status:', response.status);
            
            const responseData = await response.json();
            console.log('Response data:', responseData);
            
            document.getElementById('result').innerHTML = `
                <h3>Debug Route Response (Status: ${response.status})</h3>
                <pre>${JSON.stringify(responseData, null, 2)}</pre>
            `;
            
            if (responseData.success) {
                Swal.fire('Success!', responseData.message, 'success');
            } else {
                Swal.fire('Error!', responseData.message || 'Unknown error', 'error');
            }
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('result').innerHTML = `
                <h3>Error</h3>
                <pre>${error.message}</pre>
            `;
        }
    }
    
    async function testOriginalRoute() {
        const testData = {
            name: 'Original Test Account ' + Date.now(),
            type: 'cash',
            initial_balance: 0,
            description: 'Original route test account',
            color: '#007bff',
            icon: 'fas fa-wallet',
            _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            _ajax: '1'
        };
        
        console.log('Sending data to original route:', testData);
        
        try {
            const formData = new FormData();
            Object.keys(testData).forEach(key => {
                formData.append(key, testData[key]);
            });
            
            const response = await fetch('/accounts', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': testData._token
                },
                credentials: 'same-origin'
            });
            
            console.log('Response status:', response.status);
            
            const responseData = await response.json();
            console.log('Response data:', responseData);
            
            document.getElementById('result').innerHTML = `
                <h3>Original Route Response (Status: ${response.status})</h3>
                <pre>${JSON.stringify(responseData, null, 2)}</pre>
            `;
            
            if (responseData.success) {
                Swal.fire('Success!', responseData.message, 'success');
            } else {
                Swal.fire('Error!', responseData.message || 'Unknown error', 'error');
            }
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('result').innerHTML = `
                <h3>Error</h3>
                <pre>${error.message}</pre>
            `;
        }
    }
    </script>
</body>
</html>
