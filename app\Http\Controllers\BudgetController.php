<?php

namespace App\Http\Controllers;

use App\Models\Budget;
use App\Models\Category;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BudgetController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show budget dashboard page
     */
    public function index()
    {
        return view('budgets.index');
    }

    /**
     * Show create budget form
     */
    public function create()
    {
        $categories = Category::where('user_id', Auth::id())
                             ->where('type', 'expense')
                             ->where('is_active', true)
                             ->orderBy('name')
                             ->get();

        return view('budgets.create.index', compact('categories'));
    }

    /**
     * Show create budget form (legacy route)
     */
    public function createBudget()
    {
        return $this->create();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'category_id' => 'required|exists:categories,id',
                'amount' => 'required|string', 
                'type' => 'required|in:monthly,yearly,custom',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after:start_date',
                'description' => 'nullable|string|max:500',
                'alert_percentage' => 'nullable|integer|min:10|max:100',
                'alert_enabled' => 'nullable|boolean',
            ]);

            // Parse amount from currency format (remove non-numeric characters)
            $amount = (float) preg_replace('/[^0-9]/', '', $request->amount);

            if ($amount <= 0) {
                throw new \Exception('Jumlah anggaran harus lebih dari 0');
            }

            // Verify category belongs to user
            $category = Category::where('id', $request->category_id)
                              ->where('user_id', Auth::id())
                              ->where('type', 'expense')
                              ->firstOrFail();

            $startDate = Carbon::parse($request->start_date);
            $endDate = Carbon::parse($request->end_date);

            // Check for overlapping budget in same category
            $existingBudget = Budget::where('user_id', Auth::id())
                                  ->where('category_id', $request->category_id)
                                  ->where('is_active', true)
                                  ->where(function($query) use ($startDate, $endDate) {
                                      $query->whereBetween('start_date', [$startDate, $endDate])
                                            ->orWhereBetween('end_date', [$startDate, $endDate])
                                            ->orWhere(function($q) use ($startDate, $endDate) {
                                                $q->where('start_date', '<=', $startDate)
                                                  ->where('end_date', '>=', $endDate);
                                            });
                                  })
                                  ->exists();

            if ($existingBudget) {
                throw new \Exception('Sudah ada anggaran aktif untuk kategori ini pada periode yang sama.');
            }

            $budget = Budget::create([
                'user_id' => Auth::id(),
                'category_id' => $request->category_id,
                'name' => $request->name,
                'description' => $request->description,
                'amount' => $amount,
                'type' => $request->type,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'alert_percentage' => $request->alert_percentage ?? 80,
                'alert_enabled' => $request->has('alert_enabled'),
                'is_active' => true,
            ]);

            Log::info('Budget created successfully', [
                'budget_id' => $budget->id,
                'user_id' => Auth::id(),
                'amount' => $amount
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Anggaran berhasil dibuat!',
                    'redirect' => route('budgets.index')
                ]);
            }

            return redirect()->route('budgets.index')
                           ->with('success', 'Anggaran berhasil dibuat!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan validasi!',
                    'errors' => $e->errors()
                ], 422);
            }
            throw $e;
        } catch (\Exception $e) {
            Log::error('Error creating budget', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 500);
            }
            return back()->withErrors(['error' => $e->getMessage()])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        try {
            $budget = Budget::with('category')
                           ->where('id', $id)
                           ->where('user_id', Auth::id())
                           ->firstOrFail();
            
            $categories = Category::where('user_id', Auth::id())
                                 ->where('type', 'expense')
                                 ->where('is_active', true)
                                 ->orderBy('name')
                                 ->get();
            
            return response()->json([
                'success' => true,
                'budget' => $budget,
                'categories' => $categories
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error loading budget for edit: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Anggaran tidak ditemukan'
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'category_id' => 'nullable|exists:categories,id',
                'amount' => 'required|numeric|min:0',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'description' => 'nullable|string',
                'type' => 'required|in:monthly,yearly,custom'
            ]);

            $budget = Budget::where('id', $id)
                           ->where('user_id', Auth::id())
                           ->firstOrFail();

            // Check if category belongs to user (if provided)
            if ($request->category_id) {
                $category = Category::where('id', $request->category_id)
                                  ->where('user_id', Auth::id())
                                  ->where('type', 'expense')
                                  ->first();
                
                if (!$category) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Kategori tidak valid'
                    ], 400);
                }
            }

            // Check for overlapping budget in same category (excluding current budget)
            if ($request->category_id) {
                $startDate = Carbon::parse($request->start_date);
                $endDate = Carbon::parse($request->end_date);

                $existingBudget = Budget::where('user_id', Auth::id())
                                      ->where('category_id', $request->category_id)
                                      ->where('id', '!=', $id)
                                      ->where('is_active', true)
                                      ->where(function($query) use ($startDate, $endDate) {
                                          $query->whereBetween('start_date', [$startDate, $endDate])
                                                ->orWhereBetween('end_date', [$startDate, $endDate])
                                                ->orWhere(function($q) use ($startDate, $endDate) {
                                                    $q->where('start_date', '<=', $startDate)
                                                      ->where('end_date', '>=', $endDate);
                                                });
                                      })
                                      ->exists();

                if ($existingBudget) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Sudah ada anggaran aktif untuk kategori ini pada periode yang sama'
                    ], 400);
                }
            }

            $budget->update([
                'name' => $request->name,
                'category_id' => $request->category_id,
                'amount' => $request->amount,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'description' => $request->description,
                'type' => $request->type
            ]);

            Log::info('Budget updated successfully', [
                'budget_id' => $budget->id,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Anggaran berhasil diperbarui',
                'budget' => $budget->load('category')
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error updating budget: ' . $e->getMessage(), [
                'budget_id' => $id,
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui anggaran'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $budget = Budget::where('id', $id)
                           ->where('user_id', Auth::id())
                           ->firstOrFail();
            
            // Store budget name for logging
            $budgetName = $budget->name;
            
            $budget->delete();
            
            Log::info('Budget deleted successfully', [
                'budget_id' => $id,
                'budget_name' => $budgetName,
                'user_id' => Auth::id()
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Anggaran berhasil dihapus'
            ]);
            
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::warning('Attempt to delete non-existent budget', [
                'budget_id' => $id,
                'user_id' => Auth::id()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Anggaran tidak ditemukan'
            ], 404);
            
        } catch (\Exception $e) {
            Log::error('Error deleting budget: ' . $e->getMessage(), [
                'budget_id' => $id,
                'user_id' => Auth::id()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus anggaran'
            ], 500);
        }
    }

    /**
     * Show monitor budget page
     */
    public function monitor()
    {
        $categories = Category::where('user_id', Auth::id())
                             ->where('type', 'expense')
                             ->where('is_active', true)
                             ->orderBy('name')
                             ->get();

        return view('budgets.monitor.index', compact('categories'));
    }

    /**
     * Show budget comparison page
     */
    public function comparison()
    {
        $categories = Category::where('user_id', Auth::id())
                             ->where('type', 'expense')
                             ->where('is_active', true)
                             ->orderBy('name')
                             ->get();

        return view('budgets.comparison.index', compact('categories'));
    }

    /**
     * API: Get dashboard data
     */
    public function apiDashboard()
    {
        try {
            $user_id = Auth::id();
            $now = now();

            // Get all active budgets
            $activeBudgets = Budget::where('user_id', $user_id)
                                  ->where('is_active', true)
                                  ->with('category')
                                  ->get();

            // Calculate spent amounts
            foreach ($activeBudgets as $budget) {
                $budget->spent_amount = $this->calculateSpentAmount($budget);
                $budget->remaining_amount = $budget->amount - $budget->spent_amount;
                $budget->percentage_used = $budget->amount > 0 ? 
                    round(($budget->spent_amount / $budget->amount) * 100, 2) : 0;
            }

            // Calculate stats
            $totalActive = $activeBudgets->count();
            $safeCount = $activeBudgets->filter(function($budget) {
                return $budget->percentage_used < 75;
            })->count();
            $warningCount = $activeBudgets->filter(function($budget) {
                return $budget->percentage_used >= 75 && $budget->percentage_used < 90;
            })->count();
            $exceededCount = $activeBudgets->filter(function($budget) {
                return $budget->percentage_used >= 100;
            })->count();

            // Get recent budgets (last 5)
            $recentBudgets = $activeBudgets->sortByDesc('created_at')->take(5)->values();

            return response()->json([
                'stats' => [
                    'total_active' => $totalActive,
                    'safe_count' => $safeCount,
                    'warning_count' => $warningCount,
                    'exceeded_count' => $exceededCount,
                ],
                'recent_budgets' => $recentBudgets,
                'status_distribution' => [
                    'safe' => $safeCount,
                    'warning' => $warningCount,
                    'danger' => $activeBudgets->filter(function($budget) {
                        return $budget->percentage_used >= 90 && $budget->percentage_used < 100;
                    })->count(),
                    'exceeded' => $exceededCount,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error loading budget dashboard data', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'error' => 'Failed to load dashboard data'
            ], 500);
        }
    }

    /**
     * API: Get monitor data
     */
    public function apiMonitor(Request $request)
    {
        try {
            $user_id = Auth::id();
            $query = Budget::where('user_id', $user_id)->with('category');

            // Apply filters
            if ($request->filled('period')) {
                $this->applyPeriodFilter($query, $request->period);
            }

            if ($request->filled('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            if ($request->filled('status')) {
                // Will apply status filter after calculating spent amounts
            }

            $budgets = $query->orderBy('created_at', 'desc')->get();

            // Calculate spent amounts and apply status filter
            foreach ($budgets as $budget) {
                $budget->spent_amount = $this->calculateSpentAmount($budget);
                $budget->remaining_amount = $budget->amount - $budget->spent_amount;
                $budget->percentage_used = $budget->amount > 0 ? 
                    round(($budget->spent_amount / $budget->amount) * 100, 2) : 0;
            }

            // Apply status filter
            if ($request->filled('status')) {
                $budgets = $budgets->filter(function($budget) use ($request) {
                    return $this->matchesStatusFilter($budget, $request->status);
                });
            }

            // Calculate summary
            $totalBudget = $budgets->sum('amount');
            $totalSpent = $budgets->sum('spent_amount');
            $remainingBudget = $totalBudget - $totalSpent;
            $averageUsage = $budgets->count() > 0 ? 
                round($budgets->avg('percentage_used'), 2) : 0;

            return response()->json([
                'budgets' => $budgets->values(),
                'summary' => [
                    'total_budget' => $totalBudget,
                    'total_spent' => $totalSpent,
                    'remaining_budget' => $remainingBudget,
                    'average_usage' => $averageUsage,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error loading budget monitor data', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'filters' => $request->all()
            ]);

            return response()->json([
                'error' => 'Failed to load monitor data'
            ], 500);
        }
    }

    /**
     * API: Get comparison data
     */
    public function apiComparison(Request $request)
    {
        try {
            $user_id = Auth::id();
            $type = $request->get('type', 'period');

            switch ($type) {
                case 'period':
                    return $this->getPeriodComparison($request);
                case 'category':
                    return $this->getCategoryComparison($request);
                case 'budget_vs_actual':
                    return $this->getBudgetVsActualComparison($request);
                case 'monthly_trend':
                    return $this->getMonthlyTrendComparison($request);
                default:
                    throw new \Exception('Invalid comparison type');
            }

        } catch (\Exception $e) {
            Log::error('Error loading budget comparison data', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Failed to load comparison data'
            ], 500);
        }
    }

    /**
     * Calculate spent amount for a budget
     */
    private function calculateSpentAmount($budget)
    {
        return Transaction::where('user_id', $budget->user_id)
                         ->where('category_id', $budget->category_id)
                         ->where('type', 'expense')
                         ->whereBetween('transaction_date', [$budget->start_date, $budget->end_date])
                         ->sum('amount');
    }

    /**
     * Apply period filter to budget query
     */
    private function applyPeriodFilter($query, $period)
    {
        $now = now();
        
        switch ($period) {
            case 'current':
                $query->where('start_date', '<=', $now)
                      ->where('end_date', '>=', $now);
                break;
            case 'this_month':
                $query->whereMonth('start_date', $now->month)
                      ->whereYear('start_date', $now->year);
                break;
            case 'last_month':
                $lastMonth = $now->copy()->subMonth();
                $query->whereMonth('start_date', $lastMonth->month)
                      ->whereYear('start_date', $lastMonth->year);
                break;
            case 'this_year':
                $query->whereYear('start_date', $now->year);
                break;
            case 'all':
                // No filter
                break;
        }
    }

    /**
     * Check if budget matches status filter
     */
    private function matchesStatusFilter($budget, $status)
    {
        $percentage = $budget->percentage_used;
        
        switch ($status) {
            case 'safe':
                return $percentage < 75;
            case 'warning':
                return $percentage >= 75 && $percentage < 90;
            case 'danger':
                return $percentage >= 90 && $percentage < 100;
            case 'exceeded':
                return $percentage >= 100;
            default:
                return true;
        }
    }

    /**
     * Get period comparison data
     */
    private function getPeriodComparison($request)
    {
        // Implementation for period comparison
        $period1Data = $this->getPeriodData($request->period1, $request);
        $period2Data = $this->getPeriodData($request->period2, $request);

        $difference = $period1Data['total'] - $period2Data['total'];
        $percentageChange = $period2Data['total'] > 0 ? 
            round((($period1Data['total'] - $period2Data['total']) / $period2Data['total']) * 100, 2) : 0;

        return response()->json([
            'summary' => [
                'period1_total' => $period1Data['total'],
                'period2_total' => $period2Data['total'],
                'difference' => $difference,
                'percentage_change' => $percentageChange,
            ],
            'chart_config' => [
                'type' => 'bar',
                'data' => [
                    'labels' => array_keys($period1Data['categories']),
                    'datasets' => [
                        [
                            'label' => 'Periode 1',
                            'data' => array_values($period1Data['categories']),
                            'backgroundColor' => '#4e73df',
                        ],
                        [
                            'label' => 'Periode 2',
                            'data' => array_values($period2Data['categories']),
                            'backgroundColor' => '#1cc88a',
                        ]
                    ]
                ]
            ],
            'distribution_config' => [
                'data' => [
                    'labels' => ['Periode 1', 'Periode 2'],
                    'datasets' => [
                        [
                            'data' => [$period1Data['total'], $period2Data['total']],
                            'backgroundColor' => ['#4e73df', '#1cc88a'],
                        ]
                    ]
                ]
            ],
            'table_config' => [
                'headers' => ['Kategori', 'Periode 1', 'Periode 2', 'Selisih'],
                'rows' => $this->buildComparisonRows($period1Data['categories'], $period2Data['categories'])
            ]
        ]);
    }

    /**
     * Get data for a specific period
     */
    private function getPeriodData($period, $request)
    {
        // Placeholder implementation - you can expand this based on your needs
        $user_id = Auth::id();
        $dateRange = $this->getDateRangeForPeriod($period, $request);
        
        $transactions = Transaction::where('user_id', $user_id)
                                  ->where('type', 'expense')
                                  ->whereBetween('transaction_date', $dateRange)
                                  ->with('category')
                                  ->get();

        $total = $transactions->sum('amount');
        $categories = $transactions->groupBy('category.name')
                                  ->map(function($group) {
                                      return $group->sum('amount');
                                  })
                                  ->toArray();

        return [
            'total' => $total,
            'categories' => $categories
        ];
    }

    /**
     * Get date range for period
     */
    private function getDateRangeForPeriod($period, $request)
    {
        $now = now();
        
        switch ($period) {
            case 'this_month':
                return [$now->startOfMonth()->toDateString(), $now->endOfMonth()->toDateString()];
            case 'last_month':
                $lastMonth = $now->copy()->subMonth();
                return [$lastMonth->startOfMonth()->toDateString(), $lastMonth->endOfMonth()->toDateString()];
            case 'this_year':
                return [$now->startOfYear()->toDateString(), $now->endOfYear()->toDateString()];
            case 'last_year':
                $lastYear = $now->copy()->subYear();
                return [$lastYear->startOfYear()->toDateString(), $lastYear->endOfYear()->toDateString()];
            case 'custom':
                return [$request->custom_start1 ?? $now->toDateString(), 
                       $request->custom_end1 ?? $now->toDateString()];
            default:
                return [$now->startOfMonth()->toDateString(), $now->endOfMonth()->toDateString()];
        }
    }

    /**
     * Build comparison table rows
     */
    private function buildComparisonRows($period1Data, $period2Data)
    {
        $rows = [];
        $allCategories = array_unique(array_merge(array_keys($period1Data), array_keys($period2Data)));
        
        foreach ($allCategories as $category) {
            $value1 = $period1Data[$category] ?? 0;
            $value2 = $period2Data[$category] ?? 0;
            $difference = $value1 - $value2;
            
            $rows[] = [
                $category,
                $value1,
                $value2,
                $difference
            ];
        }
        
        return $rows;
    }

    /**
     * Get category comparison (placeholder)
     */
    private function getCategoryComparison($request)
    {
        // Implement category comparison logic
        return response()->json(['message' => 'Category comparison not implemented yet']);
    }

    /**
     * Get budget vs actual comparison (placeholder)
     */
    private function getBudgetVsActualComparison($request)
    {
        // Implement budget vs actual comparison logic
        return response()->json(['message' => 'Budget vs actual comparison not implemented yet']);
    }

    /**
     * Get monthly trend comparison (placeholder)
     */
    private function getMonthlyTrendComparison($request)
    {
        // Implement monthly trend comparison logic
        return response()->json(['message' => 'Monthly trend comparison not implemented yet']);
    }
}
