<?php

/**
 * Script untuk testing perbaikan logika transfer
 * Jalankan dengan: php test_transfer_fix.php
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Account;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;

echo "=== TESTING PERBAIKAN LOGIKA TRANSFER ===\n\n";

// Cari user pertama untuk testing
$user = User::first();
if (!$user) {
    echo "❌ Tidak ada user untuk testing. Silakan buat user terlebih dahulu.\n";
    exit;
}

echo "👤 Testing dengan user: {$user->name} (ID: {$user->id})\n\n";

// Cari atau buat 2 akun untuk testing
$account1 = Account::where('user_id', $user->id)->first();
$account2 = Account::where('user_id', $user->id)->where('id', '!=', $account1->id ?? 0)->first();

if (!$account1 || !$account2) {
    echo "❌ Perlu minimal 2 akun untuk testing transfer. Silakan buat akun terlebih dahulu.\n";
    exit;
}

echo "💳 Akun 1: {$account1->name} (ID: {$account1->id})\n";
echo "💳 Akun 2: {$account2->name} (ID: {$account2->id})\n\n";

// Simpan saldo awal
$saldoAwal1 = $account1->current_balance;
$saldoAwal2 = $account2->current_balance;

echo "💰 Saldo awal:\n";
echo "   - {$account1->name}: Rp " . number_format($saldoAwal1, 0, ',', '.') . "\n";
echo "   - {$account2->name}: Rp " . number_format($saldoAwal2, 0, ',', '.') . "\n\n";

// Jumlah transfer untuk testing
$jumlahTransfer = 100000;

echo "🔄 Melakukan transfer Rp " . number_format($jumlahTransfer, 0, ',', '.') . " dari {$account1->name} ke {$account2->name}...\n";

try {
    DB::beginTransaction();
    
    // Buat transaksi transfer
    $transaction = Transaction::create([
        'user_id' => $user->id,
        'account_id' => $account1->id,
        'to_account_id' => $account2->id,
        'type' => 'transfer',
        'amount' => $jumlahTransfer,
        'title' => 'Test Transfer',
        'description' => 'Testing perbaikan logika transfer',
        'transaction_date' => now(),
        'currency' => 'IDR',
        'status' => 'completed',
        'processed_at' => now(),
    ]);
    
    // Update saldo kedua akun
    $account1->decrement('current_balance', $jumlahTransfer);
    $account2->increment('current_balance', $jumlahTransfer);
    
    DB::commit();
    
    echo "✅ Transfer berhasil dibuat (Transaction ID: {$transaction->id})\n\n";
    
} catch (Exception $e) {
    DB::rollBack();
    echo "❌ Error saat membuat transfer: " . $e->getMessage() . "\n";
    exit;
}

// Refresh data akun
$account1->refresh();
$account2->refresh();

$saldoAkhir1 = $account1->current_balance;
$saldoAkhir2 = $account2->current_balance;

echo "💰 Saldo setelah transfer:\n";
echo "   - {$account1->name}: Rp " . number_format($saldoAkhir1, 0, ',', '.') . "\n";
echo "   - {$account2->name}: Rp " . number_format($saldoAkhir2, 0, ',', '.') . "\n\n";

// Validasi hasil
$selisih1 = $saldoAwal1 - $saldoAkhir1;
$selisih2 = $saldoAkhir2 - $saldoAwal2;

echo "🔍 VALIDASI HASIL:\n";

// Test 1: Saldo akun asal berkurang sesuai jumlah transfer
if ($selisih1 == $jumlahTransfer) {
    echo "✅ Test 1 PASSED: Saldo akun asal berkurang Rp " . number_format($selisih1, 0, ',', '.') . "\n";
} else {
    echo "❌ Test 1 FAILED: Saldo akun asal seharusnya berkurang Rp " . number_format($jumlahTransfer, 0, ',', '.') . ", tapi berkurang Rp " . number_format($selisih1, 0, ',', '.') . "\n";
}

// Test 2: Saldo akun tujuan bertambah sesuai jumlah transfer
if ($selisih2 == $jumlahTransfer) {
    echo "✅ Test 2 PASSED: Saldo akun tujuan bertambah Rp " . number_format($selisih2, 0, ',', '.') . "\n";
} else {
    echo "❌ Test 2 FAILED: Saldo akun tujuan seharusnya bertambah Rp " . number_format($jumlahTransfer, 0, ',', '.') . ", tapi bertambah Rp " . number_format($selisih2, 0, ',', '.') . "\n";
}

// Test 3: Hanya ada 1 record transaksi transfer
$transferCount = Transaction::where('user_id', $user->id)
    ->where('type', 'transfer')
    ->where('amount', $jumlahTransfer)
    ->where('title', 'Test Transfer')
    ->count();

if ($transferCount == 1) {
    echo "✅ Test 3 PASSED: Hanya ada 1 record transaksi transfer di database\n";
} else {
    echo "❌ Test 3 FAILED: Ada {$transferCount} record transaksi transfer (seharusnya 1)\n";
}

// Test 4: Total saldo tidak berubah (konservasi)
$totalSaldoAwal = $saldoAwal1 + $saldoAwal2;
$totalSaldoAkhir = $saldoAkhir1 + $saldoAkhir2;

if ($totalSaldoAwal == $totalSaldoAkhir) {
    echo "✅ Test 4 PASSED: Total saldo tetap sama (konservasi uang)\n";
} else {
    echo "❌ Test 4 FAILED: Total saldo berubah dari Rp " . number_format($totalSaldoAwal, 0, ',', '.') . " menjadi Rp " . number_format($totalSaldoAkhir, 0, ',', '.') . "\n";
}

echo "\n=== TESTING SELESAI ===\n";

// Cleanup - hapus transaksi test
echo "\n🧹 Membersihkan data test...\n";
try {
    DB::beginTransaction();
    
    // Revert saldo
    $account1->increment('current_balance', $jumlahTransfer);
    $account2->decrement('current_balance', $jumlahTransfer);
    
    // Hapus transaksi test
    Transaction::where('title', 'Test Transfer')->delete();
    
    DB::commit();
    echo "✅ Data test berhasil dibersihkan\n";
    
} catch (Exception $e) {
    DB::rollBack();
    echo "❌ Error saat cleanup: " . $e->getMessage() . "\n";
}
