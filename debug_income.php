<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Carbon\Carbon;

// Setup database connection
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'sqlite',
    'database' => 'database/database.sqlite',
]);
$capsule->setAsGlobal();
$capsule->bootEloquent();

echo "=== DEBUG INCOME & BALANCE GROWTH ===\n";

// Check user 
$userId = 2; // Assuming user ID 2

// Get current month data
$currentMonth = Carbon::now()->startOfMonth();
$currentMonthEnd = Carbon::now()->endOfMonth();
$today = Carbon::today();

echo "User ID: $userId\n";
echo "Current Month: " . $currentMonth->format('Y-m-d') . " to " . $currentMonthEnd->format('Y-m-d') . "\n";
echo "Today: " . $today->format('Y-m-d') . "\n\n";

// Check transactions by type for current month
$incomeTransactions = Capsule::table('transactions')
    ->where('user_id', $userId)
    ->where('type', 'income')
    ->whereBetween('transaction_date', [$currentMonth, $currentMonthEnd])
    ->get();

$expenseTransactions = Capsule::table('transactions')
    ->where('user_id', $userId)
    ->where('type', 'expense')
    ->whereBetween('transaction_date', [$currentMonth, $currentMonthEnd])
    ->get();

echo "=== INCOME TRANSACTIONS (Current Month) ===\n";
foreach ($incomeTransactions as $transaction) {
    echo "- {$transaction->title}: Rp " . number_format($transaction->amount, 0, ',', '.') . "\n";
    echo "  Date: {$transaction->transaction_date}\n";
    echo "  Type: {$transaction->type}\n\n";
}

echo "=== EXPENSE TRANSACTIONS (Current Month) ===\n";
foreach ($expenseTransactions as $transaction) {
    echo "- {$transaction->title}: Rp " . number_format($transaction->amount, 0, ',', '.') . "\n";
    echo "  Date: {$transaction->transaction_date}\n";
    echo "  Type: {$transaction->type}\n\n";
}

// Calculate totals
$monthlyIncome = $incomeTransactions->sum('amount');
$monthlyExpense = $expenseTransactions->sum('amount');

echo "=== MONTHLY TOTALS ===\n";
echo "Monthly Income: Rp " . number_format($monthlyIncome, 0, ',', '.') . "\n";
echo "Monthly Expense: Rp " . number_format($monthlyExpense, 0, ',', '.') . "\n";
echo "Net Income: Rp " . number_format($monthlyIncome - $monthlyExpense, 0, ',', '.') . "\n\n";

// Check previous month for balance growth
$previousMonth = Carbon::now()->subMonth()->startOfMonth();
$previousMonthEnd = Carbon::now()->subMonth()->endOfMonth();

echo "Previous Month: " . $previousMonth->format('Y-m-d') . " to " . $previousMonthEnd->format('Y-m-d') . "\n";

$previousIncomeTransactions = Capsule::table('transactions')
    ->where('user_id', $userId)
    ->where('type', 'income')
    ->whereBetween('transaction_date', [$previousMonth, $previousMonthEnd])
    ->get();

$previousExpenseTransactions = Capsule::table('transactions')
    ->where('user_id', $userId)
    ->where('type', 'expense')
    ->whereBetween('transaction_date', [$previousMonth, $previousMonthEnd])
    ->get();

$previousIncome = $previousIncomeTransactions->sum('amount');
$previousExpense = $previousExpenseTransactions->sum('amount');

echo "=== PREVIOUS MONTH TOTALS ===\n";
echo "Previous Income: Rp " . number_format($previousIncome, 0, ',', '.') . "\n";
echo "Previous Expense: Rp " . number_format($previousExpense, 0, ',', '.') . "\n";
echo "Previous Net: Rp " . number_format($previousIncome - $previousExpense, 0, ',', '.') . "\n\n";

// Calculate balance growth
$currentNet = $monthlyIncome - $monthlyExpense;
$previousNet = $previousIncome - $previousExpense;

echo "=== BALANCE GROWTH CALCULATION ===\n";
echo "Current Net: Rp " . number_format($currentNet, 0, ',', '.') . "\n";
echo "Previous Net: Rp " . number_format($previousNet, 0, ',', '.') . "\n";

if ($previousNet == 0) {
    $balanceGrowth = $currentNet > 0 ? 100 : 0;
    echo "Balance Growth: {$balanceGrowth}% (Previous net was 0)\n";
} else {
    $balanceGrowth = round((($currentNet - $previousNet) / abs($previousNet)) * 100, 1);
    echo "Balance Growth: {$balanceGrowth}%\n";
}

// Check today's transactions
$todayTransactions = Capsule::table('transactions')
    ->where('user_id', $userId)
    ->whereDate('transaction_date', $today)
    ->get();

echo "\n=== TODAY'S TRANSACTIONS ===\n";
echo "Count: " . $todayTransactions->count() . "\n";
foreach ($todayTransactions as $transaction) {
    echo "- {$transaction->title}: Rp " . number_format($transaction->amount, 0, ',', '.') . " ({$transaction->type})\n";
}

echo "\n=== ISSUE ANALYSIS ===\n";
if ($monthlyIncome == 0) {
    echo "❌ NO INCOME found for current month! This might be the issue.\n";
} else {
    echo "✅ Income found: Rp " . number_format($monthlyIncome, 0, ',', '.') . "\n";
}

if ($balanceGrowth == -100) {
    echo "❌ Balance growth shows -100%, this indicates previous month had income but current month has none.\n";
} else {
    echo "✅ Balance growth looks normal: {$balanceGrowth}%\n";
}

?>
