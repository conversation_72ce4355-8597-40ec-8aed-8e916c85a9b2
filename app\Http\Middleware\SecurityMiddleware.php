<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SecurityLog;
use Illuminate\Support\Facades\RateLimiter;

class SecurityMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip security checks for API routes and safe operations
        if ($request->is('*/api/*') || $request->is('api/*') || 
            $request->is('reports/api/*') || $request->is('emergency/*') ||
            $request->is('test-reports-*') || 
            $request->is('categories/*') || // Skip all category routes
            $request->is('categories')) {
            return $next($request);
        }
        
        // Skip strict checks for Laravel method spoofing (DELETE, PUT, PATCH)
        if ($request->isMethod('POST') && in_array($request->input('_method'), ['DELETE', 'PUT', 'PATCH'])) {
            // Only apply rate limiting and basic security for these requests
            $this->applyRateLimiting($request);
            $this->validateReferer($request);
            
            $response = $next($request);
            $this->addSecurityHeaders($response);
            return $response;
        }
        
        // Skip strict checks for actual DELETE, PUT, PATCH requests
        if (in_array($request->method(), ['DELETE', 'PUT', 'PATCH'])) {
            $this->applyRateLimiting($request);
            $this->validateReferer($request);
            
            $response = $next($request);
            $this->addSecurityHeaders($response);
            return $response;
        }
        
        // 1. Check for malicious patterns
        $this->checkMaliciousPatterns($request);
        
        // 2. Rate limiting per IP
        $this->applyRateLimiting($request);
        
        // 3. SQL Injection protection
        $this->checkSqlInjection($request);
        
        // 4. XSS protection
        $this->sanitizeInput($request);
        
        // 5. Validate referer for CSRF protection
        $this->validateReferer($request);
        
        $response = $next($request);
        
        // 6. Security headers
        $this->addSecurityHeaders($response);
        
        return $response;
    }
    
    private function checkMaliciousPatterns(Request $request): void
    {
        $maliciousPatterns = [
            '/\b(union|select|insert|update|delete|drop|create|alter|exec)\b/i',
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/\.\.\//i', // Path traversal
            '/etc\/passwd/i',
            '/proc\/self\/environ/i',
        ];
        
        $input = $request->all();
        
        // Exclude Laravel's method spoofing field and CSRF token from security checks
        $filteredInput = array_filter($input, function($key) {
            return !in_array($key, ['_method', '_token', 'csrf_token']);
        }, ARRAY_FILTER_USE_KEY);
        
        $inputString = json_encode($filteredInput);
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $inputString)) {
                SecurityLog::logEvent(
                    auth()->id(),
                    'malicious_pattern_detected',
                    'Malicious pattern detected in request',
                    'critical',
                    ['pattern' => $pattern, 'input' => $filteredInput]
                );
                
                abort(403, 'Akses ditolak: Aktivitas mencurigakan terdeteksi');
            }
        }
    }
    
    private function applyRateLimiting(Request $request): void
    {
        $key = 'security_' . $request->ip();
        
        if (RateLimiter::tooManyAttempts($key, 100)) { // 100 requests per minute
            SecurityLog::logEvent(
                auth()->id(),
                'rate_limit_exceeded',
                'Rate limit exceeded',
                'high',
                ['ip' => $request->ip()]
            );
            
            abort(429, 'Terlalu banyak permintaan. Silakan coba lagi nanti.');
        }
        
        RateLimiter::hit($key, 60);
    }
    
    private function checkSqlInjection(Request $request): void
    {
        $sqlPatterns = [
            '/(\w*)((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))/i', // SQL OR patterns
            '/((\%27)|(\'))union/i', // UNION with quotes
            '/exec(\s|\+)+(s|x)p\w+/i', // Stored procedure execution
            '/UNION(?:\s+ALL)?\s+SELECT/i', // UNION SELECT patterns
        ];
        
        $input = $request->all();
        
        // Exclude Laravel's method spoofing field and CSRF token from SQL injection checks
        $filteredInput = array_filter($input, function($key) {
            return !in_array($key, ['_method', '_token', 'csrf_token']);
        }, ARRAY_FILTER_USE_KEY);
        
        foreach ($filteredInput as $key => $value) {
            if (is_string($value)) {
                foreach ($sqlPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        SecurityLog::logEvent(
                            auth()->id(),
                            'sql_injection_attempt',
                            'SQL injection attempt detected',
                            'critical',
                            ['input' => $value, 'field' => $key, 'pattern' => $pattern]
                        );
                        
                        abort(403, 'Akses ditolak: SQL injection terdeteksi');
                    }
                }
            }
        }
    }
    
    private function sanitizeInput(Request $request): void
    {
        // XSS protection - this is basic, Laravel already has CSRF protection
        $input = $request->all();
        
        foreach ($input as $key => $value) {
            if (is_string($value)) {
                // Check for script tags and javascript
                if (preg_match('/<script[^>]*>.*?<\/script>/is', $value) || 
                    preg_match('/javascript:/i', $value) || 
                    preg_match('/on\w+\s*=/i', $value)) {
                    
                    SecurityLog::logEvent(
                        auth()->id(),
                        'xss_attempt',
                        'XSS attempt detected',
                        'high',
                        ['input' => $value, 'field' => $key]
                    );
                    
                    abort(403, 'Akses ditolak: XSS attempt terdeteksi');
                }
            }
        }
    }
    
    private function validateReferer(Request $request): void
    {
        // Skip for API requests or safe methods
        if ($request->wantsJson() || in_array($request->method(), ['GET', 'HEAD', 'OPTIONS'])) {
            return;
        }
        
        $referer = $request->header('referer');
        $host = $request->getHttpHost();
        
        if ($referer && !str_contains($referer, $host)) {
            SecurityLog::logEvent(
                auth()->id(),
                'invalid_referer',
                'Invalid referer detected - possible CSRF',
                'medium',
                ['referer' => $referer, 'host' => $host]
            );
        }
    }
    
    private function addSecurityHeaders(Response $response): void
    {
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');        if ($response->headers->get('Content-Type') === 'text/html') {
            $csp = "default-src 'self'; " .
                   "script-src 'self' 'unsafe-inline' 'unsafe-eval' cdn.jsdelivr.net js.pusher.com cdnjs.cloudflare.com; " .
                   "style-src 'self' 'unsafe-inline' fonts.googleapis.com fonts.bunny.net cdnjs.cloudflare.com; " .
                   "img-src 'self' data: https:; " .
                   "font-src 'self' https: fonts.googleapis.com fonts.bunny.net cdnjs.cloudflare.com; " .
                   "connect-src 'self' ws: wss: https: sockjs-ap1.pusher.com ws-ap1.pusher.com wss-ap1.pusher.com socksjs-mtls.pusher.com;";
            $response->headers->set('Content-Security-Policy', $csp);
        }
    }
}
