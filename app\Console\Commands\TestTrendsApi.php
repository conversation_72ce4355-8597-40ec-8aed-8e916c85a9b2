<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\ReportController;
use Illuminate\Http\Request;
use Carbon\Carbon;

class TestTrendsApi extends Command
{
    protected $signature = 'test:trends-api {start_date?} {end_date?}';
    protected $description = 'Test trends API with real data';

    public function handle()
    {
        $startDate = $this->argument('start_date') ?? Carbon::now()->subMonths(6)->format('Y-m-d');
        $endDate = $this->argument('end_date') ?? Carbon::now()->format('Y-m-d');
        
        // Login as user 2
        auth()->loginUsingId(2);
        
        // Create request
        $request = new Request([
            'start_date' => $startDate,
            'end_date' => $endDate,
            'period' => 'monthly'
        ]);
        
        // Test the API
        $controller = new ReportController();
        $response = $controller->apiTrends($request);
        
        $this->info("Trends API Response for $startDate to $endDate:");
        $this->line($response->getContent());
    }
}
