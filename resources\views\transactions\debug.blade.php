@extends('layouts.dashboard')

@section('title', 'Debug Riwayat Transaksi')

@section('page-title', '🐛 Debug Riwayat Transaksi')

@section('content')
<div class="dashboard-container">
    <div class="card">
        <div class="card-header">
            <h3>Debug Transaksi User ID: {{ Auth::id() }}</h3>
        </div>
        <div class="card-body">
            <p><strong>Total Transaksi:</strong> {{ $transactions->count() }}</p>
            
            @if($transactions->count() > 0)
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tanggal</th>
                            <th>Judul</th>
                            <th>Jenis</th>
                            <th>Jumlah</th>
                            <th>Akun</th>
                            <th>Kategori</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($transactions as $transaction)
                        <tr>
                            <td>{{ $transaction->id }}</td>
                            <td>{{ $transaction->transaction_date }}</td>
                            <td>{{ $transaction->title }}</td>
                            <td>{{ $transaction->type }}</td>
                            <td>Rp {{ number_format($transaction->amount, 0, ',', '.') }}</td>
                            <td>{{ $transaction->account ? $transaction->account->name : '-' }}</td>
                            <td>{{ $transaction->category ? $transaction->category->name : '-' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-warning">
                    Tidak ada transaksi ditemukan untuk user ini.
                </div>
            @endif
            
            <hr>
            <h4>API Response Test</h4>
            <button type="button" class="btn btn-primary" onclick="testApi()">Test API</button>
            <div id="apiResult" style="margin-top: 20px;"></div>
        </div>
    </div>
</div>

<script>
function testApi() {
    fetch('/transactions/api/data')
        .then(response => response.json())
        .then(data => {
            document.getElementById('apiResult').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('apiResult').innerHTML = '<div class="alert alert-danger">Error: ' + error + '</div>';
        });
}
</script>
@endsection
