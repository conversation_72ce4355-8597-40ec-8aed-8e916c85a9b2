<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Session and Login Security (skip if already exists)
            if (!Schema::hasColumn('users', 'current_session_id')) {
                $table->string('current_session_id')->nullable()->after('remember_token');
            }
            if (!Schema::hasColumn('users', 'last_activity')) {
                $table->timestamp('last_activity')->nullable()->after('last_login_ip');
            }
            
            // Account Locking (skip login_attempts as it already exists)
            if (!Schema::hasColumn('users', 'failed_login_attempts')) {
                $table->integer('failed_login_attempts')->default(0)->after('login_attempts');
            }
            
            // Password Security
            if (!Schema::hasColumn('users', 'password_changed_at')) {
                $table->timestamp('password_changed_at')->nullable()->after('locked_until');
            }
            
            // Two-Factor Authentication
            if (!Schema::hasColumn('users', 'two_factor_secret')) {
                $table->text('two_factor_secret')->nullable()->after('password_changed_at');
            }
            if (!Schema::hasColumn('users', 'two_factor_recovery_codes')) {
                $table->text('two_factor_recovery_codes')->nullable()->after('two_factor_secret');
            }
            if (!Schema::hasColumn('users', 'two_factor_confirmed_at')) {
                $table->timestamp('two_factor_confirmed_at')->nullable()->after('two_factor_recovery_codes');
            }
            
            // Account Recovery
            if (!Schema::hasColumn('users', 'backup_codes')) {
                $table->json('backup_codes')->nullable()->after('two_factor_confirmed_at');
            }
            if (!Schema::hasColumn('users', 'security_questions')) {
                $table->json('security_questions')->nullable()->after('backup_codes');
            }
        });
        
        // Add indexes (with existence check)
        if (!Schema::hasIndex('users', ['current_session_id'])) {
            Schema::table('users', function (Blueprint $table) {
                $table->index('current_session_id');
            });
        }
        if (!Schema::hasIndex('users', ['last_login_at'])) {
            Schema::table('users', function (Blueprint $table) {
                $table->index('last_login_at');
            });
        }
        if (!Schema::hasIndex('users', ['locked_until'])) {
            Schema::table('users', function (Blueprint $table) {
                $table->index('locked_until');
            });
        }
        if (!Schema::hasIndex('users', ['last_activity'])) {
            Schema::table('users', function (Blueprint $table) {
                $table->index('last_activity');
            });
        }
    }    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes first (if they exist)
            $indexes = ['current_session_id', 'last_login_at', 'locked_until', 'last_activity'];
            foreach ($indexes as $index) {
                if (Schema::hasIndex('users', [$index])) {
                    $table->dropIndex([$index]);
                }
            }
            
            // Drop columns (if they exist)
            $columns = [
                'current_session_id',
                'last_activity',
                'failed_login_attempts',
                'password_changed_at',
                'two_factor_secret',
                'two_factor_recovery_codes',
                'two_factor_confirmed_at',
                'backup_codes',
                'security_questions'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('users', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
