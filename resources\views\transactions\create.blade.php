@extends('layouts.dashboard')

@section('title', 'Tambah Transaksi')

@section('page-title', '💰 Tambah Transaksi - Catat Keuangan Baru')

@push('styles')
<style>
    /* Override default container behavior to match dashboard */
    .dashboard-container {
        padding: 30px !important;
        min-height: calc(100vh - var(--topbar-height)) !important;
    }
    
    /* Theme support untuk form-card sesuai dengan dashboard */
    body[data-theme="solarized-dark"] .form-card {
        background: #fdf6e3; /* Cream untuk tema solarized-dark */
    }
    
    body[data-theme="synth-wave"] .form-card {
        background: #1a1a1a !important;
        border: 1px solid #ff00ff !important;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.2) !important;
    }
    
    body[data-theme="synth-wave"] .form-header {
        background: rgba(26, 26, 26, 0.9) !important;
        border-bottom: 1px solid #ff00ff !important;
    }
    
    body[data-theme="synth-wave"] .form-header h2 {
        color: #ff00ff !important;
        text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
    }
    
    body[data-theme="synth-wave"] .form-header p {
        color: #00ffff !important;
    }
    
    /* Default theme menggunakan cream */
    .form-card {
        background: #fdf6e3; /* Default cream color sesuai dashboard */
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        transition: all 0.3s ease;
        margin: 0 auto;
        max-width: none;
        width: 100%;
    }
    
    .form-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
    }
    
    .form-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px 20px 0 0;
    }
    
    .form-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        animation: grain 20s linear infinite;
        pointer-events: none;
    }
    
    @keyframes grain {
        0%, 100% { transform: translate(0, 0); }
        25% { transform: translate(-10px, -10px); }
        50% { transform: translate(-20px, 0); }
        75% { transform: translate(-10px, 10px); }
    }
    
    .form-header h2 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }
    
    .form-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .form-body {
        padding: 40px;
    }
    
    .transaction-type-selector {
        margin-bottom: 30px;
    }
    
    .type-buttons {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .type-btn {
        flex: 1;
        padding: 20px;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .type-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s ease;
    }
    
    .type-btn:hover::before {
        left: 100%;
    }
    
    .type-btn.active {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }
    
    .type-btn i {
        font-size: 2rem;
        margin-bottom: 10px;
        display: block;
    }
    
    .type-btn h5 {
        margin: 0;
        font-weight: 600;
    }
    
    .type-btn p {
        margin: 5px 0 0 0;
        font-size: 0.85rem;
        opacity: 0.8;
    }
    
    .form-group {
        margin-bottom: 25px;
        position: relative;
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .form-label i {
        color: #667eea;
    }
    
    .form-control {
        padding: 15px 20px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        font-size: 16px;
        transition: all 0.3s ease;
        background: #f8f9fa; /* Background lebih sesuai dengan cream theme */
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
        outline: none;
    }
    
    .form-select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 12px center;
        background-repeat: no-repeat;
        background-size: 16px 12px;
        padding-right: 50px;
    }
    
    .amount-input-group {
        position: relative;
    }
    
    .currency-symbol {
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        font-weight: 600;
        font-size: 18px;
    }
    
    .amount-input {
        padding-left: 50px;
        font-size: 18px;
        font-weight: 600;
        color: #28a745;
    }
    
    .quick-amounts {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: 10px;
    }
    
    .quick-amount-btn {
        padding: 8px 15px;
        background: #f4f0e8; /* Background cream yang konsisten */
        border: 1px solid #d4c5b0; /* Border cream */
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
    }
    
    .quick-amount-btn:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }
    
    .date-time-group {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 15px;
    }
    
    .advanced-options {
        background: #f4f0e8; /* Background cream yang lebih lembut */
        border-radius: 15px;
        padding: 25px;
        margin-top: 20px;
        border: 1px solid #e6ddd1; /* Border yang sesuai dengan cream theme */
    }
    
    .advanced-options-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        cursor: pointer;
    }
    
    .advanced-options-content {
        display: none;
    }
    
    .advanced-options.active .advanced-options-content {
        display: block;
        animation: slideDown 0.3s ease;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .toggle-arrow {
        transition: transform 0.3s ease;
    }
    
    .advanced-options.active .toggle-arrow {
        transform: rotate(180deg);
    }
    
    .attachment-zone {
        border: 2px dashed #d4c5b0; /* Border cream untuk consistency */
        border-radius: 12px;
        padding: 30px;
        text-align: center;
        background: #f4f0e8; /* Background cream yang lembut */
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .attachment-zone:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }
    
    .attachment-zone.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 18px 40px;
        border-radius: 15px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        width: 100%;
    }
    
    .btn-submit::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s ease;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
    }
    
    .btn-submit:hover::before {
        left: 100%;
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        color: white;
        padding: 18px 40px;
        border-radius: 15px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        margin-right: 15px;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(108, 117, 125, 0.3);
    }
    
    .form-buttons {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 15px;
        margin-top: 30px;
    }
    
    .field-validation {
        display: none;
        color: #dc3545;
        font-size: 14px;
        margin-top: 5px;
    }
    
    .form-control.is-invalid {
        border-color: #dc3545;
        background: #fff5f5;
    }
    
    .form-control.is-valid {
        border-color: #28a745;
        background: #f8fff8;
    }
    
    .calculator-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        display: none;
    }
    
    .calculator-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1px;
        background: #e9ecef;
        border-radius: 15px;
        overflow: hidden;
    }
    
    .calc-btn {
        padding: 20px;
        background: white;
        border: none;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s ease;
    }
    
    .calc-btn:hover {
        background: #f8f9fa;
    }
    
    .calc-btn.operator {
        background: #667eea;
        color: white;
    }
    
    .calc-btn.operator:hover {
        background: #5a6fd8;
    }
    
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 15px !important;
        }
        
        .form-body {
            padding: 20px;
        }
        
        .type-buttons {
            flex-direction: column;
        }
        
        .date-time-group {
            grid-template-columns: 1fr;
        }
        
        .form-buttons {
            grid-template-columns: 1fr;
        }
        
        .quick-amounts {
            justify-content: center;
        }
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    
    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Dukungan tema untuk elemen form lainnya */
    body[data-theme="solarized-dark"] .advanced-options {
        background: #f4f0e8;
        border: 1px solid #e6ddd1;
    }
    
    body[data-theme="solarized-dark"] .attachment-zone {
        background: #f4f0e8;
        border-color: #d4c5b0;
    }
    
    body[data-theme="solarized-dark"] .quick-amount-btn {
        background: #f4f0e8;
        border-color: #d4c5b0;
    }
    
    body[data-theme="solarized-dark"] .form-control {
        background: #faf8f5;
    }
    
    body[data-theme="synth-wave"] .advanced-options {
        background: rgba(255, 0, 255, 0.05);
        border: 1px solid rgba(255, 0, 255, 0.2);
    }
    
    body[data-theme="synth-wave"] .attachment-zone {
        background: rgba(255, 0, 255, 0.05);
        border-color: rgba(255, 0, 255, 0.3);
    }
    
    body[data-theme="synth-wave"] .quick-amount-btn {
        background: rgba(255, 0, 255, 0.05);
        border-color: rgba(255, 0, 255, 0.2);
    }
    
    /* Fix untuk tema Solarized Dark - visibility text pada type buttons */
    body[data-theme="solarized-dark"] .type-btn h5 {
        color: #586e75 !important; /* Warna yang terlihat pada background cream */
    }
    
    body[data-theme="solarized-dark"] .type-btn p {
        color: #839496 !important; /* Warna yang lebih soft tapi tetap terlihat */
    }
    
    body[data-theme="solarized-dark"] .type-btn.active h5 {
        color: white !important; /* Putih saat active/hover */
    }
    
    body[data-theme="solarized-dark"] .type-btn.active p {
        color: rgba(255, 255, 255, 0.9) !important; /* Putih transparan saat active */
    }
    
    body[data-theme="solarized-dark"] .type-btn:hover h5 {
        color: white !important; /* Putih saat hover */
    }
    
    body[data-theme="solarized-dark"] .type-btn:hover p {
        color: rgba(255, 255, 255, 0.9) !important; /* Putih transparan saat hover */
    }
    
    /* Fix untuk tema Synth Wave - visibility text dan label */
    body[data-theme="synth-wave"] .type-btn {
        background: rgba(0, 0, 0, 0.8) !important; /* Background gelap untuk kontras */
        border-color: #ff00ff !important;
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .type-btn h5 {
        color: #ffffff !important; /* Putih untuk visibility yang baik */
    }
    
    body[data-theme="synth-wave"] .type-btn p {
        color: #ff00ff !important; /* Magenta untuk text description */
        opacity: 1 !important; /* Pastikan opacity penuh */
    }
    
    body[data-theme="synth-wave"] .type-btn.active {
        background: linear-gradient(135deg, #ff00ff 0%, #00ffff 100%) !important;
        color: #000000 !important; /* Text hitam pada gradient terang */
    }
    
    body[data-theme="synth-wave"] .type-btn.active h5 {
        color: #000000 !important; /* Hitam saat active */
    }
    
    body[data-theme="synth-wave"] .type-btn.active p {
        color: #000000 !important; /* Hitam saat active */
    }
    
    body[data-theme="synth-wave"] .type-btn:hover {
        background: rgba(255, 0, 255, 0.3) !important;
        border-color: #00ffff !important;
    }
    
    body[data-theme="synth-wave"] .type-btn:hover h5 {
        color: #00ffff !important; /* Cyan saat hover */
    }
    
    body[data-theme="synth-wave"] .type-btn:hover p {
        color: #ffffff !important; /* Putih saat hover */
    }
    
    /* Fix untuk label pada tema Synth Wave */
    body[data-theme="synth-wave"] .form-label {
        color: #ffffff !important; /* Label putih untuk visibility */
        font-weight: 600 !important;
    }
    
    body[data-theme="synth-wave"] .form-label i {
        color: #ff00ff !important; /* Icon magenta */
    }
    
    /* Fix untuk transaction type selector title pada Synth Wave */
    body[data-theme="synth-wave"] .transaction-type-selector h5 {
        color: #ffffff !important; /* Putih untuk judul section */
    }
    
    body[data-theme="synth-wave"] .transaction-type-selector h5 i {
        color: #ff00ff !important; /* Icon magenta */
    }
    
    /* Fix tambahan untuk Solarized Dark - pastikan semua text terlihat */
    body[data-theme="solarized-dark"] .form-label {
        color: #586e75 !important; /* Warna yang kontras dengan background cream */
    }
    
    body[data-theme="solarized-dark"] .form-label i {
        color: #268bd2 !important; /* Biru solarized untuk icon */
    }
    
    body[data-theme="solarized-dark"] .transaction-type-selector h5 {
        color: #586e75 !important;
    }
    
    body[data-theme="solarized-dark"] .transaction-type-selector h5 i {
        color: #268bd2 !important;
    }
    
    /* Fix untuk advanced options pada kedua tema */
    body[data-theme="solarized-dark"] .advanced-options-header h6 {
        color: #586e75 !important;
    }
    
    body[data-theme="solarized-dark"] .advanced-options-header i {
        color: #268bd2 !important;
    }
    
    body[data-theme="synth-wave"] .advanced-options-header h6 {
        color: #ffffff !important;
    }
    
    body[data-theme="synth-wave"] .advanced-options-header i {
        color: #ff00ff !important;
    }
    
    /* Fix untuk text pada form controls */
    body[data-theme="synth-wave"] .form-control {
        background: rgba(0, 0, 0, 0.8) !important;
        color: #ffffff !important;
        border-color: #ff00ff !important;
    }
    
    body[data-theme="synth-wave"] .form-control:focus {
        background: rgba(0, 0, 0, 0.9) !important;
        color: #ffffff !important;
        border-color: #00ffff !important;
        box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.2) !important;
    }
    
    body[data-theme="synth-wave"] .form-control::placeholder {
        color: rgba(255, 255, 255, 0.6) !important;
    }
    
    /* Fix untuk currency symbol */
    body[data-theme="synth-wave"] .currency-symbol {
        color: #00ffff !important;
    }
    
    body[data-theme="solarized-dark"] .currency-symbol {
        color: #268bd2 !important;
    }
</style>
@endpush

@section('content')
<!-- Content menggunakan dashboard-container untuk konsistensi dengan layout dashboard -->
<div class="dashboard-container">
    <div class="form-card">
        <div class="form-header">
            <h2><i class="fas fa-plus-circle"></i> Tambah Transaksi Baru</h2>
            <p>Catat pemasukan, pengeluaran, atau transfer antar akun dengan mudah</p>
        </div>
        
        <div class="form-body">
            <!-- Alert Messages -->
            @if ($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Ups! Ada yang salah:</strong>
                    <ul class="mb-0 mt-2">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif            
            {{-- Debug Information (temporary - akan dihapus setelah debugging selesai) --}}
            {{--
            @if(session('debug_info'))
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-bug"></i>
                    <strong>Debug Information:</strong>
                    <div class="mt-2">
                        <small>
                            <strong>User ID:</strong> {{ session('debug_info.user_id') }}<br>
                            
                            @if(session('debug_info.validation_errors'))
                                <strong>Validation Errors:</strong>
                                <pre style="font-size: 11px; background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 5px;">{{ json_encode(session('debug_info.validation_errors'), JSON_PRETTY_PRINT) }}</pre>
                            @endif
                            
                            @if(session('debug_info.input_data'))
                                <strong>Input Data:</strong>
                                <pre style="font-size: 11px; background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 5px;">{{ json_encode(session('debug_info.input_data'), JSON_PRETTY_PRINT) }}</pre>
                            @endif
                        </small>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(auth()->check())
                <div class="alert alert-secondary alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle"></i>
                    <strong>Debug - User's Accounts:</strong>
                    <div class="mt-2">
                        <small>
                            <strong>User ID:</strong> {{ auth()->id() }}<br>
                            <strong>Available Accounts:</strong>
                            @if($accounts && $accounts->count() > 0)
                                <ul class="mb-0 mt-1">
                                    @foreach($accounts as $account)
                                        <li>ID: {{ $account->id }}, Name: {{ $account->name }}, Active: {{ $account->is_active ? 'Yes' : 'No' }}</li>
                                    @endforeach
                                </ul>
                            @else
                                <span class="text-danger">No accounts found!</span>
                            @endif
                        </small>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif
            --}}

            <form id="transactionForm" action="{{ route('transactions.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <!-- Transaction Type Selector -->
                <div class="transaction-type-selector">
                    <h5 class="mb-3"><i class="fas fa-layer-group"></i> Pilih Jenis Transaksi</h5>
                    <div class="type-buttons">
                        <div class="type-btn active" data-type="income">
                            <i class="fas fa-arrow-up"></i>
                            <h5>Pemasukan</h5>
                            <p>Gaji, bonus, investasi</p>
                        </div>
                        <div class="type-btn" data-type="expense">
                            <i class="fas fa-arrow-down"></i>
                            <h5>Pengeluaran</h5>
                            <p>Belanja, tagihan, hiburan</p>
                        </div>
                        <div class="type-btn" data-type="transfer">
                            <i class="fas fa-exchange-alt"></i>
                            <h5>Transfer</h5>
                            <p>Antar akun, tabungan</p>
                        </div>
                    </div>
                    <input type="hidden" name="type" id="transaction_type" value="income">
                </div>

                <!-- Main Form Fields -->
                <div class="row">
                    <!-- Left Column -->
                    <div class="col-md-6">
                        <!-- Title -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-pen"></i> Judul Transaksi *
                            </label>
                            <input type="text" name="title" id="title" class="form-control" 
                                   placeholder="Contoh: Gaji bulan ini, Beli groceries, dll"
                                   value="{{ old('title') }}" required>
                            <div class="field-validation" id="title-validation"></div>
                        </div>

                        <!-- Amount -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-money-bill-wave"></i> Jumlah *
                            </label>
                            <div class="amount-input-group">
                                <span class="currency-symbol">Rp</span>
                                <input type="text" name="amount" id="amount" class="form-control amount-input" 
                                       placeholder="0" value="{{ old('amount') }}" required>
                                <button type="button" class="btn btn-sm btn-outline-secondary" 
                                        style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);"
                                        onclick="openCalculator()">
                                    <i class="fas fa-calculator"></i>
                                </button>
                            </div>
                            <div class="quick-amounts">
                                <span class="quick-amount-btn" data-amount="50000">50K</span>
                                <span class="quick-amount-btn" data-amount="100000">100K</span>
                                <span class="quick-amount-btn" data-amount="500000">500K</span>
                                <span class="quick-amount-btn" data-amount="1000000">1JT</span>
                                <span class="quick-amount-btn" data-amount="5000000">5JT</span>
                            </div>
                            <div class="field-validation" id="amount-validation"></div>
                        </div>

                        <!-- From Account -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-wallet"></i> <span id="from-account-label">Ke Akun*</span>
                            </label>
                            <select name="account_id" id="account_id" class="form-control form-select" required>
                                <option value="">Pilih akun...</option>
                                @foreach($accounts as $account)
                                    <option value="{{ $account->id }}" 
                                            data-balance="{{ formatRupiah($account->current_balance, false) }}"
                                            {{ old('account_id') == $account->id ? 'selected' : '' }}>
                                        {{ $account->name }} - {{ formatRupiah($account->current_balance) }}
                                    </option>
                                @endforeach
                            </select>
                            <div class="field-validation" id="account-validation"></div>
                        </div>

                        <!-- To Account (for transfers) -->
                        <div class="form-group" id="to-account-group" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-arrow-right"></i> Ke Akun *
                            </label>
                            <select name="to_account_id" id="to_account_id" class="form-control form-select">
                                <option value="">Pilih akun tujuan...</option>
                                @foreach($accounts as $account)
                                    <option value="{{ $account->id }}" 
                                            data-balance="{{ number_format($account->current_balance, 0, ',', '.') }}"
                                            {{ old('to_account_id') == $account->id ? 'selected' : '' }}>
                                        {{ $account->name }} - Rp {{ number_format($account->current_balance, 0, ',', '.') }}
                                    </option>
                                @endforeach
                            </select>
                            <div class="field-validation" id="to-account-validation"></div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="col-md-6">
                        <!-- Category -->
                        <div class="form-group" id="category-group">
                            <label class="form-label">
                                <i class="fas fa-tags"></i> Kategori *
                            </label>
                            <select name="category_id" id="category_id" class="form-control form-select">
                                <option value="">Pilih kategori...</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" 
                                            data-type="{{ $category->type }}"
                                            {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                        @if($category->budget_limit)
                                            (Budget: Rp {{ number_format($category->budget_limit, 0, ',', '.') }})
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                            <div class="field-validation" id="category-validation"></div>
                        </div>

                        <!-- Date & Time -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-calendar"></i> Tanggal & Waktu *
                            </label>
                            <div class="date-time-group">
                                <input type="date" name="transaction_date" id="transaction_date" 
                                       class="form-control" value="{{ old('transaction_date', date('Y-m-d')) }}" required>
                                <input type="time" name="transaction_time" id="transaction_time" 
                                       class="form-control" value="{{ old('transaction_time', date('H:i')) }}">
                            </div>
                            <div class="field-validation" id="date-validation"></div>
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-comment"></i> Deskripsi
                            </label>
                            <textarea name="description" id="description" class="form-control" rows="3" 
                                      placeholder="Tambahkan catatan atau detail transaksi...">{{ old('description') }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Advanced Options -->
                <div class="advanced-options">
                    <div class="advanced-options-header">
                        <h6><i class="fas fa-cogs"></i> Opsi Lanjutan</h6>
                        <i class="fas fa-chevron-down toggle-arrow"></i>
                    </div>
                    <div class="advanced-options-content">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Payment Method -->
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-credit-card"></i> Metode Pembayaran
                                    </label>
                                    <select name="payment_method" id="payment_method" class="form-control form-select">
                                        <option value="">Pilih metode...</option>
                                        <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Tunai</option>
                                        <option value="debit" {{ old('payment_method') == 'debit' ? 'selected' : '' }}>Kartu Debit</option>
                                        <option value="credit" {{ old('payment_method') == 'credit' ? 'selected' : '' }}>Kartu Kredit</option>
                                        <option value="transfer" {{ old('payment_method') == 'transfer' ? 'selected' : '' }}>Transfer Bank</option>
                                        <option value="ewallet" {{ old('payment_method') == 'ewallet' ? 'selected' : '' }}>E-Wallet</option>
                                        <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>Cek</option>
                                    </select>
                                </div>

                                <!-- Reference Number -->
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-hashtag"></i> Nomor Referensi
                                    </label>
                                    <input type="text" name="reference_number" id="reference_number" 
                                           class="form-control" placeholder="Nomor transaksi, invoice, dll"
                                           value="{{ old('reference_number') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Location -->
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-map-marker-alt"></i> Lokasi
                                    </label>
                                    <input type="text" name="location" id="location" 
                                           class="form-control" placeholder="Dimana transaksi ini terjadi?"
                                           value="{{ old('location') }}">
                                </div>

                                <!-- Tags -->
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-tag"></i> Tag
                                    </label>
                                    <input type="text" name="tags" id="tags" 
                                           class="form-control" placeholder="Pisahkan dengan koma"
                                           value="{{ old('tags') }}">
                                    <small class="text-muted">Contoh: urgent, monthly, tax-deductible</small>
                                </div>
                            </div>
                        </div>

                        <!-- File Attachment -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-paperclip"></i> Lampiran
                            </label>
                            <div class="attachment-zone" onclick="document.getElementById('attachment').click()">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                <p class="mb-0">Klik untuk upload atau drag & drop file</p>
                                <small class="text-muted">Maksimal 5MB (PDF, JPG, PNG)</small>
                                <input type="file" name="attachment" id="attachment" style="display: none;" 
                                       accept=".pdf,.jpg,.jpeg,.png" onchange="handleFileSelect(this)">
                            </div>
                            <div id="file-preview" style="display: none; margin-top: 10px;"></div>
                        </div>

                        <!-- Recurring Options -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_recurring" id="is_recurring" 
                                               class="form-check-input" value="1" {{ old('is_recurring') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_recurring">
                                            <i class="fas fa-redo"></i> Jadikan transaksi berulang
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6" id="recurring-options" style="display: none;">
                                <div class="form-group">
                                    <select name="recurring_type" id="recurring_type" class="form-control form-select">
                                        <option value="">Pilih periode...</option>
                                        <option value="daily">Harian</option>
                                        <option value="weekly">Mingguan</option>
                                        <option value="monthly">Bulanan</option>
                                        <option value="yearly">Tahunan</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Buttons -->
                <div class="form-buttons">
                    <button type="button" class="btn-cancel" onclick="window.history.back()">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn-submit">
                        <i class="fas fa-save"></i> Simpan Transaksi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Calculator Popup -->
<div class="calculator-popup" id="calculatorPopup">
    <div class="p-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">Kalkulator</h6>
            <button type="button" class="btn-close" onclick="closeCalculator()"></button>
        </div>
        <input type="text" id="calcDisplay" class="form-control mb-3" readonly>
        <div class="calculator-grid">
            <button class="calc-btn" onclick="clearCalc()">C</button>
            <button class="calc-btn" onclick="calcOperation('/')" class="operator">÷</button>
            <button class="calc-btn" onclick="calcOperation('*')" class="operator">×</button>
            <button class="calc-btn" onclick="backspace()">⌫</button>
            
            <button class="calc-btn" onclick="calcNumber('7')">7</button>
            <button class="calc-btn" onclick="calcNumber('8')">8</button>
            <button class="calc-btn" onclick="calcNumber('9')">9</button>
            <button class="calc-btn operator" onclick="calcOperation('-')">-</button>
            
            <button class="calc-btn" onclick="calcNumber('4')">4</button>
            <button class="calc-btn" onclick="calcNumber('5')">5</button>
            <button class="calc-btn" onclick="calcNumber('6')">6</button>
            <button class="calc-btn operator" onclick="calcOperation('+')">+</button>
            
            <button class="calc-btn" onclick="calcNumber('1')">1</button>
            <button class="calc-btn" onclick="calcNumber('2')">2</button>
            <button class="calc-btn" onclick="calcNumber('3')">3</button>
            <button class="calc-btn operator" onclick="calculateResult()" style="grid-row: span 2">=</button>
            
            <button class="calc-btn" onclick="calcNumber('0')" style="grid-column: span 2">0</button>
            <button class="calc-btn" onclick="calcNumber('.')">.</button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner"></div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Transaction type selection
    const typeButtons = document.querySelectorAll('.type-btn');
    const transactionTypeInput = document.getElementById('transaction_type');
    const categoryGroup = document.getElementById('category-group');
    const toAccountGroup = document.getElementById('to-account-group');
    const fromAccountLabel = document.getElementById('from-account-label');
    
    typeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            typeButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            const type = this.dataset.type;
            transactionTypeInput.value = type;
            
            // Update form based on type
            if (type === 'transfer') {
                categoryGroup.style.display = 'none';
                toAccountGroup.style.display = 'block';
                fromAccountLabel.textContent = 'Dari Akun *';
                document.getElementById('category_id').removeAttribute('required');
                document.getElementById('to_account_id').setAttribute('required', 'required');
            } else {
                categoryGroup.style.display = 'block';
                toAccountGroup.style.display = 'none';
                fromAccountLabel.textContent = type === 'income' ? 'Ke Akun *' : 'Dari Akun *';
                document.getElementById('category_id').setAttribute('required', 'required');
                document.getElementById('to_account_id').removeAttribute('required');
                
                // Filter categories by type
                filterCategoriesByType(type);
            }
        });
    });
    
    // Filter categories based on transaction type
    function filterCategoriesByType(type) {
        const categorySelect = document.getElementById('category_id');
        const options = categorySelect.querySelectorAll('option');
        
        options.forEach(option => {
            if (option.value === '') return; // Keep the default option
            
            const categoryType = option.dataset.type;
            if (categoryType === type) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        });
        
        // Reset selection if current selection is not valid for new type
        const currentSelection = categorySelect.value;
        if (currentSelection) {
            const currentOption = categorySelect.querySelector(`option[value="${currentSelection}"]`);
            if (currentOption && currentOption.dataset.type !== type) {
                categorySelect.value = '';
            }
        }
    }
    
    // Initialize with income type
    filterCategoriesByType('income');
    
    // Amount formatting
    const amountInput = document.getElementById('amount');
    amountInput.addEventListener('input', function() {
        let value = this.value.replace(/[^\d]/g, '');
        if (value) {
            this.value = new Intl.NumberFormat('id-ID').format(value);
        }
    });
    
    // Quick amount buttons
    const quickAmountBtns = document.querySelectorAll('.quick-amount-btn');
    quickAmountBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const amount = this.dataset.amount;
            amountInput.value = new Intl.NumberFormat('id-ID').format(amount);
        });
    });
    
    // Advanced options toggle
    const advancedOptions = document.querySelector('.advanced-options');
    const advancedHeader = document.querySelector('.advanced-options-header');
    
    advancedHeader.addEventListener('click', function() {
        advancedOptions.classList.toggle('active');
    });
    
    // Recurring options toggle
    const isRecurringCheckbox = document.getElementById('is_recurring');
    const recurringOptions = document.getElementById('recurring-options');
    
    isRecurringCheckbox.addEventListener('change', function() {
        if (this.checked) {
            recurringOptions.style.display = 'block';
            document.getElementById('recurring_type').setAttribute('required', 'required');
        } else {
            recurringOptions.style.display = 'none';
            document.getElementById('recurring_type').removeAttribute('required');
        }
    });
    
    // File upload handling
    const attachmentZone = document.querySelector('.attachment-zone');
    const attachmentInput = document.getElementById('attachment');
    
    // Drag and drop functionality
    attachmentZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    attachmentZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    attachmentZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            attachmentInput.files = files;
            handleFileSelect(attachmentInput);
        }
    });
    
    // Form validation
    const form = document.getElementById('transactionForm');
    form.addEventListener('submit', function(e) {
        let isValid = true;

        // Validate required fields, except category_id for transfer
        const requiredFields = form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            // Jika field adalah category_id dan tipe transfer, skip validasi required
            if (field.id === 'category_id' && transactionTypeInput.value === 'transfer') {
                clearFieldError(field);
                return;
            }
            if (!field.value.trim()) {
                showFieldError(field, 'Field ini wajib diisi');
                isValid = false;
            } else {
                clearFieldError(field);
            }
        });

        // Validate amount
        const amount = amountInput.value.replace(/[^\d]/g, '');
        if (!amount || parseInt(amount) <= 0) {
            showFieldError(amountInput, 'Jumlah harus lebih dari 0');
            isValid = false;
        }

        // Validate transfer accounts are different
        if (transactionTypeInput.value === 'transfer') {
            const fromAccount = document.getElementById('account_id').value;
            const toAccount = document.getElementById('to_account_id').value;

            if (fromAccount === toAccount) {
                showFieldError(document.getElementById('to_account_id'), 'Akun tujuan harus berbeda dengan akun asal');
                isValid = false;
            }
        }

        if (!isValid) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Form Tidak Valid',
                text: 'Mohon periksa kembali data yang Anda masukkan',
                confirmButtonColor: '#dc3545'
            });
        } else {
            // Debug: log data yang akan dikirim
            const formData = new FormData(form);
            const debugData = {};
            for (let [key, value] of formData.entries()) {
                debugData[key] = value;
            }
            console.log('DEBUG SUBMIT DATA:', debugData);
            // Show loading
            document.getElementById('loadingOverlay').style.display = 'flex';
            // Convert amount back to number format for submission
            const cleanAmount = amountInput.value.replace(/[^\d]/g, '');
            amountInput.value = cleanAmount;
        }
    });
    
    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        const validationDiv = document.getElementById(field.id + '-validation');
        if (validationDiv) {
            validationDiv.textContent = message;
            validationDiv.style.display = 'block';
        }
    }
    
    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        const validationDiv = document.getElementById(field.id + '-validation');
        if (validationDiv) {
            validationDiv.style.display = 'none';
        }
    }
});

// File handling function
function handleFileSelect(input) {
    const file = input.files[0];
    const preview = document.getElementById('file-preview');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `
                <div class="d-flex align-items-center p-3 bg-light rounded">
                    <i class="fas fa-file fa-2x text-primary me-3"></i>
                    <div class="flex-grow-1">
                        <strong>${file.name}</strong>
                        <br>
                        <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeFile()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
}

function removeFile() {
    document.getElementById('attachment').value = '';
    document.getElementById('file-preview').style.display = 'none';
}

// Calculator functions
let calcDisplay = '';
let operator = '';
let firstOperand = '';
let waitingForNewOperand = false;

function openCalculator() {
    document.getElementById('calculatorPopup').style.display = 'block';
    document.getElementById('calcDisplay').value = '0';
}

function closeCalculator() {
    document.getElementById('calculatorPopup').style.display = 'none';
    
    // Set calculated amount to main form
    const result = document.getElementById('calcDisplay').value;
    if (result && result !== '0') {
        document.getElementById('amount').value = new Intl.NumberFormat('id-ID').format(result.replace(/[^\d]/g, ''));
    }
}

function calcNumber(num) {
    const display = document.getElementById('calcDisplay');
    
    if (waitingForNewOperand) {
        display.value = num;
        waitingForNewOperand = false;
    } else {
        display.value = display.value === '0' ? num : display.value + num;
    }
}

function calcOperation(nextOperator) {
    const display = document.getElementById('calcDisplay');
    const inputValue = parseFloat(display.value);

    if (firstOperand === '') {
        firstOperand = inputValue;
    } else if (operator) {
        const currentValue = firstOperand || 0;
        const newValue = calculate(currentValue, inputValue, operator);

        display.value = String(newValue);
        firstOperand = newValue;
    }

    waitingForNewOperand = true;
    operator = nextOperator;
}

function calculateResult() {
    const display = document.getElementById('calcDisplay');
    const inputValue = parseFloat(display.value);

    if (firstOperand !== '' && operator) {
        const newValue = calculate(firstOperand, inputValue, operator);
        display.value = String(newValue);
        firstOperand = '';
        operator = '';
        waitingForNewOperand = true;
    }
}

function calculate(firstOperand, secondOperand, operator) {
    switch (operator) {
        case '+':
            return firstOperand + secondOperand;
        case '-':
            return firstOperand - secondOperand;
        case '*':
            return firstOperand * secondOperand;
        case '/':
            return firstOperand / secondOperand;
        default:
            return secondOperand;
    }
}

function clearCalc() {
    document.getElementById('calcDisplay').value = '0';
    firstOperand = '';
    operator = '';
    waitingForNewOperand = false;
}

function backspace() {
    const display = document.getElementById('calcDisplay');
    display.value = display.value.slice(0, -1) || '0';
}

// Auto-save draft (optional feature)
function saveDraft() {
    const formData = new FormData(document.getElementById('transactionForm'));
    const draftData = {};
    
    for (let [key, value] of formData.entries()) {
        draftData[key] = value;
    }
    
    localStorage.setItem('transaction_draft', JSON.stringify(draftData));
}

function loadDraft() {
    const draft = localStorage.getItem('transaction_draft');
    if (draft) {
        const draftData = JSON.parse(draft);
        
        Object.keys(draftData).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                element.value = draftData[key];
            }
        });
    }
}

// Save draft every 30 seconds
setInterval(saveDraft, 30000);

// Load draft on page load
// loadDraft();
</script>
@endpush
@endsection
