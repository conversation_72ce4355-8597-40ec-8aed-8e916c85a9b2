@extends('layouts.dashboard')

@section('title', 'Manajemen Ang<PERSON>n')

@section('page-title', 'Manajemen Anggaran')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Manajemen Anggaran</h1>
                    <p class="text-muted">Kelola dan pantau anggaran keuangan Anda</p>
                </div>
                <div>
                    <a href="{{ route('budgets.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Buat Anggaran Baru
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Anggaran Aktif
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalActiveBudgets">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Anggaran Aman
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="safeBudgets">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Perlu Perhatian
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="warningBudgets">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Terlampaui
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="exceededBudgets">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Menu -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Menu Cepat</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-3">
                            <a href="{{ route('budgets.create') }}" class="text-decoration-none">
                                <div class="card budget-menu-card h-100 border-left-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                                        <h5 class="card-title">Buat Anggaran</h5>
                                        <p class="card-text text-muted">Buat anggaran baru untuk kategori tertentu</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-3">
                            <a href="{{ route('budgets.monitor') }}" class="text-decoration-none">
                                <div class="card budget-menu-card h-100 border-left-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                                        <h5 class="card-title">Monitor Anggaran</h5>
                                        <p class="card-text text-muted">Pantau progress dan penggunaan anggaran</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-3">
                            <a href="{{ route('budgets.comparison') }}" class="text-decoration-none">
                                <div class="card budget-menu-card h-100 border-left-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-balance-scale fa-3x text-warning mb-3"></i>
                                        <h5 class="card-title">Perbandingan</h5>
                                        <p class="card-text text-muted">Bandingkan anggaran antar periode</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Budgets -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Anggaran Terbaru</h6>
                    <a href="{{ route('budgets.monitor') }}" class="btn btn-sm btn-outline-primary">
                        Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    <div id="recentBudgets">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <p class="mt-2">Memuat anggaran terbaru...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Budget Status Overview -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Ringkasan Status</h6>
                </div>
                <div class="card-body">
                    <canvas id="budgetStatusChart" width="100" height="100"></canvas>
                </div>
            </div>

            <!-- Quick Tips -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Tips Anggaran</h6>
                </div>
                <div class="card-body">
                    <div id="budgetTips">
                        <div class="tip-item mb-3">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <small class="ml-2">Buat anggaran realistis berdasarkan rata-rata pengeluaran 3 bulan terakhir</small>
                        </div>
                        <div class="tip-item mb-3">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <small class="ml-2">Aktifkan notifikasi untuk mendapat peringatan saat anggaran hampir habis</small>
                        </div>
                        <div class="tip-item mb-3">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <small class="ml-2">Review anggaran setiap bulan dan sesuaikan jika diperlukan</small>
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <small class="ml-2">Sisakan 10-20% buffer untuk pengeluaran tak terduga</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Light Theme (Default) */
.budget-menu-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
}

.budget-menu-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    border-color: #4e73df;
}

.budget-item {
    padding: 15px;
    border-left: 4px solid #e3e6f0;
    margin-bottom: 15px;
    background: #f8f9fc;
    border-radius: 0 5px 5px 0;
    transition: all 0.3s ease;
}

.budget-item:hover {
    border-left-color: #4e73df;
    background: #fff;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.budget-item.status-safe {
    border-left-color: #1cc88a;
}

.budget-item.status-warning {
    border-left-color: #f6c23e;
}

.budget-item.status-danger {
    border-left-color: #e74a3b;
}

.budget-progress {
    height: 8px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.budget-progress-bar {
    height: 100%;
    transition: width 0.6s ease;
}

.tip-item {
    display: flex;
    align-items: flex-start;
}

.budget-amount {
    font-size: 1.1em;
    font-weight: 600;
}

.budget-category {
    font-size: 0.9em;
    color: #6c757d;
}

.budget-period {
    font-size: 0.8em;
    color: #858796;
}

/* Solarized Dark Theme */
body[data-theme="solarized-dark"] .card-body {
    background-color: #fdf6e3 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .card-header h6,
body[data-theme="solarized-dark"] h6 {
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .budget-menu-card {
    background-color: #fdf6e3 !important;
    border-color: #eee8d5 !important;
}

body[data-theme="solarized-dark"] .budget-menu-card:hover {
    border-color: #268bd2 !important;
    box-shadow: 0 0.5rem 1rem rgba(38, 139, 210, 0.15) !important;
}

body[data-theme="solarized-dark"] .budget-item {
    background: #eee8d5 !important;
    border-left-color: #93a1a1 !important;
}

body[data-theme="solarized-dark"] .budget-item:hover {
    background: #fdf6e3 !important;
    border-left-color: #268bd2 !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(38, 139, 210, 0.15) !important;
}

body[data-theme="solarized-dark"] .budget-category,
body[data-theme="solarized-dark"] .budget-period {
    color: #657b83 !important;
}

body[data-theme="solarized-dark"] .budget-progress {
    background: #d33682 !important;
}

/* Synth Wave Theme */
body[data-theme="synth-wave"] .card-body {
    background: #1a1a1a !important;
    color: #ffffff !important;
}

/* H6 styling untuk semua konteks dalam synth-wave theme */
body[data-theme="synth-wave"] h6,
body[data-theme="synth-wave"] .h6,
body[data-theme="synth-wave"] .card h6,
body[data-theme="synth-wave"] .card .h6,
body[data-theme="synth-wave"] .card-header h6,
body[data-theme="synth-wave"] .card-header .h6,
body[data-theme="synth-wave"] .card-body h6,
body[data-theme="synth-wave"] .card-body .h6,
body[data-theme="synth-wave"] .card.shadow h6,
body[data-theme="synth-wave"] .card.shadow .h6 {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Override untuk class text-primary */
body[data-theme="synth-wave"] h6.text-primary,
body[data-theme="synth-wave"] .h6.text-primary,
body[data-theme="synth-wave"] .card h6.text-primary,
body[data-theme="synth-wave"] .card .h6.text-primary,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .card-header h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .card-body h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .card h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .card-header h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] .card-body h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] .card h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-success {
    color: #00ffff !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
}

/* Override untuk semua h6 dalam card header dengan class apapun */
body[data-theme="synth-wave"] .card-header.py-3 h6 {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Override khusus untuk h6 dengan kombinasi class m-0 font-weight-bold */
body[data-theme="synth-wave"] h6[class*="m-0"][class*="font-weight-bold"] {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] h6[class*="m-0"][class*="font-weight-bold"][class*="text-success"] {
    color: #00ffff !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
}

/* CSS dengan specificity sangat tinggi untuk override Bootstrap */
body[data-theme="synth-wave"] .card.shadow .card-header.py-3 h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .card .card-header h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .container-fluid .card .card-header h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] div.card div.card-header h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Force override dengan inline style priority */
body[data-theme="synth-wave"] h6.text-primary[class*="m-0"][class*="font-weight-bold"] {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* CSS untuk card header dengan background synth-wave */
body[data-theme="synth-wave"] .card-header {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
    border-bottom: 1px solid #ff00ff !important;
}

body[data-theme="synth-wave"] .card-header h6 {
    color: white !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

/* Override yang sangat spesifik untuk text dalam card header */
body[data-theme="synth-wave"] .card.shadow .card-header.py-3 h6,
body[data-theme="synth-wave"] .card .card-header.py-3 h6,
body[data-theme="synth-wave"] .card-header h6.m-0,
body[data-theme="synth-wave"] .card-header h6.font-weight-bold,
body[data-theme="synth-wave"] .card-header h6.text-primary {
    color: white !important;
    text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
    background: transparent !important;
}

/* Force override untuk semua h6 di card header tanpa memandang class */
body[data-theme="synth-wave"] .card-header.py-3 > h6,
body[data-theme="synth-wave"] .card-header > h6 {
    color: white !important;
    text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    let statusChart = null;
    
    // Load initial data
    loadDashboardData();
    
    function loadDashboardData() {
        // Load summary stats
        $.ajax({
            url: '{{ route("budgets.api.dashboard") }}',
            method: 'GET',
            success: function(response) {
                updateStats(response.stats);
                updateRecentBudgets(response.recent_budgets);
                updateStatusChart(response.status_distribution);
            },
            error: function(xhr) {
                console.error('Error loading budget dashboard data:', xhr);
                showErrorState();
            }
        });
    }

    function updateStats(stats) {
        $('#totalActiveBudgets').text(stats.total_active + ' Anggaran');
        $('#safeBudgets').text(stats.safe_count + ' Anggaran');
        $('#warningBudgets').text(stats.warning_count + ' Anggaran');
        $('#exceededBudgets').text(stats.exceeded_count + ' Anggaran');
    }

    function updateRecentBudgets(budgets) {
        if (budgets.length === 0) {
            $('#recentBudgets').html(`
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-3"></i>
                    <h6>Belum ada anggaran</h6>
                    <p>Mulai dengan membuat anggaran pertama Anda</p>
                    <a href="{{ route('budgets.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Buat Anggaran
                    </a>
                </div>
            `);
            return;
        }

        let html = '';
        budgets.forEach(budget => {
            const percentage = budget.spent_amount > 0 ? 
                Math.round((budget.spent_amount / budget.amount) * 100) : 0;
            
            const statusClass = getStatusClass(percentage);
            const statusText = getStatusText(percentage);
            
            html += `
                <div class="budget-item ${statusClass}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <h6 class="mb-1">${budget.name}</h6>
                            <div class="budget-category">${budget.category ? budget.category.name : 'Tanpa Kategori'}</div>
                            <div class="budget-period">${formatDate(budget.start_date)} - ${formatDate(budget.end_date)}</div>
                        </div>
                        <div class="text-right">
                            <div class="budget-amount">${formatCurrency(budget.amount)}</div>
                            <small class="text-muted">Terpakai: ${formatCurrency(budget.spent_amount)}</small>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">Progress: ${percentage}%</small>
                        <span class="badge badge-${getStatusBadgeClass(percentage)}">${statusText}</span>
                    </div>
                    <div class="budget-progress">
                        <div class="budget-progress-bar bg-${getStatusColor(percentage)}" 
                             style="width: ${Math.min(percentage, 100)}%"></div>
                    </div>
                </div>
            `;
        });

        $('#recentBudgets').html(html);
    }

    function updateStatusChart(distribution) {
        if (statusChart) statusChart.destroy();

        const ctx = document.getElementById('budgetStatusChart').getContext('2d');
        statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Aman', 'Peringatan', 'Bahaya', 'Terlampaui'],
                datasets: [{
                    data: [
                        distribution.safe || 0,
                        distribution.warning || 0,
                        distribution.danger || 0,
                        distribution.exceeded || 0
                    ],
                    backgroundColor: [
                        '#1cc88a',
                        '#f6c23e',
                        '#e74a3b',
                        '#5a5c69'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + ' anggaran';
                            }
                        }
                    }
                }
            }
        });

        // Add center text
        Chart.register({
            id: 'centerText',
            beforeDraw: function(chart) {
                const ctx = chart.ctx;
                const centerX = chart.chartArea.left + (chart.chartArea.right - chart.chartArea.left) / 2;
                const centerY = chart.chartArea.top + (chart.chartArea.bottom - chart.chartArea.top) / 2;

                ctx.restore();
                const fontSize = 16;
                ctx.font = fontSize + "px Arial";
                ctx.fillStyle = '#5a5c69';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';

                const total = distribution.safe + distribution.warning + distribution.danger + distribution.exceeded;
                ctx.fillText(total, centerX, centerY - 10);
                
                ctx.font = "12px Arial";
                ctx.fillText('Anggaran', centerX, centerY + 10);
                ctx.save();
            }
        });
    }

    function showErrorState() {
        $('#recentBudgets').html(`
            <div class="text-center text-danger py-4">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h6>Terjadi Kesalahan</h6>
                <p>Gagal memuat data anggaran</p>
                <button class="btn btn-primary" onclick="loadDashboardData()">
                    <i class="fas fa-refresh"></i> Muat Ulang
                </button>
            </div>
        `);
    }

    function getStatusClass(percentage) {
        if (percentage < 75) return 'status-safe';
        if (percentage < 90) return 'status-warning';
        return 'status-danger';
    }

    function getStatusBadgeClass(percentage) {
        if (percentage < 75) return 'success';
        if (percentage < 90) return 'warning';
        return 'danger';
    }

    function getStatusColor(percentage) {
        if (percentage < 75) return 'success';
        if (percentage < 90) return 'warning';
        return 'danger';
    }

    function getStatusText(percentage) {
        if (percentage < 75) return 'Aman';
        if (percentage < 90) return 'Peringatan';
        if (percentage < 100) return 'Bahaya';
        return 'Terlampaui';
    }

    function formatCurrency(amount) {
        return 'Rp ' + parseInt(amount).toLocaleString('id-ID');
    }

    function formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
        });
    }

    // Auto refresh every 60 seconds
    setInterval(loadDashboardData, 60000);

    // Make loadDashboardData globally accessible
    window.loadDashboardData = loadDashboardData;
});
</script>
@endpush
@endsection
