@extends('layouts.dashboard')

@section('title', 'Perbandingan Anggaran')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Perbandingan Anggaran</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('budgets.index') }}">Anggaran</a></li>
                            <li class="breadcrumb-item active">Perbandingan</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-secondary" id="exportBtn">
                        <i class="fas fa-download"></i> Export
                    </button>
                    <a href="{{ route('budgets.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Buat Anggaran
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Comparison Settings -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Pengaturan Perbandingan</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="comparisonType">Tipe Perbandingan</label>
                                <select class="form-control" id="comparisonType">
                                    <option value="period">Periode vs Periode</option>
                                    <option value="category">Kategori vs Kategori</option>
                                    <option value="budget_vs_actual">Anggaran vs Realisasi</option>
                                    <option value="monthly_trend">Tren Bulanan</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3" id="period1Section">
                            <div class="form-group">
                                <label for="period1">Periode 1</label>
                                <select class="form-control" id="period1">
                                    <option value="this_month">Bulan Ini</option>
                                    <option value="last_month">Bulan Lalu</option>
                                    <option value="this_year">Tahun Ini</option>
                                    <option value="last_year">Tahun Lalu</option>
                                    <option value="custom">Kustom</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3" id="period2Section">
                            <div class="form-group">
                                <label for="period2">Periode 2</label>
                                <select class="form-control" id="period2">
                                    <option value="last_month">Bulan Lalu</option>
                                    <option value="this_month">Bulan Ini</option>
                                    <option value="last_year">Tahun Lalu</option>
                                    <option value="this_year">Tahun Ini</option>
                                    <option value="custom">Kustom</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button class="btn btn-primary" id="compareBtn">
                                        <i class="fas fa-chart-line"></i> Bandingkan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Custom Date Range (Hidden by default) -->
                    <div class="row d-none" id="customDateSection">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="customStart1">Tanggal Mulai 1</label>
                                <input type="date" class="form-control" id="customStart1">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="customEnd1">Tanggal Selesai 1</label>
                                <input type="date" class="form-control" id="customEnd1">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="customStart2">Tanggal Mulai 2</label>
                                <input type="date" class="form-control" id="customStart2">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="customEnd2">Tanggal Selesai 2</label>
                                <input type="date" class="form-control" id="customEnd2">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Comparison Results -->
    <div id="comparisonResults" class="d-none">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Periode 1 - Total
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="period1Total">-</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Periode 2 - Total
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="period2Total">-</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Selisih
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="difference">-</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Persentase Perubahan
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="percentageChange">-</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-percentage fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Grafik Perbandingan</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="comparisonChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Distribusi</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="distributionChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Comparison Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Detail Perbandingan</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="comparisonTable">
                                <thead>
                                    <tr id="comparisonTableHeader">
                                        <!-- Dynamic header based on comparison type -->
                                    </tr>
                                </thead>
                                <tbody id="comparisonTableBody">
                                    <!-- Dynamic content -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empty State -->
    <div id="emptyState" class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-chart-line fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">Pilih Parameter Perbandingan</h5>
                    <p class="text-muted">Pilih tipe perbandingan dan periode untuk melihat analisis data anggaran</p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Light Theme (Default) */
.comparison-card {
    transition: all 0.3s ease;
}

.comparison-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.trend-up {
    color: #1cc88a;
}

.trend-down {
    color: #e74a3b;
}

.trend-neutral {
    color: #36b9cc;
}

.percentage-badge {
    font-size: 0.8em;
    padding: 4px 8px;
}

#comparisonChart, #distributionChart {
    max-height: 400px;
}

.table th {
    background-color: #f8f9fc;
    border-top: none;
}

.comparison-value {
    font-weight: 600;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Solarized Dark Theme */
body[data-theme="solarized-dark"] .card-body {
    background-color: #fdf6e3 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .card-header h6,
body[data-theme="solarized-dark"] h6 {
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .comparison-card {
    background-color: #fdf6e3 !important;
    border-color: #eee8d5 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .comparison-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(38, 139, 210, 0.15) !important;
}

body[data-theme="solarized-dark"] .table {
    background-color: #fdf6e3 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .table th {
    background-color: #eee8d5 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .table th,
body[data-theme="solarized-dark"] .table td {
    border-color: #eee8d5 !important;
}

body[data-theme="solarized-dark"] .loading-overlay {
    background: rgba(253, 246, 227, 0.8) !important;
}

/* Synth Wave Theme */
body[data-theme="synth-wave"] .card-body {
    background: #1a1a1a !important;
    color: #ffffff !important;
}

/* H6 styling untuk semua konteks dalam synth-wave theme */
body[data-theme="synth-wave"] h6,
body[data-theme="synth-wave"] .h6,
body[data-theme="synth-wave"] .card h6,
body[data-theme="synth-wave"] .card .h6,
body[data-theme="synth-wave"] .card-header h6,
body[data-theme="synth-wave"] .card-header .h6,
body[data-theme="synth-wave"] .card-body h6,
body[data-theme="synth-wave"] .card-body .h6,
body[data-theme="synth-wave"] .card.shadow h6,
body[data-theme="synth-wave"] .card.shadow .h6 {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Override untuk class text-primary */
body[data-theme="synth-wave"] h6.text-primary,
body[data-theme="synth-wave"] .h6.text-primary,
body[data-theme="synth-wave"] .card h6.text-primary,
body[data-theme="synth-wave"] .card .h6.text-primary,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Override untuk class text-success */
body[data-theme="synth-wave"] h6.text-success,
body[data-theme="synth-wave"] .h6.text-success,
body[data-theme="synth-wave"] .card h6.text-success,
body[data-theme="synth-wave"] .card .h6.text-success,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold.text-success {
    color: #00ffff !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
}

/* Override untuk class m-0 font-weight-bold */
body[data-theme="synth-wave"] h6.m-0.font-weight-bold,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold,
body[data-theme="synth-wave"] .card h6.m-0.font-weight-bold,
body[data-theme="synth-wave"] .card .h6.m-0.font-weight-bold {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .comparison-card {
    background: #1a1a1a !important;
    border: 1px solid #ff00ff !important;
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .comparison-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 255, 255, 0.3) !important;
    transform: translateY(-5px);
}

body[data-theme="synth-wave"] .table {
    background: #1a1a1a !important;
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .table th {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
}

body[data-theme="synth-wave"] .table th,
body[data-theme="synth-wave"] .table td {
    border-color: #ff00ff !important;
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .comparison-value {
    color: #ff00ff !important;
    text-shadow: 0 0 5px rgba(255, 0, 255, 0.3) !important;
}

body[data-theme="synth-wave"] .loading-overlay {
    background: rgba(26, 26, 26, 0.8) !important;
}

body[data-theme="synth-wave"] .trend-up {
    color: #00ffff !important;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .trend-down {
    color: #ff00ff !important;
    text-shadow: 0 0 5px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .trend-neutral {
    color: #ffffff !important;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3) !important;
}

/* CSS dengan specificity sangat tinggi untuk override Bootstrap */
body[data-theme="synth-wave"] .card.shadow .card-header.py-3 h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .card .card-header h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .container-fluid .card .card-header h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] div.card div.card-header h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Force override dengan inline style priority */
body[data-theme="synth-wave"] h6.text-primary[class*="m-0"][class*="font-weight-bold"] {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* CSS untuk card header dengan background synth-wave */
body[data-theme="synth-wave"] .card-header {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
    border-bottom: 1px solid #ff00ff !important;
}

body[data-theme="synth-wave"] .card-header h6 {
    color: white !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

/* Override yang sangat spesifik untuk text dalam card header */
body[data-theme="synth-wave"] .card.shadow .card-header.py-3 h6,
body[data-theme="synth-wave"] .card .card-header.py-3 h6,
body[data-theme="synth-wave"] .card-header h6.m-0,
body[data-theme="synth-wave"] .card-header h6.font-weight-bold,
body[data-theme="synth-wave"] .card-header h6.text-primary {
    color: white !important;
    text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
    background: transparent !important;
}

/* Force override untuk semua h6 di card header tanpa memandang class */
body[data-theme="synth-wave"] .card-header.py-3 > h6,
body[data-theme="synth-wave"] .card-header > h6 {
    color: white !important;
    text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    let comparisonChart = null;
    let distributionChart = null;
    
    // Handle comparison type change
    $('#comparisonType').on('change', function() {
        const type = $(this).val();
        toggleSections(type);
    });

    // Handle period changes
    $('#period1, #period2').on('change', function() {
        const period1 = $('#period1').val();
        const period2 = $('#period2').val();
        
        if (period1 === 'custom' || period2 === 'custom') {
            $('#customDateSection').removeClass('d-none');
        } else {
            $('#customDateSection').addClass('d-none');
        }
    });

    // Handle compare button
    $('#compareBtn').on('click', function() {
        performComparison();
    });

    // Handle export button
    $('#exportBtn').on('click', function() {
        exportComparison();
    });

    function toggleSections(type) {
        if (type === 'category' || type === 'monthly_trend') {
            $('#period2Section').addClass('d-none');
            if (type === 'category') {
                $('#period1Section label').text('Periode');
            } else {
                $('#period1Section label').text('Tahun');
                $('#period1').html(`
                    <option value="this_year">Tahun Ini</option>
                    <option value="last_year">Tahun Lalu</option>
                    <option value="custom_year">Kustom</option>
                `);
            }
        } else {
            $('#period2Section').removeClass('d-none');
            $('#period1Section label').text('Periode 1');
            if (type === 'period') {
                $('#period1').html(`
                    <option value="this_month">Bulan Ini</option>
                    <option value="last_month">Bulan Lalu</option>
                    <option value="this_year">Tahun Ini</option>
                    <option value="last_year">Tahun Lalu</option>
                    <option value="custom">Kustom</option>
                `);
            }
        }
    }

    function performComparison() {
        const type = $('#comparisonType').val();
        const period1 = $('#period1').val();
        const period2 = $('#period2').val();
        
        const params = {
            type: type,
            period1: period1,
            period2: period2
        };

        // Add custom dates if needed
        if (period1 === 'custom') {
            params.custom_start1 = $('#customStart1').val();
            params.custom_end1 = $('#customEnd1').val();
        }
        if (period2 === 'custom') {
            params.custom_start2 = $('#customStart2').val();
            params.custom_end2 = $('#customEnd2').val();
        }

        // Show loading state
        showLoadingState();

        $.ajax({
            url: '{{ route("budgets.api.comparison") }}',
            method: 'GET',
            data: params,
            success: function(response) {
                hideLoadingState();
                displayComparisonResults(response);
            },
            error: function(xhr) {
                hideLoadingState();
                console.error('Error performing comparison:', xhr);
                alert('Terjadi kesalahan saat melakukan perbandingan');
            }
        });
    }

    function showLoadingState() {
        $('#emptyState').addClass('d-none');
        $('#comparisonResults').removeClass('d-none');
        $('#comparisonResults').append(`
            <div class="loading-overlay">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
            </div>
        `);
    }

    function hideLoadingState() {
        $('.loading-overlay').remove();
    }

    function displayComparisonResults(data) {
        $('#emptyState').addClass('d-none');
        $('#comparisonResults').removeClass('d-none');

        // Update summary cards
        updateSummaryCards(data.summary);

        // Update charts
        updateCharts(data);

        // Update table
        updateComparisonTable(data);
    }

    function updateSummaryCards(summary) {
        $('#period1Total').text(formatCurrency(summary.period1_total));
        $('#period2Total').text(formatCurrency(summary.period2_total || 0));
        
        const difference = summary.difference || 0;
        const percentageChange = summary.percentage_change || 0;
        
        $('#difference').html(formatCurrency(Math.abs(difference)) + 
            (difference >= 0 ? ' <i class="fas fa-arrow-up trend-up"></i>' : ' <i class="fas fa-arrow-down trend-down"></i>'));
        
        $('#percentageChange').html(Math.abs(percentageChange).toFixed(1) + '% ' +
            (percentageChange >= 0 ? '<i class="fas fa-arrow-up trend-up"></i>' : '<i class="fas fa-arrow-down trend-down"></i>'));
    }

    function updateCharts(data) {
        // Destroy existing charts
        if (comparisonChart) comparisonChart.destroy();
        if (distributionChart) distributionChart.destroy();

        // Create comparison chart
        const ctx1 = document.getElementById('comparisonChart').getContext('2d');
        comparisonChart = new Chart(ctx1, {
            type: data.chart_config.type || 'bar',
            data: data.chart_config.data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + value.toLocaleString('id-ID');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': Rp ' + 
                                       context.parsed.y.toLocaleString('id-ID');
                            }
                        }
                    }
                }
            }
        });

        // Create distribution chart
        const ctx2 = document.getElementById('distributionChart').getContext('2d');
        distributionChart = new Chart(ctx2, {
            type: 'doughnut',
            data: data.distribution_config.data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': Rp ' + 
                                       context.parsed.toLocaleString('id-ID');
                            }
                        }
                    }
                }
            }
        });
    }

    function updateComparisonTable(data) {
        // Update header
        $('#comparisonTableHeader').html(data.table_config.headers.map(header => 
            `<th>${header}</th>`
        ).join(''));

        // Update body
        let bodyHtml = '';
        data.table_config.rows.forEach(row => {
            bodyHtml += '<tr>';
            row.forEach((cell, index) => {
                if (index > 0 && typeof cell === 'number') {
                    bodyHtml += `<td class="text-right comparison-value">${formatCurrency(cell)}</td>`;
                } else {
                    bodyHtml += `<td>${cell}</td>`;
                }
            });
            bodyHtml += '</tr>';
        });
        $('#comparisonTableBody').html(bodyHtml);
    }

    function exportComparison() {
        const type = $('#comparisonType').val();
        const period1 = $('#period1').val();
        const period2 = $('#period2').val();
        
        const params = new URLSearchParams({
            type: type,
            period1: period1,
            period2: period2,
            export: 'excel'
        });

        // Add custom dates if needed
        if (period1 === 'custom') {
            params.append('custom_start1', $('#customStart1').val());
            params.append('custom_end1', $('#customEnd1').val());
        }
        if (period2 === 'custom') {
            params.append('custom_start2', $('#customStart2').val());
            params.append('custom_end2', $('#customEnd2').val());
        }

        window.open(`{{ route('budgets.api.comparison') }}?${params}`, '_blank');
    }

    function formatCurrency(amount) {
        return 'Rp ' + parseInt(amount).toLocaleString('id-ID');
    }

    // Initialize with default state
    toggleSections('period');
});
</script>
@endpush
@endsection
