<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Transaction;

class DebugTransaction extends Command
{
    protected $signature = 'debug:transaction {id}';
    protected $description = 'Debug transaction details';

    public function handle()
    {
        $id = $this->argument('id');
        
        $transaction = Transaction::with(['account', 'category'])->find($id);
        
        if (!$transaction) {
            $this->error("Transaction ID $id not found");
            return;
        }
        
        $this->info("Transaction Details:");
        $this->line("ID: " . $transaction->id);
        $this->line("User ID: " . $transaction->user_id);
        $this->line("Account ID: " . $transaction->account_id);
        $this->line("Account Name: " . ($transaction->account->name ?? 'NULL'));
        $this->line("Category ID: " . $transaction->category_id);
        $this->line("Category Name: " . ($transaction->category->name ?? 'NULL'));
        $this->line("Amount: " . $transaction->amount);
        $this->line("Type: " . $transaction->type);
        $this->line("Title: " . $transaction->title);
        
        // Check if account exists for user
        $this->info("\nAccount validation:");
        $accountExists = \App\Models\Account::where('id', $transaction->account_id)
            ->where('user_id', $transaction->user_id)
            ->exists();
        $this->line("Account exists for user: " . ($accountExists ? 'YES' : 'NO'));
        
        // Check if category exists for user
        $this->info("\nCategory validation:");
        if ($transaction->category_id) {
            $categoryExists = \App\Models\Category::where('id', $transaction->category_id)
                ->where('user_id', $transaction->user_id)
                ->exists();
            $this->line("Category exists for user: " . ($categoryExists ? 'YES' : 'NO'));
        } else {
            $this->line("No category (transfer transaction)");
        }
    }
}
