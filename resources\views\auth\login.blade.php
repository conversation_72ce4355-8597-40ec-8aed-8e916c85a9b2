<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'My Money') }} - Masuk</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />    <!-- Bootstrap & Custom CSS -->
    @vite(['resources/sass/app.scss'])
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Figtree', sans-serif;
        }
          .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
        }
          .auth-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 650px;
            height: 420px;
            display: flex;
        }
        
        .auth-left {
            flex: 1;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .auth-animation {
            text-align: center;
            color: white;
            z-index: 2;
        }
          .auth-animation h2 {
            font-size: 2rem;
            margin-bottom: 0.8rem;
            font-weight: 600;
        }
        
        .auth-animation p {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .shape {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            left: 10%;
            top: 20%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            left: 70%;
            top: 60%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            left: 20%;
            top: 80%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }        .auth-right {
            flex: 1;
            padding: 20px;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            position: relative;
            overflow: hidden;
            max-height: 420px;
        }        .form-container {
            width: 100%;
            max-width: 280px;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }.form-wrapper {
            position: relative;
            overflow: hidden;
            flex: 1;
            display: flex;
            flex-direction: column;
        }        /* Custom scrollbar untuk form */
        .form-wrapper::-webkit-scrollbar,
        .form-slide::-webkit-scrollbar {
            width: 4px;
        }
        
        .form-wrapper::-webkit-scrollbar-track,
        .form-slide::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .form-wrapper::-webkit-scrollbar-thumb,
        .form-slide::-webkit-scrollbar-thumb {
            background: #dee2e6;
            border-radius: 10px;
        }
        
        .form-wrapper::-webkit-scrollbar-thumb:hover,
        .form-slide::-webkit-scrollbar-thumb:hover {
            background: #adb5bd;
        }
          /* Styling untuk scrollbar pada Firefox */
        .form-slide {
            scrollbar-width: thin;
            scrollbar-color: #dee2e6 #f8f9fa;
        }
        
        /* Hide scrollbar but keep functionality */
        .form-slide::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }
        
        .form-slide {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }.form-slide {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            padding-bottom: 10px;
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 380px;
        }
        
        .form-slide.active {
            opacity: 1;
            transform: translateX(0);
            position: relative;
        }
        
        .form-slide.exit {
            opacity: 0;
            transform: translateX(-100%);
        }        .form-title {
            text-align: center;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .form-title h3 {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 0.2rem;
        }
        
        .form-title p {
            color: #666;
            font-size: 0.8rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }        .form-group label {
            display: block;
            margin-bottom: 0.3rem;
            color: #555;
            font-weight: 500;
            font-size: 0.85rem;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 0.85rem;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }        .captcha-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .captcha-refresh {
            background: #667eea;
            border: none;
            border-radius: 6px;
            color: white;
            padding: 6px 8px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s ease;
        }
        
        .captcha-refresh:hover {
            background: #5a6fd8;
        }
          .captcha-image {
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            cursor: pointer;
            width: 80px;
            height: 26px;
        }
          .form-check {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 1rem;
        }
        
        .form-check-input {
            margin: 0;
            width: 16px;
            height: 16px;
        }
          .form-check-label {
            margin: 0;
            font-size: 0.8rem;
            color: #555;
            cursor: pointer;
        }        .btn-primary {
            width: 100%;
            padding: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
        }
        
        .form-links {
            text-align: center;
            margin-top: 1rem;
        }
        
        .form-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin: 0 8px;
            font-size: 0.8rem;
        }
        
        .form-links a:hover {
            text-decoration: underline;
        }
          .alert {
            padding: 10px 14px;
            border-radius: 6px;
            margin-bottom: 1rem;
            border-left: 4px solid;
            font-size: 0.85rem;
        }
        
        .alert-danger {
            background-color: #fef2f2;
            border-left-color: #ef4444;
            color: #dc2626;
        }        @media (max-width: 768px) {
            .auth-card {
                flex-direction: column;
                margin: 10px;
                height: auto;
                max-height: 85vh;
                max-width: 100%;
            }
            
            .auth-left {
                min-height: 120px;
            }
            
            .auth-right {
                padding: 15px;
                max-height: none;
            }
            
            .form-container {
                max-width: none;
            }
            
            .form-slide {
                max-height: none;
            }
        }
        
        @media (max-width: 480px) {
            .auth-container {
                padding: 10px;
            }
            
            .auth-card {
                max-width: 100%;
                border-radius: 15px;
            }
            
            .captcha-container {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            
            .captcha-image {
                width: 90px;
                height: 30px;
            }
              .captcha-input {
                max-width: 90px;
            }
        }
        
        /* ===== THEME SWITCHER STYLES ===== */
        .theme-switcher-container {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .theme-switcher {
            position: relative;
        }
        
        .theme-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 8px 16px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            min-width: 120px;
        }
        
        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }
        
        .theme-icon {
            font-size: 16px;
        }
        
        .theme-text {
            font-weight: 500;
        }
        
        .dropdown-arrow {
            font-size: 10px;
            margin-left: auto;
            transition: transform 0.3s ease;
        }
        
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 8px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #333;
            font-size: 0.85rem;
        }
        
        .theme-option:hover {
            background-color: #f8f9fa;
        }
        
        .theme-option.active {
            background-color: #667eea;
            color: white;
        }
        
        /* ===== TEMA STYLES ===== */
        
        /* Light Theme (Default) */
        body {
            transition: all 0.3s ease;
        }
        
        /* Solarized Dark Theme */
        body[data-theme="solarized-dark"] {
            background: linear-gradient(135deg, #002b36 0%, #073642 100%);
        }
        
        body[data-theme="solarized-dark"] .auth-left {
            background: linear-gradient(45deg, #073642, #002b36);
        }
        
        body[data-theme="solarized-dark"] .auth-card {
            background: #073642;
            border: 1px solid #586e75;
        }
        
        body[data-theme="solarized-dark"] .form-control {
            background: #002b36;
            border-color: #586e75;
            color: #839496;
        }
        
        body[data-theme="solarized-dark"] .form-control:focus {
            border-color: #268bd2;
            background: #002b36;
        }
        
        body[data-theme="solarized-dark"] .form-title h3,
        body[data-theme="solarized-dark"] .form-group label {
            color: #93a1a1;
        }
        
        body[data-theme="solarized-dark"] .form-title p {
            color: #657b83;
        }
        
        body[data-theme="solarized-dark"] .btn-primary {
            background: linear-gradient(45deg, #268bd2, #2aa198);
        }
        
        body[data-theme="solarized-dark"] .form-links a {
            color: #268bd2;
        }
        
        /* Synth Wave Theme */
        body[data-theme="synth-wave"] {
            background: linear-gradient(135deg, #0f0f23 0%, #1a0033 50%, #2d1b69 100%);
        }
        
        body[data-theme="synth-wave"] .auth-left {
            background: linear-gradient(45deg, #ff00ff, #00ffff, #ff0080);
            position: relative;
        }
        
        body[data-theme="synth-wave"] .auth-left::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                90deg,
                transparent,
                transparent 2px,
                rgba(255, 0, 255, 0.1) 2px,
                rgba(255, 0, 255, 0.1) 4px
            );
        }
        
        body[data-theme="synth-wave"] .auth-card {
            background: #1a0033;
            border: 2px solid #ff00ff;
            box-shadow: 0 0 30px rgba(255, 0, 255, 0.3);
        }
        
        body[data-theme="synth-wave"] .form-control {
            background: #0f0f23;
            border-color: #00ffff;
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .form-control:focus {
            border-color: #ff00ff;
            box-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .form-title h3 {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .form-group label {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .form-title p {
            color: #ff0080;
        }
        
        body[data-theme="synth-wave"] .btn-primary {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.4);
        }
        
        body[data-theme="synth-wave"] .form-links a {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .captcha-image {
            border-color: #ff00ff;
        }
    </style>
</head>
<body>    <!-- Theme Switcher -->
    <div class="theme-switcher-container">
        <div class="theme-switcher">            <button class="theme-toggle" id="themeToggle" onclick="toggleThemeDropdown()">
                <i class="theme-icon" id="themeIcon">🌞</i>
                <span class="theme-text" id="themeText">Light</span>
                <i class="dropdown-arrow">▼</i>
            </button>
            <div class="theme-dropdown" id="themeDropdown">
                <div class="theme-option" onclick="setTheme('light')">
                    <i class="theme-option-icon">🌞</i>
                    <span>Light</span>
                </div>
                <div class="theme-option" onclick="setTheme('solarized-dark')">
                    <i class="theme-option-icon">🌙</i>
                    <span>Solarized Dark</span>
                </div>
                <div class="theme-option" onclick="setTheme('synth-wave')">
                    <i class="theme-option-icon">🌆</i>
                    <span>Synth Wave</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="auth-container">
        <div class="auth-card">
            <!-- Left Side - Animation -->
            <div class="auth-left">
                <div class="floating-shapes">
                    <div class="shape"></div>
                    <div class="shape"></div>
                    <div class="shape"></div>
                </div>
                <div class="auth-animation">
                    <h2 id="animated-title">Selamat Datang!</h2>
                    <p id="animated-subtitle">Kelola keuangan Anda dengan mudah</p>
                </div>
            </div>
            
            <!-- Right Side - Forms -->
            <div class="auth-right">
                <div class="form-container">
                    <div class="form-wrapper">                        <!-- Login Form -->
                        <div id="login-form" class="form-slide active">
                            <div class="form-title">
                                <h3>Masuk</h3>
                                <p>Silakan masuk ke akun Anda</p>
                            </div>
                            
                            <form method="POST" action="{{ route('login') }}">
                                @csrf
                                
                                <div class="form-group">
                                    <label for="username">Username</label>
                                    <input id="username" type="text" class="form-control @error('username') is-invalid @enderror" 
                                           name="username" value="{{ old('username') }}" required autofocus>
                                </div>
                                
                                <div class="form-group">
                                    <label for="password">Password</label>
                                    <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" 
                                           name="password" required>
                                </div>                                <div class="form-group">
                                    <label for="captcha">Captcha</label>
                                    <div class="captcha-container">
                                        <img src="{{ route('captcha.generate') }}" class="captcha-image" id="captcha-image" 
                                             onclick="refreshCaptcha()" title="Klik untuk refresh captcha">
                                        <button type="button" class="captcha-refresh" onclick="refreshCaptcha()" title="Refresh Captcha">
                                            🔄
                                        </button>
                                        <input id="captcha" type="text" class="form-control captcha-input @error('captcha') is-invalid @enderror" 
                                               name="captcha" required maxlength="4" placeholder="Kode">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="remember" id="remember">
                                        <label class="form-check-label" for="remember">
                                            Ingat saya
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn-primary">Masuk</button>
                            </form>
                              <div class="form-links">
                                <a href="#" onclick="showForm('register')">Daftar Baru</a> |
                                <a href="#" onclick="showForm('forgot')">Lupa Password?</a> |
                                <a href="#" onclick="showForm('verify')">Verifikasi Email</a>
                            </div>
                        </div>
                        
                        <!-- Registration Form -->
                        <div id="register-form" class="form-slide">
                            <div class="form-title">
                                <h3>Daftar Baru</h3>
                                <p>Buat akun baru untuk memulai</p>
                            </div>
                            
                            <form method="POST" action="{{ route('register') }}">
                                @csrf
                                
                                <div class="form-group">
                                    <label for="reg_name">Nama Lengkap</label>
                                    <input id="reg_name" type="text" class="form-control" name="name" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="reg_username">Username</label>
                                    <input id="reg_username" type="text" class="form-control" name="username" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="reg_email">Email</label>
                                    <input id="reg_email" type="email" class="form-control" name="email" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="reg_password">Password</label>
                                    <input id="reg_password" type="password" class="form-control" name="password" required minlength="8">
                                </div>
                                
                                <div class="form-group">
                                    <label for="reg_password_confirmation">Konfirmasi Password</label>
                                    <input id="reg_password_confirmation" type="password" class="form-control" name="password_confirmation" required>
                                </div>
                                
                                <button type="submit" class="btn-primary">Daftar</button>
                            </form>
                            
                            <div class="form-links">
                                <a href="#" onclick="showForm('login')">Sudah punya akun? Masuk</a>
                            </div>
                        </div>
                          <!-- Forgot Password Form -->
                        <div id="forgot-form" class="form-slide">
                            <div class="form-title">
                                <h3>Lupa Password</h3>
                                <p>Masukkan email untuk reset password</p>
                            </div>
                            
                            <form method="POST" action="{{ route('password.email') }}">
                                @csrf
                                
                                <div class="form-group">
                                    <label for="forgot_email">Email</label>
                                    <input id="forgot_email" type="email" class="form-control" name="email" required>
                                </div>
                                
                                <button type="submit" class="btn-primary">Kirim Link Reset</button>
                            </form>
                            
                            <div class="form-links">
                                <a href="#" onclick="showForm('login')">Kembali ke Login</a>
                            </div>
                        </div>
                        
                        <!-- Email Verification Form -->
                        <div id="verify-form" class="form-slide">
                            <div class="form-title">
                                <h3>Verifikasi Email</h3>
                                <p>Kirim ulang link verifikasi ke email Anda</p>
                            </div>
                            
                            <form method="POST" action="{{ route('verification.resend') }}">
                                @csrf
                                
                                <div class="form-group">
                                    <label for="verify_email">Email</label>
                                    <input id="verify_email" type="email" class="form-control" name="email" required>
                                </div>
                                
                                <button type="submit" class="btn-primary">Kirim Verifikasi</button>
                            </form>
                            
                            <div class="form-links">
                                <a href="#" onclick="showForm('login')">Kembali ke Login</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    @vite(['resources/js/app.js'])
    
    <script>
        let currentForm = 'login';
          const animations = {
            login: {
                title: "Selamat Datang!",
                subtitle: "Kelola keuangan Anda dengan mudah"
            },
            register: {
                title: "Bergabung Bersama Kami!",
                subtitle: "Mulai perjalanan finansial Anda"
            },
            forgot: {
                title: "Reset Password",
                subtitle: "Kami akan membantu Anda"
            },
            verify: {
                title: "Verifikasi Email",
                subtitle: "Aktifkan akun Anda sekarang"
            }
        };
        
        function showForm(formType) {
            if (currentForm === formType) return;
            
            const currentFormEl = document.getElementById(currentForm + '-form');
            const newFormEl = document.getElementById(formType + '-form');
            
            // Animation sequence
            currentFormEl.classList.add('exit');
            
            setTimeout(() => {
                currentFormEl.classList.remove('active', 'exit');
                newFormEl.classList.add('active');
                
                // Update left side animation
                updateAnimation(formType);
                
                currentForm = formType;
            }, 250);
        }
        
        function updateAnimation(formType) {
            const titleEl = document.getElementById('animated-title');
            const subtitleEl = document.getElementById('animated-subtitle');
            
            titleEl.style.transform = 'translateY(-20px)';
            titleEl.style.opacity = '0';
            subtitleEl.style.transform = 'translateY(-20px)';
            subtitleEl.style.opacity = '0';
            
            setTimeout(() => {
                titleEl.textContent = animations[formType].title;
                subtitleEl.textContent = animations[formType].subtitle;
                
                titleEl.style.transform = 'translateY(0)';
                titleEl.style.opacity = '1';
                subtitleEl.style.transform = 'translateY(0)';
                subtitleEl.style.opacity = '1';
            }, 250);
        }
        
        function refreshCaptcha() {
            const img = document.getElementById('captcha-image');
            img.src = '{{ route("captcha.generate") }}?' + Date.now();
        }        // Add smooth transitions
        document.addEventListener('DOMContentLoaded', function() {
            const titleEl = document.getElementById('animated-title');
            const subtitleEl = document.getElementById('animated-subtitle');
            
            titleEl.style.transition = 'all 0.5s ease';
            subtitleEl.style.transition = 'all 0.5s ease';
            
            // Initialize theme
            initializeTheme();
            
            // Handle flash messages with SweetAlert
            handleFlashMessages();
        });
          // ===== SWEETALERT FLASH MESSAGES =====
        function handleFlashMessages() {
            @if(session('success'))
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: '{{ session('success') }}',
                    confirmButtonColor: '#667eea',
                    timer: 3000,
                    timerProgressBar: true,
                    showConfirmButton: false,
                    toast: true,
                    position: 'top-end'
                });
            @endif
            
            @if(session('error'))
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: '{{ session('error') }}',
                    confirmButtonColor: '#667eea'
                });
            @endif
            
            @if(session('warning'))
                Swal.fire({
                    icon: 'warning',
                    title: 'Peringatan!',
                    text: '{{ session('warning') }}',
                    confirmButtonColor: '#667eea'
                });
            @endif
            
            @if(session('info'))
                Swal.fire({
                    icon: 'info',
                    title: 'Informasi',
                    text: '{{ session('info') }}',
                    confirmButtonColor: '#667eea',
                    timer: 3000,
                    timerProgressBar: true,
                    showConfirmButton: false,
                    toast: true,
                    position: 'top-end'
                });
            @endif
            
            @if(session('feedback'))
                Swal.fire({
                    icon: 'question',
                    title: 'Feedback',
                    text: '{{ session('feedback') }}',
                    confirmButtonColor: '#667eea'
                });
            @endif
            
            @if(session('unauthorized'))
                Swal.fire({
                    icon: 'error',
                    title: 'Akses Ditolak!',
                    text: '{{ session('unauthorized') }}',
                    confirmButtonColor: '#667eea'
                });
            @endif
            
            @if($errors->any())
                let errorMessages = [];
                @foreach ($errors->all() as $error)
                    errorMessages.push('{{ $error }}');
                @endforeach
                
                Swal.fire({
                    icon: 'warning',
                    title: 'Validasi Error',
                    html: errorMessages.join('<br>'),
                    confirmButtonColor: '#667eea'
                });
            @endif
        }
        
        // ===== THEME SWITCHER FUNCTIONALITY =====
        
        function toggleThemeDropdown() {
            const dropdown = document.getElementById('themeDropdown');
            const arrow = document.querySelector('.dropdown-arrow');
            
            dropdown.classList.toggle('show');
            arrow.style.transform = dropdown.classList.contains('show') ? 'rotate(180deg)' : 'rotate(0)';
        }
        
        function setTheme(theme) {
            // Set theme attribute
            document.body.setAttribute('data-theme', theme);
            
            // Save to localStorage
            localStorage.setItem('selectedTheme', theme);
            
            // Update theme switcher display
            updateThemeDisplay(theme);
            
            // Close dropdown
            document.getElementById('themeDropdown').classList.remove('show');
            document.querySelector('.dropdown-arrow').style.transform = 'rotate(0)';
            
            // Update active theme option
            updateActiveThemeOption(theme);
        }
        
        function updateThemeDisplay(theme) {
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');
            
            const themes = {
                'light': { icon: '🌞', text: 'Light' },
                'solarized-dark': { icon: '🌙', text: 'Solarized Dark' },
                'synth-wave': { icon: '🌆', text: 'Synth Wave' }
            };
            
            if (themes[theme]) {
                themeIcon.textContent = themes[theme].icon;
                themeText.textContent = themes[theme].text;
            }
        }
        
        function updateActiveThemeOption(activeTheme) {
            const options = document.querySelectorAll('.theme-option');
            options.forEach(option => {
                option.classList.remove('active');
            });
            
            const activeOption = document.querySelector(`.theme-option[onclick="setTheme('${activeTheme}')"]`);
            if (activeOption) {
                activeOption.classList.add('active');
            }
        }
        
        function initializeTheme() {
            // Get saved theme or default to light
            const savedTheme = localStorage.getItem('selectedTheme') || 'light';
            
            // Apply theme
            setTheme(savedTheme);
        }        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const themeSwitcher = document.querySelector('.theme-switcher');
            const dropdown = document.getElementById('themeDropdown');
            
            if (!themeSwitcher.contains(event.target)) {
                dropdown.classList.remove('show');
                document.querySelector('.dropdown-arrow').style.transform = 'rotate(0)';
            }
        });
    </script>
</body>
</html>
