@extends('layouts.dashboard')

@section('title', 'Monitor Anggaran')

@section('page-title', 'Monitor Anggaran')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Monitor Anggaran</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('budgets.index') }}">Anggaran</a></li>
                            <li class="breadcrumb-item active">Monitor</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('budgets.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Buat <PERSON>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body py-3">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <select class="form-control" id="periodFilter">
                                <option value="current">Periode Aktif</option>
                                <option value="this_month">Bulan Ini</option>
                                <option value="last_month">Bulan Lalu</option>
                                <option value="this_year">Tahun Ini</option>
                                <option value="all">Semua Periode</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="categoryFilter">
                                <option value="">Semua Kategori</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="statusFilter">
                                <option value="">Semua Status</option>
                                <option value="safe">Aman (&lt; 75%)</option>
                                <option value="warning">Peringatan (75-90%)</option>
                                <option value="danger">Bahaya (&gt; 90%)</option>
                                <option value="exceeded">Terlampaui</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-secondary" id="resetFilter">
                                <i class="fas fa-refresh"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4" id="summaryCards">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Anggaran
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalBudget">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Terpakai
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalSpent">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Sisa Anggaran
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="remainingBudget">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-piggy-bank fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Rata-rata Pemakaian
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="averageUsage">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Budget List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Daftar Anggaran</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="budgetTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Nama Anggaran</th>
                                    <th>Kategori</th>
                                    <th>Periode</th>
                                    <th>Anggaran</th>
                                    <th>Terpakai</th>
                                    <th>Sisa</th>
                                    <th>Progress</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="budgetTableBody">
                                <tr>
                                    <td colspan="9" class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="sr-only">Loading...</span>
                                        </div>
                                        <p class="mt-2">Memuat data anggaran...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detail Modal -->
<div class="modal fade" id="budgetDetailModal" tabindex="-1" role="dialog" aria-labelledby="budgetDetailModalLabel" aria-hidden="true" data-backdrop="true" data-keyboard="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="budgetDetailModalLabel">Detail Anggaran</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeBudgetDetailModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="budgetDetailContent">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeBudgetDetailModal()">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="budgetEditModal" tabindex="-1" role="dialog" aria-labelledby="budgetEditModalLabel" aria-hidden="true" data-backdrop="true" data-keyboard="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="budgetEditModalLabel">Edit Anggaran</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeBudgetEditModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="budgetEditContent">
                <form id="budgetEditForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editBudgetName"><strong>Nama Anggaran *</strong></label>
                                <input type="text" class="form-control" id="editBudgetName" name="name" required maxlength="255" placeholder="Masukkan nama anggaran">
                                <div class="invalid-feedback" id="editNameError"></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="editBudgetCategory"><strong>Kategori</strong></label>
                                <select class="form-control" id="editBudgetCategory" name="category_id">
                                    <option value="">Pilih Kategori (Opsional)</option>
                                </select>
                                <div class="invalid-feedback" id="editCategoryError"></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="editBudgetAmount"><strong>Jumlah Anggaran *</strong></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Rp</span>
                                    </div>
                                    <input type="number" class="form-control" id="editBudgetAmount" name="amount" required min="0" step="1000" placeholder="0">
                                </div>
                                <div class="invalid-feedback" id="editAmountError"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editBudgetType"><strong>Tipe Anggaran *</strong></label>
                                <select class="form-control" id="editBudgetType" name="type" required>
                                    <option value="">Pilih Tipe</option>
                                    <option value="monthly">Bulanan</option>
                                    <option value="yearly">Tahunan</option>
                                    <option value="custom">Kustom</option>
                                </select>
                                <div class="invalid-feedback" id="editTypeError"></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="editBudgetStartDate"><strong>Tanggal Mulai *</strong></label>
                                <input type="date" class="form-control" id="editBudgetStartDate" name="start_date" required>
                                <div class="invalid-feedback" id="editStartDateError"></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="editBudgetEndDate"><strong>Tanggal Berakhir *</strong></label>
                                <input type="date" class="form-control" id="editBudgetEndDate" name="end_date" required>
                                <div class="invalid-feedback" id="editEndDateError"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="editBudgetDescription"><strong>Deskripsi</strong></label>
                        <textarea class="form-control" id="editBudgetDescription" name="description" rows="3" placeholder="Deskripsi anggaran (opsional)"></textarea>
                        <div class="invalid-feedback" id="editDescriptionError"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeBudgetEditModal()">Batal</button>
                <button type="button" class="btn btn-primary" id="saveBudgetBtn" onclick="saveBudgetEdit()">
                    <i class="fas fa-save"></i> Simpan Perubahan
                </button>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Light Theme (Default) */
.progress-budget {
    height: 20px;
}

.badge-status {
    font-size: 0.8em;
    padding: 4px 8px;
}

.budget-card {
    transition: transform 0.2s;
}

.budget-card:hover {
    transform: translateY(-2px);
}

.progress-text {
    position: absolute;
    width: 100%;
    text-align: center;
    line-height: 20px;
    color: white;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}

.table td {
    vertical-align: middle;
}

.btn-action {
    padding: 4px 8px;
    font-size: 12px;
    margin: 0 1px;
    min-width: 30px;
    height: 30px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-action:hover {
    transform: scale(1.05);
    text-decoration: none;
}

.btn-action i {
    font-size: 12px;
    pointer-events: none;
}

/* Ensure action buttons are clickable */
.table td:last-child {
    white-space: nowrap;
    min-width: 120px;
    padding: 8px;
}

.action-buttons {
    display: flex;
    gap: 2px;
    justify-content: flex-start;
}

/* Solarized Dark Theme */
body[data-theme="solarized-dark"] .card-body {
    background-color: #fdf6e3 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .card-header h6,
body[data-theme="solarized-dark"] h6 {
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .budget-card {
    background-color: #fdf6e3 !important;
    border-color: #eee8d5 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .budget-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(38, 139, 210, 0.15) !important;
}

body[data-theme="solarized-dark"] .table {
    background-color: #fdf6e3 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .table th,
body[data-theme="solarized-dark"] .table td {
    border-color: #eee8d5 !important;
    color: #586e75 !important;
}

body[data-theme="solarized-dark"] .progress-budget {
    background-color: #eee8d5 !important;
}

body[data-theme="solarized-dark"] .btn-action {
    border: 1px solid rgba(0,0,0,0.1);
}

body[data-theme="solarized-dark"] .btn-action:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Synth Wave Theme */
body[data-theme="synth-wave"] .card-body {
    background: #1a1a1a !important;
    color: #ffffff !important;
}

/* H6 styling untuk semua konteks dalam synth-wave theme */
body[data-theme="synth-wave"] h6,
body[data-theme="synth-wave"] .h6,
body[data-theme="synth-wave"] .card h6,
body[data-theme="synth-wave"] .card .h6,
body[data-theme="synth-wave"] .card-header h6,
body[data-theme="synth-wave"] .card-header .h6,
body[data-theme="synth-wave"] .card-body h6,
body[data-theme="synth-wave"] .card-body .h6,
body[data-theme="synth-wave"] .card.shadow h6,
body[data-theme="synth-wave"] .card.shadow .h6 {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Override untuk class text-primary */
body[data-theme="synth-wave"] h6.text-primary,
body[data-theme="synth-wave"] .h6.text-primary,
body[data-theme="synth-wave"] .card h6.text-primary,
body[data-theme="synth-wave"] .card .h6.text-primary,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Override untuk class text-success */
body[data-theme="synth-wave"] h6.text-success,
body[data-theme="synth-wave"] .h6.text-success,
body[data-theme="synth-wave"] .card h6.text-success,
body[data-theme="synth-wave"] .card .h6.text-success,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold.text-success {
    color: #00ffff !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
}

/* Override untuk class m-0 font-weight-bold */
body[data-theme="synth-wave"] h6.m-0.font-weight-bold,
body[data-theme="synth-wave"] .h6.m-0.font-weight-bold,
body[data-theme="synth-wave"] .card h6.m-0.font-weight-bold,
body[data-theme="synth-wave"] .card .h6.m-0.font-weight-bold {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .budget-card {
    background: #1a1a1a !important;
    border: 1px solid #ff00ff !important;
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .budget-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 255, 255, 0.3) !important;
    transform: translateY(-2px);
}

body[data-theme="synth-wave"] .table {
    background: #1a1a1a !important;
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .table th,
body[data-theme="synth-wave"] .table td {
    border-color: #ff00ff !important;
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .table th {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
}

body[data-theme="synth-wave"] .progress-budget {
    background-color: #1a1a1a !important;
    border: 1px solid #ff00ff !important;
}

body[data-theme="synth-wave"] .progress-bar {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    box-shadow: 0 0 10px rgba(255, 0, 255, 0.3) !important;
}

body[data-theme="synth-wave"] .badge {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    color: white !important;
    box-shadow: 0 0 10px rgba(255, 0, 255, 0.3) !important;
}

/* CSS dengan specificity tinggi untuk kombinasi class Bootstrap */
body[data-theme="synth-wave"] .card-header h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .card-body h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] .card h6.m-0.font-weight-bold.text-primary,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-primary {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .card-header h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] .card-body h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] .card h6.m-0.font-weight-bold.text-success,
body[data-theme="synth-wave"] h6.m-0.font-weight-bold.text-success {
    color: #00ffff !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
}

/* Override untuk semua h6 dalam card header dengan class apapun */
body[data-theme="synth-wave"] .card-header.py-3 h6 {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Modal styling untuk semua tema - simplified */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    z-index: 1050 !important;
}

.modal-dialog {
    z-index: 1060 !important;
    margin: 1.75rem auto;
}

.modal-content {
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    z-index: 1061 !important;
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

/* Light Theme Modal */
body[data-theme="light"] .modal-content {
    background-color: #ffffff;
    color: #333333;
}

body[data-theme="light"] .modal-header {
    background-color: #f8f9fa;
    border-bottom-color: #dee2e6;
}

body[data-theme="light"] .modal-footer {
    background-color: #f8f9fa;
    border-top-color: #dee2e6;
}

/* Solarized Dark Modal */
body[data-theme="solarized-dark"] .modal-content {
    background-color: #002b36;
    color: #839496;
    border: 1px solid #073642;
}

body[data-theme="solarized-dark"] .modal-header {
    background-color: #073642;
    border-bottom-color: #073642;
    color: #839496;
}

body[data-theme="solarized-dark"] .modal-footer {
    background-color: #073642;
    border-top-color: #073642;
}

body[data-theme="solarized-dark"] .modal-title {
    color: #839496 !important;
}

body[data-theme="solarized-dark"] .modal-body .table {
    color: #839496 !important;
}

body[data-theme="solarized-dark"] .modal-body .table td {
    border-color: #073642 !important;
}

/* Synth-wave Modal */
body[data-theme="synth-wave"] .modal-content {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a0a1a 50%, #0a1a1a 100%);
    color: #ffffff;
    border: 2px solid #ff00ff;
    box-shadow: 0 0 30px rgba(255, 0, 255, 0.3);
}

body[data-theme="synth-wave"] .modal-header {
    background: linear-gradient(45deg, #ff00ff, #00ffff);
    border-bottom: 2px solid #ff00ff;
    color: #ffffff;
}

body[data-theme="synth-wave"] .modal-footer {
    background: linear-gradient(45deg, #1a0a1a, #0a1a1a);
    border-top: 2px solid #ff00ff;
}

body[data-theme="synth-wave"] .modal-title {
    color: #ffffff !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

body[data-theme="synth-wave"] .modal-body h6 {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .modal-body .table {
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .modal-body .table td {
    border-color: #ff00ff !important;
}

body[data-theme="synth-wave"] .modal-backdrop {
    background-color: rgba(255, 0, 255, 0.1) !important;
}

/* Close button styling */
.modal-header .close {
    background: none;
    border: none;
    font-size: 1.5rem;
    opacity: 0.7;
    cursor: pointer;
    color: inherit;
}

.modal-header .close:hover {
    opacity: 1;
}

body[data-theme="synth-wave"] .modal-header .close {
    color: #ffffff !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

/* Edit Modal Additional Styling */
#budgetEditModal .form-group label {
    margin-bottom: 5px;
    font-weight: 600;
}

#budgetEditModal .form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

#budgetEditModal .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#budgetEditModal .input-group-text {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-right: none;
}

#budgetEditModal .invalid-feedback {
    display: block;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

#budgetEditModal .is-invalid {
    border-color: #dc3545;
}

/* Theme-specific styling for edit modal */

/* Solarized Dark Edit Modal */
body[data-theme="solarized-dark"] #budgetEditModal .form-control {
    background-color: #073642;
    border-color: #586e75;
    color: #839496;
}

body[data-theme="solarized-dark"] #budgetEditModal .form-control:focus {
    background-color: #073642;
    border-color: #268bd2;
    color: #839496;
    box-shadow: 0 0 0 0.2rem rgba(38, 139, 210, 0.25);
}

body[data-theme="solarized-dark"] #budgetEditModal .input-group-text {
    background-color: #002b36;
    border-color: #586e75;
    color: #839496;
}

body[data-theme="solarized-dark"] #budgetEditModal label {
    color: #839496;
}

body[data-theme="solarized-dark"] #budgetEditModal .form-control option {
    background-color: #073642;
    color: #839496;
}

/* Synth-wave Edit Modal */
body[data-theme="synth-wave"] #budgetEditModal .form-control {
    background-color: #1a0a1a;
    border-color: #ff00ff;
    color: #ffffff;
}

body[data-theme="synth-wave"] #budgetEditModal .form-control:focus {
    background-color: #1a0a1a;
    border-color: #00ffff;
    color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25);
}

body[data-theme="synth-wave"] #budgetEditModal .input-group-text {
    background: linear-gradient(45deg, #ff00ff, #00ffff);
    border-color: #ff00ff;
    color: #ffffff;
    font-weight: bold;
}

body[data-theme="synth-wave"] #budgetEditModal label {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
}

body[data-theme="synth-wave"] #budgetEditModal .form-control option {
    background-color: #1a0a1a;
    color: #ffffff;
}

body[data-theme="synth-wave"] #budgetEditModal .form-control::placeholder {
    color: #888888;
}

/* Modal buttons styling */
#budgetEditModal .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

#budgetEditModal .btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

body[data-theme="synth-wave"] #budgetEditModal .btn-primary {
    background: linear-gradient(45deg, #ff00ff, #00ffff);
    border: none;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.3);
}

body[data-theme="synth-wave"] #budgetEditModal .btn-primary:hover {
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

body[data-theme="solarized-dark"] #budgetEditModal .btn-primary {
    background-color: #268bd2;
    border-color: #268bd2;
}

body[data-theme="solarized-dark"] #budgetEditModal .btn-primary:hover {
    background-color: #2176bd;
    border-color: #2176bd;
}

/* SweetAlert Edit Form Styling */
.swal-edit-form {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.swal-edit-form input,
.swal-edit-form select,
.swal-edit-form textarea {
    transition: all 0.3s ease !important;
}

.swal-edit-form input:focus,
.swal-edit-form select:focus,
.swal-edit-form textarea:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
    outline: none !important;
}

.swal-edit-form label {
    font-size: 14px !important;
    margin-bottom: 5px !important;
}

.swal-edit-form label i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Theme-specific SweetAlert styling */

/* Light Theme */
body[data-theme="light"] .swal-edit-form input,
body[data-theme="light"] .swal-edit-form select,
body[data-theme="light"] .swal-edit-form textarea {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #ddd !important;
}

body[data-theme="light"] .swal-edit-form label {
    color: #333333 !important;
}

/* Solarized Dark Theme */
body[data-theme="solarized-dark"] .swal-edit-form input,
body[data-theme="solarized-dark"] .swal-edit-form select,
body[data-theme="solarized-dark"] .swal-edit-form textarea {
    background-color: #073642 !important;
    color: #839496 !important;
    border-color: #586e75 !important;
}

body[data-theme="solarized-dark"] .swal-edit-form input:focus,
body[data-theme="solarized-dark"] .swal-edit-form select:focus,
body[data-theme="solarized-dark"] .swal-edit-form textarea:focus {
    border-color: #268bd2 !important;
    box-shadow: 0 0 0 3px rgba(38, 139, 210, 0.1) !important;
}

body[data-theme="solarized-dark"] .swal-edit-form label {
    color: #839496 !important;
}

body[data-theme="solarized-dark"] .swal-edit-form option {
    background-color: #073642 !important;
    color: #839496 !important;
}

/* Synth-wave Theme */
body[data-theme="synth-wave"] .swal-edit-form input,
body[data-theme="synth-wave"] .swal-edit-form select,
body[data-theme="synth-wave"] .swal-edit-form textarea {
    background-color: #1a0a1a !important;
    color: #ffffff !important;
    border-color: #ff00ff !important;
    border-width: 2px !important;
}

body[data-theme="synth-wave"] .swal-edit-form input:focus,
body[data-theme="synth-wave"] .swal-edit-form select:focus,
body[data-theme="synth-wave"] .swal-edit-form textarea:focus {
    border-color: #00ffff !important;
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.2) !important;
    background-color: #2a1a2a !important;
}

body[data-theme="synth-wave"] .swal-edit-form label {
    color: #ff00ff !important;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
    font-weight: bold !important;
}

body[data-theme="synth-wave"] .swal-edit-form label i {
    color: #00ffff !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .swal-edit-form option {
    background-color: #1a0a1a !important;
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .swal-edit-form input::placeholder,
body[data-theme="synth-wave"] .swal-edit-form textarea::placeholder {
    color: #888888 !important;
}

/* Custom styling for currency input */
.swal-edit-form input[type="number"] {
    text-align: right !important;
}

/* Error styling */
body[data-theme="synth-wave"] .swal-edit-form [style*="border-color: #e74a3b"] {
    border-color: #ff0066 !important;
    box-shadow: 0 0 10px rgba(255, 0, 102, 0.3) !important;
}

body[data-theme="solarized-dark"] .swal-edit-form [style*="border-color: #e74a3b"] {
    border-color: #dc322f !important;
}

/* SweetAlert Button Styling for Edit Form */
body[data-theme="synth-wave"] .swal-edit-form .swal2-confirm {
    background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
    border: none !important;
    color: #ffffff !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.3) !important;
}

body[data-theme="synth-wave"] .swal-edit-form .swal2-confirm:hover {
    background: linear-gradient(45deg, #00ffff, #ff00ff) !important;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5) !important;
}

body[data-theme="synth-wave"] .swal-edit-form .swal2-cancel {
    background-color: #444444 !important;
    border: 2px solid #ff00ff !important;
    color: #ffffff !important;
}

body[data-theme="synth-wave"] .swal-edit-form .swal2-cancel:hover {
    background-color: #555555 !important;
    border-color: #00ffff !important;
}

body[data-theme="solarized-dark"] .swal-edit-form .swal2-confirm {
    background-color: #268bd2 !important;
    border-color: #268bd2 !important;
}

body[data-theme="solarized-dark"] .swal-edit-form .swal2-confirm:hover {
    background-color: #2176bd !important;
}

body[data-theme="solarized-dark"] .swal-edit-form .swal2-cancel {
    background-color: #586e75 !important;
    border-color: #586e75 !important;
}

body[data-theme="solarized-dark"] .swal-edit-form .swal2-cancel:hover {
    background-color: #657b83 !important;
}
</style>
@endpush

@push('scripts')
<!-- SweetAlert2 CDN -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    console.log('Budget monitor page loaded');
    let budgets = [];
    
    // Load initial data with error handling
    try {
        loadBudgetData();
    } catch (e) {
        console.error('Error in loadBudgetData:', e);
        showErrorState();
    }
    
    // Filter handlers
    $('#periodFilter, #categoryFilter, #statusFilter').on('change', function() {
        try {
            loadBudgetData();
        } catch (e) {
            console.error('Error in filter change:', e);
        }
    });
    
    $('#resetFilter').on('click', function() {
        try {
            $('#periodFilter').val('current');
            $('#categoryFilter').val('');
            $('#statusFilter').val('');
            loadBudgetData();
        } catch (e) {
            console.error('Error in reset filter:', e);
        }
    });

    function loadBudgetData() {
        console.log('Loading budget data...');
        const filters = {
            period: $('#periodFilter').val(),
            category_id: $('#categoryFilter').val(),
            status: $('#statusFilter').val()
        };

        // Show loading state
        showLoadingState();

        $.ajax({
            url: '{{ route("budgets.api.monitor") }}',
            method: 'GET',
            data: filters,
            success: function(response) {
                console.log('Budget data loaded successfully:', response);
                budgets = response.budgets || [];
                updateSummaryCards(response.summary || {});
                updateBudgetTable(budgets);
            },
            error: function(xhr) {
                console.error('Error loading budget data:', xhr);
                console.error('Response text:', xhr.responseText);
                showErrorState();
            }
        });
    }

    function showLoadingState() {
        $('#budgetTableBody').html(`
            <tr>
                <td colspan="9" class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Memuat data anggaran...</p>
                </td>
            </tr>
        `);
    }

    function showErrorState() {
        $('#budgetTableBody').html(`
            <tr>
                <td colspan="9" class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>Terjadi kesalahan saat memuat data</p>
                    <button class="btn btn-sm btn-primary" onclick="loadBudgetData()">
                        <i class="fas fa-refresh"></i> Muat Ulang
                    </button>
                </td>
            </tr>
        `);
    }

    function updateSummaryCards(summary) {
        $('#totalBudget').html(formatCurrency(summary.total_budget));
        $('#totalSpent').html(formatCurrency(summary.total_spent));
        $('#remainingBudget').html(formatCurrency(summary.remaining_budget));
        $('#averageUsage').html(summary.average_usage + '%');
    }

    function updateBudgetTable(budgets) {
        if (budgets.length === 0) {
            $('#budgetTableBody').html(`
                <tr>
                    <td colspan="9" class="text-center text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>Tidak ada data anggaran</p>
                        <a href="{{ route('budgets.create') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Buat Anggaran Pertama
                        </a>
                    </td>
                </tr>
            `);
            return;
        }

        let html = '';
        budgets.forEach(budget => {
            const percentage = budget.spent_amount > 0 ? 
                Math.round((budget.spent_amount / budget.amount) * 100) : 0;
            
            const statusClass = getStatusClass(percentage);
            const statusText = getStatusText(percentage);
            
            html += `
                <tr>
                    <td>
                        <strong>${budget.name}</strong>
                        ${budget.description ? `<br><small class="text-muted">${budget.description}</small>` : ''}
                    </td>
                    <td>${budget.category ? budget.category.name : '-'}</td>
                    <td>
                        <small>
                            ${formatDate(budget.start_date)} - ${formatDate(budget.end_date)}
                            <br><span class="badge badge-info">${budget.type}</span>
                        </small>
                    </td>
                    <td class="text-right font-weight-bold">${formatCurrency(budget.amount)}</td>
                    <td class="text-right">${formatCurrency(budget.spent_amount)}</td>
                    <td class="text-right">${formatCurrency(budget.remaining_amount)}</td>
                    <td style="min-width: 120px;">
                        <div class="progress progress-budget">
                            <div class="progress-bar ${statusClass}" 
                                 style="width: ${Math.min(percentage, 100)}%"></div>
                            <div class="progress-text">${percentage}%</div>
                        </div>
                    </td>
                    <td>
                        <span class="badge badge-status ${statusClass}">${statusText}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-info btn-action" 
                                    onclick="showBudgetDetail(${budget.id})" 
                                    title="Detail">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-warning btn-action" 
                                    onclick="showBudgetEditSwal(${budget.id})" 
                                    title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger btn-action" 
                                    onclick="deleteBudget(${budget.id})" 
                                    title="Hapus">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
        
        $('#budgetTableBody').html(html);
    }

    function getStatusClass(percentage) {
        if (percentage < 75) return 'bg-success';
        if (percentage < 90) return 'bg-warning';
        if (percentage < 100) return 'bg-danger';
        return 'bg-dark';
    }

    function getStatusText(percentage) {
        if (percentage < 75) return 'Aman';
        if (percentage < 90) return 'Peringatan';
        if (percentage < 100) return 'Bahaya';
        return 'Terlampaui';
    }

    function formatCurrency(amount) {
        return 'Rp ' + parseInt(amount).toLocaleString('id-ID');
    }

    function formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
        });
    }

    // Global functions
    window.showBudgetDetail = function(budgetId) {
        console.log('showBudgetDetail called with ID:', budgetId);
        
        const budget = budgets.find(b => b.id === budgetId);
        if (!budget) {
            console.error('Budget not found with ID:', budgetId);
            return;
        }

        console.log('Budget found:', budget);

        // Try modal first
        try {
            const percentage = budget.spent_amount > 0 ? 
                Math.round((budget.spent_amount / budget.amount) * 100) : 0;
            
            const statusClass = getStatusClass(percentage);
            const statusText = getStatusText(percentage);

            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="font-weight-bold">Informasi Anggaran</h6>
                        <table class="table table-sm table-borderless">
                            <tr><td><strong>Nama:</strong></td><td>${budget.name}</td></tr>
                            <tr><td><strong>Kategori:</strong></td><td>${budget.category ? budget.category.name : '-'}</td></tr>
                            <tr><td><strong>Tipe:</strong></td><td>${budget.type}</td></tr>
                            <tr><td><strong>Periode:</strong></td><td>${formatDate(budget.start_date)} - ${formatDate(budget.end_date)}</td></tr>
                            ${budget.description ? `<tr><td><strong>Deskripsi:</strong></td><td>${budget.description}</td></tr>` : ''}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="font-weight-bold">Progress Anggaran</h6>
                        <div class="progress progress-budget mb-3">
                            <div class="progress-bar ${statusClass}" style="width: ${Math.min(percentage, 100)}%"></div>
                            <div class="progress-text">${percentage}%</div>
                        </div>
                        <table class="table table-sm table-borderless">
                            <tr><td><strong>Anggaran:</strong></td><td class="text-right">${formatCurrency(budget.amount)}</td></tr>
                            <tr><td><strong>Terpakai:</strong></td><td class="text-right">${formatCurrency(budget.spent_amount)}</td></tr>
                            <tr><td><strong>Sisa:</strong></td><td class="text-right">${formatCurrency(budget.remaining_amount)}</td></tr>
                            <tr><td><strong>Status:</strong></td><td class="text-right"><span class="badge ${statusClass}">${statusText}</span></td></tr>
                        </table>
                    </div>
                </div>
            `;

            console.log('Setting modal content...');
            $('#budgetDetailContent').html(content);
            
            console.log('Showing modal...');
            
            // Check if jQuery and modal plugin are available
            if (typeof $ === 'undefined' || !$.fn.modal) {
                console.warn('jQuery or Bootstrap modal not available, using SweetAlert fallback');
                showBudgetDetailSwal(budgetId);
                return;
            }

            // Simple jQuery modal show - most reliable approach
            $('#budgetDetailModal').modal({
                backdrop: true,
                keyboard: true,
                focus: true,
                show: true
            });
            
            console.log('Modal show command executed');
            
            // Check if modal actually opened (fallback to SweetAlert if it didn't)
            setTimeout(function() {
                if (!$('#budgetDetailModal').hasClass('show') && !$('#budgetDetailModal').is(':visible')) {
                    console.warn('Modal failed to open, using SweetAlert fallback');
                    showBudgetDetailSwal(budgetId);
                }
            }, 500);
            
        } catch (error) {
            console.error('Error showing modal:', error);
            console.log('Using SweetAlert fallback due to modal error');
            showBudgetDetailSwal(budgetId);
        }
    };

    // Alternative: Show detail using SweetAlert if modal fails
    window.showBudgetDetailSwal = function(budgetId) {
        const budget = budgets.find(b => b.id === budgetId);
        if (!budget) return;

        const percentage = budget.spent_amount > 0 ? 
            Math.round((budget.spent_amount / budget.amount) * 100) : 0;
        
        const statusClass = getStatusClass(percentage);
        const statusText = getStatusText(percentage);

        const progressBarColor = percentage > 100 ? '#e74a3b' : 
                                percentage > 80 ? '#f39c12' : '#1cc88a';

        const content = `
            <div style="text-align: left;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h6 style="font-weight: bold; margin-bottom: 15px; color: #333;">Informasi Anggaran</h6>
                        <table style="width: 100%; font-size: 14px;">
                            <tr><td style="padding: 4px 8px; font-weight: bold;">Nama:</td><td style="padding: 4px 8px;">${budget.name}</td></tr>
                            <tr><td style="padding: 4px 8px; font-weight: bold;">Kategori:</td><td style="padding: 4px 8px;">${budget.category ? budget.category.name : '-'}</td></tr>
                            <tr><td style="padding: 4px 8px; font-weight: bold;">Tipe:</td><td style="padding: 4px 8px;">${budget.type}</td></tr>
                            <tr><td style="padding: 4px 8px; font-weight: bold;">Periode:</td><td style="padding: 4px 8px;">${formatDate(budget.start_date)} - ${formatDate(budget.end_date)}</td></tr>
                            ${budget.description ? `<tr><td style="padding: 4px 8px; font-weight: bold;">Deskripsi:</td><td style="padding: 4px 8px;">${budget.description}</td></tr>` : ''}
                        </table>
                    </div>
                    <div>
                        <h6 style="font-weight: bold; margin-bottom: 15px; color: #333;">Progress Anggaran</h6>
                        <div style="background: #f8f9fa; border-radius: 10px; height: 20px; margin-bottom: 15px; position: relative;">
                            <div style="background: ${progressBarColor}; height: 100%; border-radius: 10px; width: ${Math.min(percentage, 100)}%; position: relative;">
                                <span style="position: absolute; width: 100%; text-align: center; line-height: 20px; color: white; font-size: 12px; font-weight: bold;">${percentage}%</span>
                            </div>
                        </div>
                        <table style="width: 100%; font-size: 14px;">
                            <tr><td style="padding: 4px 8px; font-weight: bold;">Anggaran:</td><td style="padding: 4px 8px; text-align: right;">${formatCurrency(budget.amount)}</td></tr>
                            <tr><td style="padding: 4px 8px; font-weight: bold;">Terpakai:</td><td style="padding: 4px 8px; text-align: right;">${formatCurrency(budget.spent_amount)}</td></tr>
                            <tr><td style="padding: 4px 8px; font-weight: bold;">Sisa:</td><td style="padding: 4px 8px; text-align: right;">${formatCurrency(budget.remaining_amount)}</td></tr>
                            <tr><td style="padding: 4px 8px; font-weight: bold;">Status:</td><td style="padding: 4px 8px; text-align: right;"><span style="background: ${progressBarColor}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">${statusText}</span></td></tr>
                        </table>
                    </div>
                </div>
            </div>
        `;

        Swal.fire({
            title: 'Detail Anggaran',
            html: content,
            width: '800px',
            showCloseButton: true,
            showConfirmButton: true,
            confirmButtonText: '<i class="fas fa-times"></i> Tutup',
            confirmButtonColor: '#6c757d',
            customClass: {
                popup: 'swal-popup-theme',
                title: 'swal-title-theme',
                htmlContainer: 'swal-content-theme',
                confirmButton: 'swal-confirm-theme'
            }
        });
    };

    window.deleteBudget = function(budgetId) {
        const budget = budgets.find(b => b.id === budgetId);
        if (!budget) return;

        Swal.fire({
            title: 'Konfirmasi Hapus',
            html: `Apakah Anda yakin ingin menghapus anggaran <strong>"${budget.name}"</strong>?<br><br><small class="text-muted">Tindakan ini tidak dapat dibatalkan!</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#e74a3b',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> Ya, Hapus!',
            cancelButtonText: '<i class="fas fa-times"></i> Batal',
            reverseButtons: true,
            focusCancel: true,
            allowOutsideClick: false,
            customClass: {
                popup: 'swal-popup-theme',
                title: 'swal-title-theme',
                htmlContainer: 'swal-content-theme',
                confirmButton: 'swal-confirm-theme',
                cancelButton: 'swal-cancel-theme'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: 'Menghapus Anggaran',
                    html: 'Sedang memproses penghapusan anggaran...',
                    icon: 'info',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    customClass: {
                        popup: 'swal-popup-theme'
                    },
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: `{{ route('budgets.index') }}/${budgetId}`,
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            title: 'Berhasil Dihapus!',
                            html: `Anggaran <strong>"${budget.name}"</strong> berhasil dihapus dari sistem.`,
                            icon: 'success',
                            confirmButtonColor: '#1cc88a',
                            confirmButtonText: '<i class="fas fa-check"></i> OK',
                            timer: 3000,
                            timerProgressBar: true,
                            customClass: {
                                popup: 'swal-popup-theme',
                                title: 'swal-title-theme',
                                htmlContainer: 'swal-content-theme',
                                confirmButton: 'swal-confirm-theme'
                            }
                        }).then(() => {
                            loadBudgetData();
                        });
                    },
                    error: function(xhr) {
                        let errorMessage = 'Terjadi kesalahan saat menghapus anggaran';
                        
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.status === 404) {
                            errorMessage = 'Anggaran tidak ditemukan';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Anda tidak memiliki izin untuk menghapus anggaran ini';
                        }

                        Swal.fire({
                            title: 'Gagal Menghapus!',
                            html: `<strong>Error:</strong> ${errorMessage}<br><br><small>Silakan coba lagi atau hubungi administrator.</small>`,
                            icon: 'error',
                            confirmButtonColor: '#e74a3b',
                            confirmButtonText: '<i class="fas fa-times"></i> Tutup',
                            customClass: {
                                popup: 'swal-popup-theme',
                                title: 'swal-title-theme',
                                htmlContainer: 'swal-content-theme',
                                confirmButton: 'swal-confirm-theme'
                            }
                        });
                    }
                });
            }
        });
    };

    // Simple modal close function
    window.closeBudgetDetailModal = function() {
        console.log('closeBudgetDetailModal called');
        $('#budgetDetailModal').modal('hide');
        console.log('Modal hide command executed');
    };

    // Variable to store current editing budget ID
    let currentEditingBudgetId = null;

    // Show budget edit modal
    window.showBudgetEdit = function(budgetId) {
        console.log('showBudgetEdit called with ID:', budgetId);
        currentEditingBudgetId = budgetId;
        
        // Show loading state
        $('#budgetEditContent').html(`
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Memuat data anggaran...</p>
            </div>
        `);
        
        // Show modal immediately
        $('#budgetEditModal').modal({
            backdrop: true,
            keyboard: true,
            focus: true,
            show: true
        });
        
        // Load budget data
        $.ajax({
            url: `{{ route('budgets.index') }}/${budgetId}/edit`,
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    console.log('Budget data loaded:', response.budget);
                    populateEditForm(response.budget, response.categories);
                } else {
                    console.error('Failed to load budget data:', response.message);
                    showErrorInModal('Gagal memuat data anggaran');
                }
            },
            error: function(xhr) {
                console.error('Error loading budget data:', xhr);
                let errorMessage = 'Terjadi kesalahan saat memuat data anggaran';
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 404) {
                    errorMessage = 'Anggaran tidak ditemukan';
                }
                
                showErrorInModal(errorMessage);
            }
        });
    };

    // Populate edit form with budget data
    function populateEditForm(budget, categories) {
        // Restore form HTML
        $('#budgetEditContent').html(`
            <form id="budgetEditForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="editBudgetName"><strong>Nama Anggaran *</strong></label>
                            <input type="text" class="form-control" id="editBudgetName" name="name" required maxlength="255" placeholder="Masukkan nama anggaran">
                            <div class="invalid-feedback" id="editNameError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="editBudgetCategory"><strong>Kategori</strong></label>
                            <select class="form-control" id="editBudgetCategory" name="category_id">
                                <option value="">Pilih Kategori (Opsional)</option>
                            </select>
                            <div class="invalid-feedback" id="editCategoryError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="editBudgetAmount"><strong>Jumlah Anggaran *</strong></label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">Rp</span>
                                </div>
                                <input type="number" class="form-control" id="editBudgetAmount" name="amount" required min="0" step="1000" placeholder="0">
                            </div>
                            <div class="invalid-feedback" id="editAmountError"></div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="editBudgetType"><strong>Tipe Anggaran *</strong></label>
                            <select class="form-control" id="editBudgetType" name="type" required>
                                <option value="">Pilih Tipe</option>
                                <option value="monthly">Bulanan</option>
                                <option value="yearly">Tahunan</option>
                                <option value="custom">Kustom</option>
                            </select>
                            <div class="invalid-feedback" id="editTypeError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="editBudgetStartDate"><strong>Tanggal Mulai *</strong></label>
                            <input type="date" class="form-control" id="editBudgetStartDate" name="start_date" required>
                            <div class="invalid-feedback" id="editStartDateError"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="editBudgetEndDate"><strong>Tanggal Berakhir *</strong></label>
                            <input type="date" class="form-control" id="editBudgetEndDate" name="end_date" required>
                            <div class="invalid-feedback" id="editEndDateError"></div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="editBudgetDescription"><strong>Deskripsi</strong></label>
                    <textarea class="form-control" id="editBudgetDescription" name="description" rows="3" placeholder="Deskripsi anggaran (opsional)"></textarea>
                    <div class="invalid-feedback" id="editDescriptionError"></div>
                </div>
            </form>
        `);
        
        // Populate categories
        const categorySelect = $('#editBudgetCategory');
        categorySelect.html('<option value="">Pilih Kategori (Opsional)</option>');
        categories.forEach(category => {
            const selected = budget.category_id === category.id ? 'selected' : '';
            categorySelect.append(`<option value="${category.id}" ${selected}>${category.name}</option>`);
        });
        
        // Populate form fields
        $('#editBudgetName').val(budget.name);
        $('#editBudgetAmount').val(budget.amount);
        $('#editBudgetType').val(budget.type);
        $('#editBudgetStartDate').val(budget.start_date);
        $('#editBudgetEndDate').val(budget.end_date);
        $('#editBudgetDescription').val(budget.description || '');
        
        // Clear previous validation errors
        clearEditFormErrors();
    }

    // Show error in modal
    function showErrorInModal(message) {
        $('#budgetEditContent').html(`
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Error:</strong> ${message}
            </div>
            <div class="text-center mt-3">
                <button type="button" class="btn btn-secondary" onclick="closeBudgetEditModal()">Tutup</button>
            </div>
        `);
    }

    // Close edit modal
    window.closeBudgetEditModal = function() {
        $('#budgetEditModal').modal('hide');
        currentEditingBudgetId = null;
        clearEditFormErrors();
    };

    // Clear form validation errors
    function clearEditFormErrors() {
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').text('');
    }

    // Form validation helper
    function validateBudgetEditForm() {
        let isValid = true;
        clearEditFormErrors();
        
        // Required field validation
        const requiredFields = [
            { id: 'editBudgetName', name: 'Nama Anggaran' },
            { id: 'editBudgetAmount', name: 'Jumlah Anggaran' },
            { id: 'editBudgetType', name: 'Tipe Anggaran' },
            { id: 'editBudgetStartDate', name: 'Tanggal Mulai' },
            { id: 'editBudgetEndDate', name: 'Tanggal Berakhir' }
        ];
        
        requiredFields.forEach(field => {
            const element = $(`#${field.id}`);
            const value = element.val().trim();
            
            if (!value) {
                element.addClass('is-invalid');
                $(`#${field.id.replace('editBudget', 'edit').replace('Date', '')}Error`).text(`${field.name} wajib diisi`);
                isValid = false;
            }
        });
        
        // Amount validation
        const amount = parseFloat($('#editBudgetAmount').val());
        if (amount && amount < 0) {
            $('#editBudgetAmount').addClass('is-invalid');
            $('#editAmountError').text('Jumlah anggaran tidak boleh negatif');
            isValid = false;
        }
        
        // Date validation
        const startDate = new Date($('#editBudgetStartDate').val());
        const endDate = new Date($('#editBudgetEndDate').val());
        
        if (startDate && endDate && startDate > endDate) {
            $('#editBudgetEndDate').addClass('is-invalid');
            $('#editEndDateError').text('Tanggal berakhir tidak boleh lebih awal dari tanggal mulai');
            isValid = false;
        }
        
        return isValid;
    }

    // Enhanced save budget edit with validation
    window.saveBudgetEdit = function() {
        if (!currentEditingBudgetId) {
            console.error('No budget ID set for editing');
            return;
        }
        
        // Validate form first
        if (!validateBudgetEditForm()) {
            console.log('Form validation failed');
            return;
        }
        
        // Get form data
        const formData = {
            name: $('#editBudgetName').val().trim(),
            category_id: $('#editBudgetCategory').val() || null,
            amount: parseFloat($('#editBudgetAmount').val()),
            type: $('#editBudgetType').val(),
            start_date: $('#editBudgetStartDate').val(),
            end_date: $('#editBudgetEndDate').val(),
            description: $('#editBudgetDescription').val().trim()
        };
        
        console.log('Saving budget edit:', formData);
        
        // Disable save button and show loading
        const saveBtn = $('#saveBudgetBtn');
        const originalText = saveBtn.html();
        saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        
        // Send update request
        $.ajax({
            url: `{{ route('budgets.index') }}/${currentEditingBudgetId}`,
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.success) {
                    // Show success message
                    Swal.fire({
                        title: 'Berhasil!',
                        html: `Anggaran <strong>"${formData.name}"</strong> berhasil diperbarui.`,
                        icon: 'success',
                        confirmButtonColor: '#1cc88a',
                        confirmButtonText: '<i class="fas fa-check"></i> OK',
                        timer: 3000,
                        timerProgressBar: true,
                        customClass: {
                            popup: 'swal-popup-theme',
                            title: 'swal-title-theme',
                            htmlContainer: 'swal-content-theme',
                            confirmButton: 'swal-confirm-theme'
                        }
                    }).then(() => {
                        closeBudgetEditModal();
                        loadBudgetData(); // Refresh data
                    });
                } else {
                    console.error('Update failed:', response.message);
                    showUpdateError(response.message);
                }
            },
            error: function(xhr) {
                console.error('Error updating budget:', xhr);
                
                if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    // Validation errors
                    const errors = xhr.responseJSON.errors;
                    displayValidationErrors(errors);
                } else {
                    let errorMessage = 'Terjadi kesalahan saat memperbarui anggaran';
                    
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    
                    showUpdateError(errorMessage);
                }
            },
            complete: function() {
                // Re-enable save button
                saveBtn.prop('disabled', false).html(originalText);
            }
        });
    };

    // Display validation errors
    function displayValidationErrors(errors) {
        Object.keys(errors).forEach(field => {
            const inputField = $(`#editBudget${field.charAt(0).toUpperCase() + field.slice(1).replace('_', '')}`);
            const errorElement = $(`#edit${field.charAt(0).toUpperCase() + field.slice(1).replace('_', '')}Error`);
            
            if (inputField.length && errorElement.length) {
                inputField.addClass('is-invalid');
                errorElement.text(errors[field][0]);
            }
        });
    }

    // Show update error
    function showUpdateError(message) {
        Swal.fire({
            title: 'Gagal Memperbarui!',
            html: `<strong>Error:</strong> ${message}<br><br><small>Silakan periksa data dan coba lagi.</small>`,
            icon: 'error',
            confirmButtonColor: '#e74a3b',
            confirmButtonText: '<i class="fas fa-times"></i> Tutup',
            customClass: {
                popup: 'swal-popup-theme',
                title: 'swal-title-theme',
                htmlContainer: 'swal-content-theme',
                confirmButton: 'swal-confirm-theme'
            }
        });
    }

    // Show budget edit form using SweetAlert
    window.showBudgetEditSwal = function(budgetId) {
        console.log('showBudgetEditSwal called with ID:', budgetId);
        currentEditingBudgetId = budgetId;
        
        // Show loading SweetAlert first
        Swal.fire({
            title: 'Memuat Data Anggaran',
            html: 'Sedang mengambil data anggaran...',
            icon: 'info',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            customClass: {
                popup: 'swal-popup-theme'
            },
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        // Load budget data
        $.ajax({
            url: `{{ route('budgets.index') }}/${budgetId}/edit`,
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Edit budget API response:', response);
                if (response.success) {
                    console.log('Budget data loaded:', response.budget);
                    showEditFormSwal(response.budget, response.categories);
                } else {
                    console.error('Failed to load budget data:', response.message || 'Unknown error');
                    showErrorSwal(response.message || 'Gagal memuat data anggaran');
                }
            },
            error: function(xhr) {
                console.error('Error loading budget data:', xhr);
                let errorMessage = 'Terjadi kesalahan saat memuat data anggaran';
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 404) {
                    errorMessage = 'Anggaran tidak ditemukan';
                }
                
                showErrorSwal(errorMessage);
            }
        });
    };

    // Show edit form in SweetAlert
    function showEditFormSwal(budget, categories) {
        // Build categories options
        let categoryOptions = '<option value="">Pilih Kategori (Opsional)</option>';
        categories.forEach(category => {
            const selected = budget.category_id === category.id ? 'selected' : '';
            categoryOptions += `<option value="${category.id}" ${selected}>${category.name}</option>`;
        });

        const formHtml = `
            <div style="text-align: left; max-height: 70vh; overflow-y: auto;">
                <form id="swalBudgetEditForm">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">
                                    <i class="fas fa-tag"></i> Nama Anggaran *
                                </label>
                                <input type="text" id="swalBudgetName" name="name" 
                                       value="${budget.name}" required maxlength="255"
                                       style="width: 100%; padding: 8px 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 14px;"
                                       placeholder="Masukkan nama anggaran">
                                <div id="swalNameError" style="color: #e74a3b; font-size: 12px; margin-top: 5px; display: none;"></div>
                            </div>
                            
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">
                                                                       <i class="fas fa-list"></i> Kategori
                                </label>
                                <select id="swalBudgetCategory" name="category_id"
                                        style="width: 100%; padding: 8px 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 14px;">
                                    ${categoryOptions}
                                </select>
                                <div id="swalCategoryError" style="color: #e74a3b; font-size: 12px; margin-top: 5px; display: none;"></div>
                            </div>
                            
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">
                                    <i class="fas fa-money-bill"></i> Jumlah Anggaran *
                                </label>
                                <div style="position: relative;">
                                    <span style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #666; font-weight: bold; z-index: 1;">Rp</span>
                                    <input type="number" id="swalBudgetAmount" name="amount" 
                                           value="${budget.amount}" required min="0" step="1000"
                                           style="width: 100%; padding: 8px 12px 8px 35px; border: 2px solid #ddd; border-radius: 8px; font-size: 14px;"
                                           placeholder="0">
                                </div>
                                <div id="swalAmountError" style="color: #e74a3b; font-size: 12px; margin-top: 5px; display: none;"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">
                                    <i class="fas fa-calendar-alt"></i> Tipe Anggaran *
                                </label>
                                <select id="swalBudgetType" name="type" required
                                        style="width: 100%; padding: 8px 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 14px;">
                                    <option value="">Pilih Tipe</option>
                                    <option value="monthly" ${budget.type === 'monthly' ? 'selected' : ''}>Bulanan</option>
                                    <option value="yearly" ${budget.type === 'yearly' ? 'selected' : ''}>Tahunan</option>
                                    <option value="custom" ${budget.type === 'custom' ? 'selected' : ''}>Kustom</option>
                                </select>
                                <div id="swalTypeError" style="color: #e74a3b; font-size: 12px; margin-top: 5px; display: none;"></div>
                            </div>
                            
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">
                                    <i class="fas fa-calendar-day"></i> Tanggal Mulai *
                                </label>
                                <input type="date" id="swalBudgetStartDate" name="start_date" 
                                       value="${budget.start_date}" required
                                       style="width: 100%; padding: 8px 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 14px;">
                                <div id="swalStartDateError" style="color: #e74a3b; font-size: 12px; margin-top: 5px; display: none;"></div>
                            </div>
                            
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">
                                    <i class="fas fa-calendar-check"></i> Tanggal Berakhir *
                                </label>
                                <input type="date" id="swalBudgetEndDate" name="end_date" 
                                       value="${budget.end_date}" required
                                       style="width: 100%; padding: 8px 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 14px;">
                                <div id="swalEndDateError" style="color: #e74a3b; font-size: 12px; margin-top: 5px; display: none;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">
                            <i class="fas fa-sticky-note"></i> Deskripsi
                        </label>
                        <textarea id="swalBudgetDescription" name="description" rows="3"
                                  style="width: 100%; padding: 8px 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 14px; resize: vertical;"
                                  placeholder="Deskripsi anggaran (opsional)">${budget.description || ''}</textarea>
                        <div id="swalDescriptionError" style="color: #e74a3b; font-size: 12px; margin-top: 5px; display: none;"></div>
                    </div>
                </form>
            </div>
        `;

        Swal.fire({
            title: '<i class="fas fa-edit"></i> Edit Anggaran',
            html: formHtml,
            width: '900px',
            showCancelButton: true,
            confirmButtonText: '<i class="fas fa-save"></i> Simpan Perubahan',
            cancelButtonText: '<i class="fas fa-times"></i> Batal',
            confirmButtonColor: '#1cc88a',
            cancelButtonColor: '#6c757d',
            reverseButtons: true,
            focusCancel: false,
            allowOutsideClick: false,
            customClass: {
                popup: 'swal-popup-theme swal-edit-form',
                title: 'swal-title-theme',
                htmlContainer: 'swal-content-theme',
                confirmButton: 'swal-confirm-theme',
                cancelButton: 'swal-cancel-theme'
            },
            preConfirm: () => {
                return validateAndGetSwalFormData();
            }
        }).then((result) => {
            if (result.isConfirmed && result.value) {
                saveBudgetEditSwal(result.value);
            } else {
                currentEditingBudgetId = null;
            }
        });
    }

    // Validate SweetAlert form and return data
    function validateAndGetSwalFormData() {
        clearSwalFormErrors();
        let isValid = true;
        
        // Get form data
        const formData = {
            name: $('#swalBudgetName').val().trim(),
            category_id: $('#swalBudgetCategory').val() || null,
            amount: parseFloat($('#swalBudgetAmount').val()),
            type: $('#swalBudgetType').val(),
            start_date: $('#swalBudgetStartDate').val(),
            end_date: $('#swalBudgetEndDate').val(),
            description: $('#swalBudgetDescription').val().trim()
        };
        
        // Required field validation
        if (!formData.name) {
            showSwalFieldError('swalNameError', 'Nama anggaran wajib diisi');
            $('#swalBudgetName').css('border-color', '#e74a3b');
            isValid = false;
        }
        
        if (!formData.amount || formData.amount <= 0) {
            showSwalFieldError('swalAmountError', 'Jumlah anggaran wajib diisi dan harus lebih dari 0');
            $('#swalBudgetAmount').css('border-color', '#e74a3b');
            isValid = false;
        }
        
        if (!formData.type) {
            showSwalFieldError('swalTypeError', 'Tipe anggaran wajib dipilih');
            $('#swalBudgetType').css('border-color', '#e74a3b');
            isValid = false;
        }
        
        if (!formData.start_date) {
            showSwalFieldError('swalStartDateError', 'Tanggal mulai wajib diisi');
            $('#swalBudgetStartDate').css('border-color', '#e74a3b');
            isValid = false;
        }
        
        if (!formData.end_date) {
            showSwalFieldError('swalEndDateError', 'Tanggal berakhir wajib diisi');
            $('#swalBudgetEndDate').css('border-color', '#e74a3b');
            isValid = false;
        }
        
        // Date validation
        if (formData.start_date && formData.end_date) {
            const startDate = new Date(formData.start_date);
            const endDate = new Date(formData.end_date);
            
            if (startDate > endDate) {
                showSwalFieldError('swalEndDateError', 'Tanggal berakhir tidak boleh lebih awal dari tanggal mulai');
                $('#swalBudgetEndDate').css('border-color', '#e74a3b');
                isValid = false;
            }
        }
        
        if (!isValid) {
            Swal.showValidationMessage('Silakan periksa dan lengkapi semua field yang diperlukan');
            return false;
        }
        
        return formData;
    }

    // Clear SweetAlert form errors
    function clearSwalFormErrors() {
        ['Name', 'Category', 'Amount', 'Type', 'StartDate', 'EndDate', 'Description'].forEach(field => {
            $(`#swal${field}Error`).hide();
            $(`#swalBudget${field}`).css('border-color', '#ddd');
        });
    }

    // Show field error in SweetAlert
    function showSwalFieldError(errorId, message) {
        $(`#${errorId}`).text(message).show();
    }

    // Save budget edit from SweetAlert
    function saveBudgetEditSwal(formData) {
        console.log('Saving budget edit from SweetAlert:', formData);
        
        // Show loading
        Swal.fire({
            title: 'Menyimpan Perubahan',
            html: 'Sedang memperbarui data anggaran...',
            icon: 'info',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            customClass: {
                popup: 'swal-popup-theme'
            },
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        // Send update request
        $.ajax({
            url: `{{ route('budgets.index') }}/${currentEditingBudgetId}`,
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.success) {
                    // Show success message
                    Swal.fire({
                        title: 'Berhasil Diperbarui!',
                        html: `Anggaran <strong>"${formData.name}"</strong> telah berhasil diperbarui.<br><br><small class="text-muted">Data akan dimuat ulang secara otomatis.</small>`,
                        icon: 'success',
                        confirmButtonColor: '#1cc88a',
                        confirmButtonText: '<i class="fas fa-check"></i> OK',
                        timer: 4000,
                        timerProgressBar: true,
                        customClass: {
                            popup: 'swal-popup-theme',
                            title: 'swal-title-theme',
                            htmlContainer: 'swal-content-theme',
                            confirmButton: 'swal-confirm-theme'
                        }
                    }).then(() => {
                        currentEditingBudgetId = null;
                        loadBudgetData(); // Refresh data
                    });
                } else {
                    console.error('Update failed:', response.message);
                    showErrorSwal(response.message || 'Gagal memperbarui anggaran');
                }
            },
            error: function(xhr) {
                console.error('Error updating budget:', xhr);
                
                let errorMessage = 'Terjadi kesalahan saat memperbarui anggaran';
                let errorDetails = '';
                
                if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    // Validation errors
                    const errors = xhr.responseJSON.errors;
                    errorDetails = '<ul style="text-align: left; margin: 10px 0;">';
                    Object.keys(errors).forEach(field => {
                        errorDetails += `<li><strong>${field}:</strong> ${errors[field][0]}</li>`;
                    });
                    errorDetails += '</ul>';
                    errorMessage = 'Data tidak valid';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 404) {
                    errorMessage = 'Anggaran tidak ditemukan';
                } else if (xhr.status === 403) {
                    errorMessage = 'Anda tidak memiliki izin untuk memperbarui anggaran ini';
                }
                
                Swal.fire({
                    title: 'Gagal Memperbarui!',
                    html: `<strong>Error:</strong> ${errorMessage}${errorDetails}<br><br><small>Silakan periksa data dan coba lagi.</small>`,
                    icon: 'error',
                    confirmButtonColor: '#e74a3b',
                    confirmButtonText: '<i class="fas fa-times"></i> Tutup',
                    customClass: {
                        popup: 'swal-popup-theme',
                        title: 'swal-title-theme',
                        htmlContainer: 'swal-content-theme',
                        confirmButton: 'swal-confirm-theme'
                    }
                });
                
                currentEditingBudgetId = null;
            }
        });
    }

    // Show error using SweetAlert
    function showErrorSwal(message) {
        Swal.fire({
            title: 'Terjadi Kesalahan!',
            html: `<strong>Error:</strong> ${message}<br><br><small>Silakan coba lagi atau hubungi administrator.</small>`,
            icon: 'error',
            confirmButtonColor: '#e74a3b',
            confirmButtonText: '<i class="fas fa-times"></i> Tutup',
            customClass: {
                popup: 'swal-popup-theme',
                title: 'swal-title-theme',
                htmlContainer: 'swal-content-theme',
                confirmButton: 'swal-confirm-theme'
            }
        });
        currentEditingBudgetId = null;
    }

    // Initialize modal event handlers when document is ready
    $(document).ready(function() {
        console.log('Document ready - initializing modal events');
        
        // Detail Modal Events
        $('#budgetDetailModal').on('hidden.bs.modal', function () {
            console.log('Detail modal hidden event triggered');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        
        $('#budgetDetailModal').on('shown.bs.modal', function () {
            console.log('Detail modal shown event triggered');
        });
        
        $('#budgetDetailModal').on('show.bs.modal', function () {
            console.log('Detail modal show event triggered');
            $('.modal-backdrop').remove();
        });
        
        // Edit Modal Events
        $('#budgetEditModal').on('hidden.bs.modal', function () {
            console.log('Edit modal hidden event triggered');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            currentEditingBudgetId = null;
            clearEditFormErrors();
        });
        
        $('#budgetEditModal').on('shown.bs.modal', function () {
            console.log('Edit modal shown event triggered');
        });
        
        $('#budgetEditModal').on('show.bs.modal', function () {
            console.log('Edit modal show event triggered');
            $('.modal-backdrop').remove();
        });
        
        // Handle escape key for both modals
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) {
                if ($('#budgetDetailModal').hasClass('show')) {
                    console.log('Escape key pressed - closing detail modal');
                    closeBudgetDetailModal();
                } else if ($('#budgetEditModal').hasClass('show')) {
                    console.log('Escape key pressed - closing edit modal');
                    closeBudgetEditModal();
                }
            }
        });
        
        // Debug: Log when modal events are bound
        console.log('Modal event handlers bound successfully');
    });

    // Auto refresh every 30 seconds - DISABLED
    // setInterval(loadBudgetData, 30000);
});
</script>
@endpush
@endsection
