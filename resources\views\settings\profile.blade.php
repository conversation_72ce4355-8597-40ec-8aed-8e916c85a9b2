@extends('layouts.dashboard')

@section('title', 'Profil & Akun')

@section('page-title', '👤 Profil & Akun - Informasi Lengkap')

@push('styles')
<style>
/* ===== PROFILE PAGE THEME STYLES ===== */
<script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
      <style>        /* Ensure FontAwesome icons are loaded properly */
        .fas, .far, .fab {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro" !important;
            font-weight: 900;
        }
        
        .far {
            font-weight: 400;
        }
        
        /* Force icon visibility with fallback */
        .menu-icon.fas::before,
        .submenu-icon.fas::before,
        .menu-arrow.fas::before {
            display: inline-block !important;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
        }
        
        /* Fallback untuk icon yang tidak ter-load */
        .menu-icon {
            position: relative;
        }
        
        .menu-icon.fa-home::before { content: "🏠"; }
        .menu-icon.fa-exchange-alt::before { content: "💱"; }
        .menu-icon.fa-tags::before { content: "🏷️"; }
        .menu-icon.fa-wallet::before { content: "👛"; }
        .menu-icon.fa-clipboard-list::before { content: "📋"; }
        .menu-icon.fa-chart-bar::before { content: "📊"; }
        .menu-icon.fa-cog::before { content: "⚙️"; }
        
        :root {
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --primary-color: #667eea;
            --secondary-color: #764ba2;
        }body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Figtree', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .app-container {
            display: flex;
            min-height: 100vh;
        }
          /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
            overflow-x: hidden;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
          .sidebar-subtitle {
            font-size: 0.8rem;
            color: #666;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .sidebar-menu {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        
        /* Styling khusus untuk sidebar collapsed */
        .sidebar.collapsed .sidebar-menu {
            padding: 10px 0;
        }
        
        .sidebar.collapsed .menu-item {
            margin: 6px 0;
        }
        
        .sidebar.collapsed .menu-item:first-child {
            margin-top: 20px;
        }
        
        .menu-item {
            position: relative;
        }
          .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border-radius: 8px;
            margin: 2px 16px;
        }
          .menu-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            text-decoration: none;
            transform: translateX(2px);
        }
        
        .menu-link.active {
            background: var(--primary-color);
            color: white;
        }        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
            font-size: 1rem;
            color: inherit;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: margin-right 0.3s ease, font-size 0.3s ease;
        }
          .menu-text {
            flex: 1;
            font-weight: 500;
            transition: opacity 0.3s ease, visibility 0.3s ease, width 0.3s ease;
        }
        
        .menu-arrow {
            font-size: 0.8rem;
            transition: transform 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .menu-arrow.rotated {
            transform: rotate(90deg);
        }
        
        .submenu {
            list-style: none;
            margin: 0;
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.05);
        }
        
        .submenu.open {
            max-height: 300px;
        }
        
        .submenu-item {
            padding: 8px 20px 8px 52px;
        }
        
        .submenu-link {
            display: flex;
            align-items: center;
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .submenu-link:hover {
            color: var(--primary-color);
            text-decoration: none;
        }
          .submenu-icon {
            width: 16px;
            margin-right: 8px;
            text-align: center;
            font-size: 0.9rem;
            color: inherit;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
          /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            flex: 1;
            min-height: 100vh;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .topbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            height: var(--topbar-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .topbar-left h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }
        
        .topbar-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .user-details h4 {
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .user-details p {
            margin: 0;
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .dashboard-container {
            padding: 30px;
            min-height: calc(100vh - var(--topbar-height));
        }
          
        .dashboard-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            color: white;
        }
        
        .welcome-section h2 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .welcome-section p {
            opacity: 0.9;
            margin: 0;
        }
        
        .btn-logout {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .dashboard-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .card-subtitle {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .card-content {
            color: #555;
            line-height: 1.6;
        }
        
        .finance-icon {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .security-icon {
            background: linear-gradient(45deg, #11998e, #38ef7d);
            color: white;
        }
        
        .stats-icon {
            background: linear-gradient(45deg, #fa709a, #fee140);
            color: white;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0 0 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .quick-actions h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
            display: block;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }        /* Theme Switcher in Topbar */
        .theme-switcher {
            position: relative;
        }
        
        .theme-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
            z-index: 9999; /* Fix z-index issue */
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
        
        /* User Info Dropdown */
        .user-info-dropdown {
            position: relative;
        }
        
        .user-info-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.8rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .user-info-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
            z-index: 9999;
        }
        
        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .user-dropdown-item:hover {
            background: #f0f0f0;
        }
        
        .user-dropdown-icon {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }          /* Sidebar Collapse Button */
        .sidebar-collapse-btn {
            position: fixed;
            top: 20px;
            left: calc(var(--sidebar-width) - 15px);
            width: 30px;
            height: 30px;
            background: var(--primary-color);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
        }
        
        .sidebar-collapse-btn:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        /* Tombol saat sidebar collapsed - posisi berubah ke kanan */
        .sidebar.collapsed + .sidebar-collapse-btn {
            left: calc(80px - 15px);
        }.sidebar.collapsed {
            width: 80px;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
          .sidebar.collapsed .sidebar-header {
            padding: 12px 3px;
            text-align: center;
        }
        
        .sidebar.collapsed .sidebar-logo {
            font-size: 1.4rem;
            margin-bottom: 0;
        }
        
        .sidebar.collapsed .sidebar-subtitle {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        
        .sidebar.collapsed .menu-text {
            opacity: 0;
            visibility: hidden;
            width: 0;
            transition: opacity 0.2s ease, visibility 0.2s ease, width 0.2s ease;
        }
        
        .sidebar.collapsed .menu-arrow {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        
        .sidebar.collapsed .submenu {
            display: none;
        }        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 8px 4px;
            margin: 4px 6px;
            border-radius: 8px;
            position: relative;
            min-height: 36px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }          .sidebar.collapsed .menu-link:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.12);
            border-color: rgba(102, 126, 234, 0.15);
        }
          .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            border-color: transparent;        }.sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 1.1rem;
            color: inherit;
            width: auto;
            min-width: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
          /* Tooltip for collapsed sidebar */
        .sidebar.collapsed .menu-link::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 15px;
            z-index: 10000;
            pointer-events: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .sidebar.collapsed .menu-link:hover::after {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) translateX(5px);
        }        .main-content.sidebar-collapsed {
            margin-left: 70px;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
          /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-collapse-btn {
                left: 15px;
                top: 15px;
            }
            
            .sidebar.collapsed + .sidebar-collapse-btn {
                left: 15px;
            }
            
            .topbar {
                padding: 0 15px;
            }
            
            .dashboard-container {
                padding: 15px;
            }
            
            .dashboard-content {
                grid-template-columns: 1fr;
            }
              .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .user-info-toggle .user-details h4 {
                display: none;
            }
            
            .user-info-toggle .user-details p {
                display: none;
            }
            
            .user-dropdown {
                min-width: 150px;
            }
        }
        
        .theme-icon {
            font-size: 1rem;
        }
        
        .dropdown-arrow {
            font-size: 0.7rem;
            transition: transform 0.3s ease;
        }
        
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 5px;
        }
        
        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            color: #333;
            transition: background 0.2s ease;
        }
        
        .theme-option:hover {
            background: #f0f0f0;
        }
        
        .theme-option-icon {
            font-size: 1rem;
        }
          /* Responsive */
        @media (max-width: 768px) {
            .welcome-section h2 {
                font-size: 1.3rem;
            }
        }
          /* Theme Variations for Sidebar */
        /* Solarized Dark Theme */
        body[data-theme="solarized-dark"] {
            background: linear-gradient(135deg, #002b36 0%, #073642 100%);
        }
        
        body[data-theme="solarized-dark"] .sidebar {
            background: rgba(253, 246, 227, 0.95);
        }
        
        body[data-theme="solarized-dark"] .sidebar-logo {
            color: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .menu-link {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .menu-link:hover {
            background: rgba(38, 139, 210, 0.1);
            color: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .menu-link.active {
            background: #268bd2;
            color: white;
        }
        
        body[data-theme="solarized-dark"] .sidebar-collapse-btn {
            background: #268bd2;
        }
        
        body[data-theme="solarized-dark"] .sidebar-collapse-btn:hover {
            background: #2aa198;
        }
        
        body[data-theme="solarized-dark"] .dashboard-card {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .quick-actions {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .card-title {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .card-subtitle {
            color: #657b83;
        }
        
        body[data-theme="solarized-dark"] .card-content {
            color: #839496;
        }
        
        body[data-theme="solarized-dark"] .action-btn {
            background: linear-gradient(45deg, #268bd2, #2aa198);
        }
        
        /* Synth Wave Theme */
        body[data-theme="synth-wave"] {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a0033 50%, #000000 100%);
        }
        
        body[data-theme="synth-wave"] .sidebar {
            background: rgba(26, 26, 26, 0.95);
            border-right: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .sidebar-logo {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .sidebar-subtitle {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .menu-link {
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .menu-link:hover {
            background: rgba(255, 0, 255, 0.1);
            color: #ff00ff;
        }
        
        body[data-theme="synth-wave"] .menu-link.active {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            color: white;
        }
        
        body[data-theme="synth-wave"] .sidebar-collapse-btn {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
        }
        
        body[data-theme="synth-wave"] .dashboard-card {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .quick-actions {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .card-title {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        body[data-theme="synth-wave"] .card-subtitle {
            color: #00ffff;
        }
        
        body[data-theme="synth-wave"] .card-content {
            color: #ffffff;
        }
        
        body[data-theme="synth-wave"] .action-btn {
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.4);
        }
          body[data-theme="synth-wave"] .quick-actions h3 {
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
        
        /* Theme-specific styles for user dropdown */
        body[data-theme="solarized-dark"] .user-dropdown {
            background: #fdf6e3;
        }
        
        body[data-theme="solarized-dark"] .user-dropdown-item {
            color: #586e75;
        }
        
        body[data-theme="solarized-dark"] .user-dropdown-item:hover {
            background: rgba(38, 139, 210, 0.1);
        }
        
        body[data-theme="synth-wave"] .user-dropdown {
            background: #1a1a1a;
            border: 1px solid #ff00ff;
        }
        
        body[data-theme="synth-wave"] .user-dropdown-item {
            color: #ffffff;
        }
          body[data-theme="synth-wave"] .user-dropdown-item:hover {
            background: rgba(255, 0, 255, 0.1);
        }
          /* Tooltip styles for different themes */
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link::after {
            background: linear-gradient(135deg, #268bd2, #2aa198);
            color: #fdf6e3;
            box-shadow: 0 4px 12px rgba(38, 139, 210, 0.3);
        }
        
        body[data-theme="synth-wave"] .sidebar.collapsed .menu-link::after {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            color: #ffffff;
            border: 1px solid rgba(255, 0, 255, 0.5);
            box-shadow: 0 4px 12px rgba(255, 0, 255, 0.4);
        }
          /* Theme-specific styling for collapsed sidebar */
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link {
            background: rgba(253, 246, 227, 0.9);
            border-color: rgba(38, 139, 210, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }          body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link:hover {
            background: rgba(38, 139, 210, 0.1);
            border-color: rgba(38, 139, 210, 0.25);
            box-shadow: 0 3px 10px rgba(38, 139, 210, 0.12);
        }
        
        body[data-theme="solarized-dark"] .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, #268bd2, #2aa198);
        }
          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(255, 0, 255, 0.2);
            box-shadow: 0 2px 8px rgba(255, 0, 255, 0.08);
        }          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link:hover {
            background: rgba(255, 0, 255, 0.1);
            border-color: rgba(255, 0, 255, 0.3);
            box-shadow: 0 3px 10px rgba(255, 0, 255, 0.15);
        }
          body[data-theme="synth-wave"] .sidebar.collapsed .menu-link.active {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            box-shadow: 0 4px 15px rgba(255, 0, 255, 0.4);
        }
    </style>
@endpush

@section('content')
<div style="max-width: 1400px; margin: 0 auto;">
    
    @if(session('success'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i> {{ session('error') }}
        </div>
    @endif

    <!-- Header Profile Section -->
    <div class="profile-header-section">
        <div class="profile-header-bg-decoration">
            <div class="decoration-circle-1"></div>
            <div class="decoration-circle-2"></div>
        </div>
        
        <div class="profile-header-content">
            <div class="profile-avatar">
                @if(Auth::user()->avatar)
                    <img src="{{ asset('storage/' . Auth::user()->avatar) }}" alt="Avatar">
                @else
                    {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                @endif
            </div>
            
            <div class="profile-header-info">
                <h1 class="profile-main-title">{{ Auth::user()->name }}</h1>
                <p class="profile-subtitle">
                    <i class="fas fa-at"></i> {{ Auth::user()->username }}
                </p>
                <p class="profile-subtitle">
                    <i class="fas fa-envelope"></i> {{ Auth::user()->email }}
                </p>
                
                <div class="profile-badges">
                    <div class="profile-badge">
                        <i class="fas fa-calendar"></i> 
                        Bergabung {{ Auth::user()->created_at ? Auth::user()->created_at->diffForHumans() : 'Tidak diketahui' }}
                    </div>
                    <div class="profile-badge">
                        <i class="fas fa-clock"></i> 
                        Login terakhir {{ Auth::user()->last_login_at ? Auth::user()->last_login_at->diffForHumans() : 'Tidak pernah' }}
                    </div>
                    <div class="profile-badge status-badge {{ Auth::user()->is_active ? 'active' : 'inactive' }}">
                        <i class="fas fa-{{ Auth::user()->is_active ? 'check-circle' : 'times-circle' }}"></i> 
                        {{ Auth::user()->is_active ? 'Aktif' : 'Nonaktif' }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="profile-content-grid">
        
        <!-- Left Column - Main Information -->
        <div class="profile-left-column">
            
            <!-- Basic Information Card -->
            <div class="profile-card">
                <h3 class="profile-card-title">
                    <i class="fas fa-user profile-card-icon"></i>
                    Informasi Dasar
                </h3>
                
                <div class="profile-fields-grid">
                    <div class="profile-field">
                        <label class="profile-field-label">ID User</label>
                        <p class="profile-field-value">#{{ Auth::user()->id }}</p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Nama Lengkap</label>
                        <p class="profile-field-value">{{ Auth::user()->name }}</p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Username</label>
                        <p class="profile-field-value">{{ Auth::user()->username }}</p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Email</label>
                        <p class="profile-field-value">
                            {{ Auth::user()->email }}
                            @if(Auth::user()->email_verified_at)
                                <span class="verification-badge verified">
                                    <i class="fas fa-check-circle"></i> Terverifikasi
                                </span>
                            @else
                                <span class="verification-badge unverified">
                                    <i class="fas fa-exclamation-triangle"></i> Belum Terverifikasi
                                </span>
                            @endif
                        </p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Nomor Telepon</label>
                        <p class="profile-field-value">{{ Auth::user()->phone ?: 'Belum diisi' }}</p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Jenis Kelamin</label>
                        <p class="profile-field-value">
                            {{ Auth::user()->gender === 'L' ? 'Laki-laki' : (Auth::user()->gender === 'P' ? 'Perempuan' : 'Belum diisi') }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Personal Details Card -->
            <div class="profile-card">
                <h3 class="profile-card-title">
                    <i class="fas fa-id-card profile-card-icon"></i>
                    Detail Pribadi
                </h3>
                
                <div class="profile-fields-grid">
                    <div class="profile-field">
                        <label class="profile-field-label">Tanggal Lahir</label>
                        <p class="profile-field-value">
                            {{ Auth::user()->birth_date ? Auth::user()->birth_date->format('d F Y') : 'Belum diisi' }}
                        </p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Kota</label>
                        <p class="profile-field-value">{{ Auth::user()->city ?: 'Belum diisi' }}</p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Kode Pos</label>
                        <p class="profile-field-value">{{ Auth::user()->postal_code ?: 'Belum diisi' }}</p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Negara</label>
                        <p class="profile-field-value">{{ Auth::user()->country ?: 'Indonesia' }}</p>
                    </div>
                </div>
                
                <div class="profile-field full-width">
                    <label class="profile-field-label">Alamat</label>
                    <p class="profile-field-value profile-field-textarea">
                        {{ Auth::user()->address ?: 'Belum diisi' }}
                    </p>
                </div>
                
                <div class="profile-field full-width">
                    <label class="profile-field-label">Bio</label>
                    <p class="profile-field-value profile-field-textarea">
                        {{ Auth::user()->bio ?: 'Belum ada bio' }}
                    </p>
                </div>
            </div>

            <!-- Preferences Card -->
            <div class="profile-card">
                <h3 class="profile-card-title">
                    <i class="fas fa-cog profile-card-icon"></i>
                    Preferensi & Pengaturan
                </h3>
                
                <div class="profile-fields-grid">
                    <div class="profile-field">
                        <label class="profile-field-label">Zona Waktu</label>
                        <p class="profile-field-value">{{ Auth::user()->timezone ?: 'Asia/Jakarta' }}</p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Bahasa</label>
                        <p class="profile-field-value">
                            {{ Auth::user()->language === 'id' ? 'Bahasa Indonesia' : (Auth::user()->language === 'en' ? 'English' : 'Bahasa Indonesia') }}
                        </p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Tema</label>
                        <p class="profile-field-value theme-display" id="profileThemeDisplay">
                            @switch(Auth::user()->theme)
                                @case('light') 
                                    <i class="fas fa-sun"></i> Terang 
                                    @break
                                @case('solarized-dark') 
                                    <i class="fas fa-moon"></i> Solarized Dark 
                                    @break
                                @case('synth-wave') 
                                    <i class="fas fa-city"></i> Synth Wave 
                                    @break
                                @default 
                                    <i class="fas fa-sun"></i> Terang
                            @endswitch
                        </p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Mata Uang</label>
                        <p class="profile-field-value">{{ Auth::user()->currency ?: 'IDR' }}</p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Format Tanggal</label>
                        <p class="profile-field-value">{{ Auth::user()->date_format ?: 'd/m/Y' }}</p>
                    </div>
                    <div class="profile-field">
                        <label class="profile-field-label">Format Waktu</label>
                        <p class="profile-field-value">
                            {{ Auth::user()->time_format === '24' ? '24 Jam' : '12 Jam' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Security & Activity -->
        <div class="profile-right-column">
            
            <!-- Account Status Card -->
            <div class="profile-card">
                <h3 class="profile-card-title">
                    <i class="fas fa-shield-alt profile-card-icon"></i>
                    Status Akun
                </h3>
                
                <div class="profile-status-list">
                    <div class="profile-status-item">
                        <span class="status-label">Status Akun</span>
                        <span class="status-value {{ Auth::user()->is_active ? 'active' : 'inactive' }}">
                            {{ Auth::user()->is_active ? 'Aktif' : 'Nonaktif' }}
                        </span>
                    </div>
                    
                    <div class="profile-status-item">
                        <span class="status-label">Email Terverifikasi</span>
                        <span class="status-value {{ Auth::user()->email_verified_at ? 'verified' : 'unverified' }}">
                            {{ Auth::user()->email_verified_at ? 'Terverifikasi' : 'Belum Terverifikasi' }}
                        </span>
                    </div>
                    
                    <div class="profile-status-item">
                        <span class="status-label">2FA</span>
                        <span class="status-value {{ Auth::user()->two_factor_enabled ? 'enabled' : 'disabled' }}">
                            {{ Auth::user()->two_factor_enabled ? 'Aktif' : 'Nonaktif' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Security Information Card -->
            <div class="profile-card">
                <h3 class="profile-card-title">
                    <i class="fas fa-lock profile-card-icon"></i>
                    Keamanan
                </h3>
                
                <div class="profile-security-list">
                    <div class="security-item">
                        <span class="security-label">Percobaan Login</span>
                        <span class="security-value">{{ Auth::user()->login_attempts }}</span>
                    </div>
                    
                    <div class="security-item">
                        <span class="security-label">Login Gagal</span>
                        <span class="security-value {{ Auth::user()->failed_login_attempts > 0 ? 'warning' : '' }}">
                            {{ Auth::user()->failed_login_attempts }}
                        </span>
                    </div>
                    
                    @if(Auth::user()->locked_until)
                        <div class="security-item locked">
                            <span class="security-label">
                                <i class="fas fa-exclamation-triangle"></i> Akun Dikunci
                            </span>
                            <span class="security-value">
                                Hingga {{ Auth::user()->locked_until->format('d M Y, H:i') }}
                            </span>
                        </div>
                    @endif
                    
                    @if(Auth::user()->current_session_id)
                        <div class="security-item active">
                            <span class="security-label">
                                <i class="fas fa-check-circle"></i> Sesi Aktif
                            </span>
                            <span class="security-value">Tersedia</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Activity Timeline Card -->
            <div class="profile-card">
                <h3 class="profile-card-title">
                    <i class="fas fa-history profile-card-icon"></i>
                    Aktivitas Terakhir
                </h3>
                
                <div class="profile-timeline">
                    <div class="timeline-item">
                        <div class="timeline-icon login">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="timeline-content">
                            <h4>Login Terakhir</h4>
                            <p class="timeline-date">
                                {{ Auth::user()->last_login_at ? Auth::user()->last_login_at->format('d M Y, H:i') : 'Tidak pernah login' }}
                            </p>
                            @if(Auth::user()->last_login_ip)
                                <p class="timeline-detail">IP: {{ Auth::user()->last_login_ip }}</p>
                            @endif
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-icon activity">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="timeline-content">
                            <h4>Aktivitas Terakhir</h4>
                            <p class="timeline-date">
                                {{ Auth::user()->last_activity ? Auth::user()->last_activity->format('d M Y, H:i') : 'Tidak ada data' }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-icon password">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="timeline-content">
                            <h4>Password Terakhir Diubah</h4>
                            <p class="timeline-date">
                                {{ Auth::user()->password_changed_at ? Auth::user()->password_changed_at->format('d M Y, H:i') : 'Tidak ada data' }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-icon backup">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="timeline-content">
                            <h4>Backup Terakhir</h4>
                            <p class="timeline-date">
                                {{ Auth::user()->last_backup_at ? Auth::user()->last_backup_at->format('d M Y, H:i') : 'Belum pernah backup' }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons Card -->
            <div class="profile-card">
                <h3 class="profile-card-title">
                    <i class="fas fa-tools profile-card-icon"></i>
                    Tindakan
                </h3>
                  <div class="profile-actions">
                    <a href="{{ route('profile.edit') }}" class="profile-action-btn primary">
                        <i class="fas fa-edit"></i> Edit Profil
                    </a>
                    <a href="{{ route('password.change.form') }}" class="profile-action-btn secondary">
                        <i class="fas fa-lock"></i> Ubah Password
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fungsi untuk mengupdate display tema
    function updateThemeDisplay(theme) {
        const profileThemeDisplay = document.getElementById('profileThemeDisplay');
        
        if (profileThemeDisplay) {
            let themeHTML = '';
            
            switch(theme) {
                case 'light':
                    themeHTML = '<i class="fas fa-sun"></i> Terang';
                    break;
                case 'solarized-dark':
                    themeHTML = '<i class="fas fa-moon"></i> Solarized Dark';
                    break;
                case 'synth-wave':
                    themeHTML = '<i class="fas fa-city"></i> Synth Wave';
                    break;
                default:
                    themeHTML = '<i class="fas fa-sun"></i> Terang';
            }
            
            profileThemeDisplay.innerHTML = themeHTML;
            
            // Tambahkan efek animasi untuk menunjukkan perubahan
            profileThemeDisplay.style.transform = 'scale(1.05)';
            profileThemeDisplay.style.transition = 'transform 0.3s ease';
            
            setTimeout(() => {
                profileThemeDisplay.style.transform = 'scale(1)';
            }, 300);
        }
    }
    
    // Jika Pusher tersedia, gunakan untuk real-time updates
    @if(config('broadcasting.default') === 'pusher')
    if (typeof Pusher !== 'undefined') {
        const pusher = new Pusher('{{ config('broadcasting.connections.pusher.key') }}', {
            cluster: '{{ config('broadcasting.connections.pusher.options.cluster') }}',
            encrypted: true,
            auth: {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            },
            authEndpoint: '/broadcasting/auth'
        });
        
        // Subscribe ke channel theme updates untuk user ini
        const channel = pusher.subscribe('private-user.{{ Auth::id() }}.theme');
        
        // Listen untuk theme change events
        channel.bind('theme.changed', function(data) {
            console.log('Theme changed via Pusher:', data.theme);
            updateThemeDisplay(data.theme);
            
            // Tampilkan notifikasi real-time
            if (typeof Swal !== 'undefined') {
                const successToast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 2000,
                    timerProgressBar: true
                });
                
                successToast.fire({
                    icon: 'success',
                    title: '🎨 Tema berubah secara real-time!',
                    text: `Tema telah diperbarui ke ${data.theme_display}`
                });
            }
        });
        
        console.log('Pusher theme channel subscribed successfully');
    }
    @endif
    
    // Fallback: Listen untuk storage events (jika tema diubah di tab lain)
    window.addEventListener('storage', function(e) {
        if (e.key === 'theme') {
            console.log('Theme changed via localStorage:', e.newValue);
            updateThemeDisplay(e.newValue);
        }
    });
    
    // Listen untuk custom events dari theme switcher
    window.addEventListener('themeChanged', function(e) {
        console.log('Theme changed via custom event:', e.detail.theme);
        updateThemeDisplay(e.detail.theme);
    });
    
    // Observasi perubahan pada document body data-theme attribute
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                const newTheme = document.body.getAttribute('data-theme');
                console.log('Theme changed via body attribute:', newTheme);
                updateThemeDisplay(newTheme);
            }
        });
    });
    
    // Mulai observasi
    observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['data-theme']
    });
});
</script>
@endpush
@endsection
